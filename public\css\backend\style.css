@charset "UTF-8";
body {
  font-family: "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック体", "Yu Gothic", Yu<PERSON><PERSON><PERSON>, "メイリオ", Meiryo, Osaka, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif;
  margin: 0;
  padding: 0;
  color: #111111;
  font-size: 13px;
  position: relative;
}

/* =============================================
 *  01. Common settings
 *      - reset
 *      - common
 *      - padding
 *      - margin
 *      - width
 *  02. Layout
 *      - common
 *      - float
 *      - clearfix
 *      - align
 *  03. Font
 *      - font size
 *      - font color
 *      - font family
 *      - contact
 *  04. Parts
 *      - icon
 *      - button
 *      - table
 *      - heading
 *      - pagination
 *  05. Form
 *      - layout
 *      - parts
 *  06. Dialog
 * -----------------------------------
 *  11. Header
 *  12. Navigation
 *  13. Aside
 *  14. Footer
 * -----------------------------------
 *  20. Calender setting
 * -----------------------------------
 *  31. Login
 * ============================================= */
/* ---------------------------------------------------------------------------------------- */
/* ==============================================
 01. Common settings
=============================================== */
/* reset
----------------------------------------------- */
html, body, div, span, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
abbr, address, cite, code,
del, dfn, em, img, ins, kbd, q, samp,
small, strong, sub, sup, var,
b, i,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, figcaption, figure,
footer, header, hgroup, menu, nav, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  vertical-align: bottom;
}

article, aside, details, figcaption, figure, main,
footer, header, hgroup, menu, nav, section {
  display: block;
}

nav ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}

a {
  margin: 0;
  padding: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
}

/* change colours to suit your needs */
ins {
  background-color: #ff9;
  color: #000;
  text-decoration: none;
}

/* change colours to suit your needs */
mark {
  background-color: #ff9;
  color: #000;
  font-style: italic;
  font-weight: bold;
}

del {
  text-decoration: line-through;
}

abbr[title], dfn[title] {
  border-bottom: 1px dotted;
  cursor: help;
}

table {
  border-collapse: separate;
  border-spacing: 0;
}

/* change border colour to suit your needs */
hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #cccccc;
  margin: 1em 0;
  padding: 0;
}

input, select {
  vertical-align: middle;
}

span {
  vertical-align: baseline;
}

/* common
----------------------------------------------- */
*, *:before, *:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}

html {
  font-size: 62.5%;
  overflow-y: scroll;
  margin: 0;
  width: 100%;
}

body {
  background: #EAF2FF;
  color: #111111;
  font-family: "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック体", "Yu Gothic", YuGothic, "メイリオ", Meiryo, Osaka, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif;
  font-size: 14px;
  font-size: 1.4rem;
  font-weight: normal;
  font-weight: 400;
  letter-spacing: 0;
  margin: 0;
  padding: 0;
  -webkit-text-size-adjust: 100%;
  width: 100%;
}

@media (max-width: 767px) {
  body {
    font-size: 13px;
    font-size: 1.3rem;
  }
}

.preload * {
  -webkit-transition: none 0.14s ease-out;
  -moz-transition: none 0.14s ease-out;
  -ms-transition: none 0.14s ease-out;
  -o-transition: none 0.14s ease-out;
  transition: none 0.14s ease-out;
}

a {
  color: #111111;
  outline: medium none;
  text-decoration: none;
}

a:visited {
  outline: medium none;
}

a:focus {
  outline: medium none;
}

a:active, a:hover {
  outline: medium none;
}

a:hover {
  color: #111111;
  text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
  clear: both;
  font-weight: bold;
  font-weight: 600;
  margin: 0;
}

address {
  font-style: italic;
  margin: 0 0 24px;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b, strong {
  font-weight: bold;
}

dfn {
  font-style: italic;
}

mark {
  background: none repeat scroll 0 0 #FFFF00;
  color: #000000;
}

p {
  line-height: 1.6;
  margin: 0 0 24px;
  max-height: 100%;
}

code, kbd, pre, samp {
  -moz-hyphens: none;
  font-family: monospace,serif;
  font-size: 14px;
}

pre {
  background: none repeat scroll 0 0 #F5F5F5;
  color: #666666;
  font-family: monospace;
  font-size: 14px;
  margin: 20px 0;
  overflow: auto;
  padding: 20px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

blockquote, q {
  -moz-hyphens: none;
  quotes: none;
}

blockquote:before, blockquote:after, q:before, q:after {
  content: none;
}

blockquote {
  font-size: 18px;
  font-style: italic;
  font-weight: 300;
  margin: 24px 40px;
}

blockquote blockquote {
  margin-right: 0;
}

blockquote cite, blockquote small {
  font-size: 14px;
  font-weight: normal;
  text-transform: uppercase;
}

blockquote em, blockquote i {
  font-style: normal;
  font-weight: 300;
}

blockquote strong, blockquote b {
  font-weight: 400;
}

small {
  font-size: 13px;
  font-size: 1.3rem;
}

sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

dl {
  margin: 0;
}

dt {
  line-height: 1.8;
  font-weight: bold;
  font-weight: 600;
  margin: 0;
}

dd {
  line-height: 1.8;
  margin: 0;
}

menu, ol, ul {
  margin: 0;
  padding: 0;
}

ul {
  list-style-type: none;
}

li {
  line-height: 1.8;
}

th {
  font-weight: bold;
}

img {
  border: 0 none;
  height: auto;
  max-width: 100%;
  vertical-align: middle;
  -webkit-backface-visibility: hidden;
}

input[type="text"]:focus,
textarea:focus {
  outline: 0;
}

a[href^="tel:"] {
  cursor: default;
  pointer-events: none;
}

/* ==============================================
 02. Layout
=============================================== */
/* Common
----------------------------------------------- */
.content {
  padding-bottom: 50px;
}

.container {
  padding-right: 25px;
  padding-left: 25px;
  max-width: 1180px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}

@media (max-width: 767px) {
  .container {
    padding-left: 12px;
    padding-right: 12px;
  }
}

.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

.flex-wrap {
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.align-items-center {
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
}

.justify-content-center {
  -webkit-justify-content: center;
  -moz-justify-content: center;
  -ms-justify-content: center;
  justify-content: center;
}

.flex-root {
  -webkit-flex: 1;
  flex: 1;
}

.flex-column {
  flex-direction: column;
}

.flex-column-fluid {
  -webkit-flex: 1 0 auto;
  flex: 1 0 auto;
}

.flex-row {
  flex-direction: row;
}

.flex-row-fluid {
  -webkit-flex: 1 auto;
  flex: 1 auto;
  min-width: 0;
}

.justify-content-end {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.flex-shrink-0 {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

/* float
----------------------------------------------- */
.fl-left {
  float: left;
}

.fl-right {
  float: right;
}

/* clearfix
----------------------------------------------- */
.cf:before,
.cf:after {
  content: " ";
  display: table;
}

.cf:after {
  clear: both;
}

.cf {
  *zoom: 1;
}

/* align
----------------------------------------------- */
.align-top {
  vertical-align: top;
}

.align-bottom {
  vertical-align: bottom;
}

.align-middle {
  vertical-align: middle;
}

.align-baseline {
  vertical-align: baseline;
}

/* card
----------------------------------------------- */
.card {
  background-color: #fff;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  position: relative;
  min-width: 0;
}

.card .card-header {
  padding: 30px 30px 0;
}

@media (max-width: 767px) {
  .card .card-header {
    padding: 15px 15px 0;
  }
}

.card .card-header .btn-primary {
  min-width: 160px;
}

@media (max-width: 767px) {
  .card .card-header .btn-primary {
    min-width: 120px;
  }
}

.card .card-body {
  padding: 30px 30px 30px;
}

@media (max-width: 767px) {
  .card .card-body {
    padding: 15px 15px;
  }
}

/* ==============================================
 03. Font
=============================================== */
/* Font size
----------------------------------------------- */
.fs-12 {
  font-size: 12px;
}

.fs-13 {
  font-size: 13px;
}

.fs-14 {
  font-size: 14px;
}

.fs-15 {
  font-size: 15px;
}

.fs-16 {
  font-size: 14px;
}

.fs-17 {
  font-size: 17px;
}

.fs-18 {
  font-size: 18px;
}

.fs-19 {
  font-size: 19px;
}

.fs-20 {
  font-size: 20px;
}

.fs-asterisk {
  font-size: 120%;
  display: inline-block;
  line-height: 1;
}

/* Font color
----------------------------------------------- */
.fc-red {
  color: #C90303;
}

.fc-blue {
  color: #508FF4;
}

.fc-mute {
  color: #777777;
}

/* Font family
----------------------------------------------- */
.ff-serif {
  font-family: "游明朝", YuMincho, "ヒラギノ明朝 ProN W3", "Hiragino Mincho ProN", "HG明朝E", "ＭＳ Ｐ明朝", "ＭＳ 明朝", serif;
}

.ff-en {
  font-family: "Roboto", sans-serif;
}

/* Text align
----------------------------------------------- */
.ta-center {
  text-align: center;
}

.ta-left {
  text-align: left;
}

.ta-right {
  text-align: right;
}

/* Text indent
----------------------------------------------- */
.indent-1 {
  text-indent: 1em;
}

/* ==============================================
 04. Parts
=============================================== */
/* icon
----------------------------------------------- */
.svg-icon svg [fill]:not(.permanent):not(g) {
  -webkit-transition: all 0.14s ease-out;
  -moz-transition: all 0.14s ease-out;
  -ms-transition: all 0.14s ease-out;
  -o-transition: all 0.14s ease-out;
  transition: all 0.14s ease-out;
  fill: #a5a4bf;
}

/* button
----------------------------------------------- */
.btn {
  display: inline-block;
  font-size: 14px;
  line-height: 1.5;
  padding: 9px 30px;
  text-align: center;
  white-space: nowrap;
  max-width: 100%;
  -webkit-border-radius: 0.475rem;
  -moz-border-radius: 0.475rem;
  -ms-border-radius: 0.475rem;
  border-radius: 0.475rem;
  -webkit-transition: all 0.14s ease-out;
  -moz-transition: all 0.14s ease-out;
  -ms-transition: all 0.14s ease-out;
  -o-transition: all 0.14s ease-out;
  transition: all 0.14s ease-out;
    border: 0;
}

.btn:not(.disabled):not(.btn-text):hover {
  transform: translateY(-1px);
  -webkit-box-shadow: 0 4px 8px rgba(33, 33, 43, 0.08), 0 3px 6px rgba(33, 33, 43, 0.06);
  -moz-box-shadow: 0 4px 8px rgba(33, 33, 43, 0.08), 0 3px 6px rgba(33, 33, 43, 0.06);
  -ms-box-shadow: 0 4px 8px rgba(33, 33, 43, 0.08), 0 3px 6px rgba(33, 33, 43, 0.06);
  box-shadow: 0 4px 8px rgba(33, 33, 43, 0.08), 0 3px 6px rgba(33, 33, 43, 0.06);
}

.btn:focus, .btn:active:focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn.active.focus {
    outline: 0;
}

.btn-destroy:hover {
    box-shadow: none !important;
}

.btn.btn-lg {
  width: 200px;
}

@media (max-width: 767px) {
  .btn.btn-lg {
    width: 120px;
  }
}

.btn.btn-sm {
  padding: 9px 15px;
}

.btn.btn-h-sm {
  padding: 7px 30px;
}

@media (max-width: 767px) {
  .btn {
    font-size: 13px;
    padding: 9px 16px;
  }
}

.btn.disabled {
  cursor: default;
}

.btn-primary {
  color: #fff;
  background: #508FF4;
}

.btn-primary:hover {
  color: #fff;
  background: #508FF4;
}

.btn-primary.disabled {
  background: #A7A7A7;
}

.btn-secondary {
  background: #8E8E8E;
  color: #fff;
}

.btn-secondary:hover {
  background: #8E8E8E;
  color: #fff;
}

.btn-more {
  background-color: #fff;
  border: 1px solid #ccc;
  color: #888888;
  width: 180px;
}

@media (max-width: 767px) {
  .btn-more {
    width: 120px;
  }
}

.btn-text {
  background: none;
  color: #111111;
  padding: 10px 15px;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
}

.btn-text.disabled {
  color: #777777;
  text-decoration: none;
}

.btn-text:hover {
  background: none;
}

@media (max-width: 767px) {
  .btn-text {
    font-size: 13px;
    padding: 7px 0;
  }
}

.btn-text-underline {
  text-decoration: underline;
}

@media (min-width: 1121px) {
  .btn-text-underline:hover {
    text-decoration: none;
  }
}

.btn-icon {
  background: none;
  padding: 0;
  min-width: 40px;
}

.btn-icon:hover {
  transform: translateY(0) !important;
  -webkit-box-shadow: none !important;
  -moz-box-shadow: none !important;
  -ms-box-shadow: none !important;
  box-shadow: none !important;
}

.btn-add {
  background-color: #111111;
  height: 40px;
  position: relative;
  padding: 0;
  min-width: 40px;
}

.btn-add::before, .btn-add::after {
  content: "";
  background-color: #fff;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.btn-add::before {
  height: 2px;
  width: 16px;
}

.btn-add::after {
  height: 16px;
  width: 2px;
}

.prev-page-btn .btn {
  background-color: #fff;
  border: 2px solid #508FF4;
  color: #508FF4;
  font-size: 116%;
  font-weight: bold;
  padding: 7px 24px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  border-radius: 50px;
}

.prev-page-btn .btn .icon-arrow {
  background: url("img/icon_back.svg") no-repeat 0 50%;
  background-size: 22px 22px;
  display: inline-block;
  padding: 3px 0 3px 30px;
  white-space: nowrap;
}

/* table
----------------------------------------------- */
.table {
  border-bottom: 1px solid #C0C0C0;
  width: 100%;
}

.table th, .table td {
  font-weight: normal;
  text-align: left;
  padding: 0.75rem 1.75rem;
  white-space: nowrap;
}

@media (max-width: 767px) {
  .table th, .table td {
    padding: 0.75rem 1.25rem;
  }
}

.table th.row-wrap-normal, .table td.row-wrap-normal {
  white-space: normal;
}

.table th p, .table td p {
  margin-bottom: 0;
}

.table.align-middle th, .table.align-middle td {
  vertical-align: middle;
}

.table.table-row-dashed {
  border-collapse: collapse;
}

.table.table-row-dashed tr:not(:last-child) {
  border-bottom: 1px dashed #C0C0C0;
}

.table thead th {
  background-color: #B7B7B7;
  color: #fff;
  padding: .5rem 1.75rem;
  vertical-align: middle;
}

@media (max-width: 767px) {
  .table thead th {
    padding: .5rem 1.25rem;
  }
}

.table tbody tr:nth-child(odd) {
  background-color: #F5F5F5;
}

.table tbody th, .table tbody td {
  border-top: 1px solid #C0C0C0;
}

.responsive-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 1120px) {
  .responsive-container-tab {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

@media (max-width: 767px) {
  .responsive-container-sp {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* page title
----------------------------------------------- */
.page-title {
  margin-bottom: 3rem;
}

@media (max-width: 767px) {
  .page-title {
    margin-bottom: 2rem;
  }
}

.page-title a:not(.btn) {
  color: #508FF4;
  text-decoration: underline;
}

@media (min-width: 1121px) {
  .page-title a:not(.btn):hover {
    text-decoration: none;
  }
}

.page-title .btn-primary {
  color: #fff;
  min-width: 160px;
}

@media (max-width: 767px) {
  .page-title .btn-primary {
    min-width: none;
  }
}

/* heading
----------------------------------------------- */
.heading-1 {
  border-left: 3px solid #508FF4;
  color: #508FF4;
  font-size: 20px;
  font-weight: bold;
  line-height: 1.25;
  padding-left: 1.75rem;
  position: relative;
  white-space: nowrap;
}

@media (max-width: 767px) {
  .heading-1 {
    font-size: 18px;
    padding-left: 1.25rem;
  }
}

.heading-1 .form-required {
  color: #C90303;
  display: inline-block;
  line-height: 1;
  margin-left: 0.5rem;
  font-size: 80%;
  vertical-align: top;
}

.heading-2 {
  font-size: 16px;
  line-height: 1.4;
}

/* title
----------------------------------------------- */
.post-block-title {
  margin-bottom: 16px;
  margin-top: 44px;
}

/* scrollbox
----------------------------------------------- */
.scroll-area {
  overflow: auto;
  position: relative;
}

.scroll-area.ps--active-x {
  padding-bottom: 10px;
}

.scroll-area.ps--active-x .ps__rail-x {
  opacity: 1;
}

.scroll-area.ps--active-x .ps__rail-x .ps__thumb-x {
  height: 10px;
  height: 4px;
}

/* pagination
----------------------------------------------- */
.pagination {
  font-size: 0;
  letter-spacing: 0;
}

.pagination .page-item {
  font-size: 14px;
  display: inline-block;
  margin: 0 4px;
  vertical-align: middle;
}

@media (max-width: 767px) {
  .pagination .page-item {
    font-size: 12px;
  }
}

.pagination .page-item:first-child {
  margin-left: auto;
}

.pagination .page-item:last-child {
  margin-right: auto;
}

.pagination .page-item a, .pagination .page-item span {
  color: #777777;
  display: inline-block;
  padding: 0;
  height: 32px;
  line-height: 32px;
  vertical-align: middle;
}

@media (max-width: 767px) {
  .pagination .page-item a, .pagination .page-item span {
    height: 30px;
    line-height: 30px;
  }
}

.pagination .page-item .current-page {
  border: 1px solid #6F6F6F;
  color: #777777;
  height: 32px;
  line-height: 32px;
  text-align: center;
  padding: 0 10px;
}

.total-page {
    border: 0px !important;
}

@media (max-width: 767px) {
  .pagination .page-item .current-page {
    height: 30px;
    line-height: 30px;
  }
}

.pagination .page-item .page-link {
  background-color: #6F6F6F;
  height: 32px;
  text-indent: -9999px;
  overflow: hidden;
  position: relative;
  width: 32px;
}

@media (max-width: 767px) {
  .pagination .page-item .page-link {
    height: 30px;
    width: 30px;
  }
}

.pagination .page-item .page-link::after {
  content: "";
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

.pagination .page-item .page-prev {
  margin-right: 0.25rem;
    border-radius: 0;
}

.pagination .page-item .page-input {
    width: 3.25rem;
    text-align: center;
    border-radius: 0;
}

.pagination .page-item .page-prev::after {
  left: 14px;
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -5px;
  width: 10px;
  height: 10px;
  border-bottom: 1px solid #fff;
  border-left: 1px solid #fff;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

.pagination .page-item .page-next {
  margin-left: 0.25rem;
    border-radius: 0;
}

.pagination .page-item .page-next::after {
  right: 14px;
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -5px;
  width: 10px;
  height: 10px;
  border-top: 1px solid #fff;
  border-right: 1px solid #fff;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

.page-controls {
  font-size: 14px;
}

@media (max-width: 767px) {
  .page-controls {
    font-size: 12px;
  }
}

/* dropdown
----------------------------------------------- */
.dropdown {
  position: relative;
}

.dropdown .dropdown-content {
  background: #fff;
  top: 0;
  left: 50%;
  z-index: 1000;
  display: none;
  width: 20rem;
  padding: 0;
  margin: .55rem 0 0;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  border-radius: 3px;
  -webkit-box-shadow: 0 1px 5px 1px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 1px 5px 1px rgba(0, 0, 0, 0.2);
  -ms-box-shadow: 0 1px 5px 1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 5px 1px rgba(0, 0, 0, 0.2);
}

.dropdown[aria-opened="true"] .dropdown-content {
  display: block;
}

.dropdown .dropdown-contain {
  position: relative;
}

.dropdown .dropdown-contain::after {
  position: absolute;
  display: block;
  content: '';
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 10px solid #fff;
  top: -20px;
  right: 50%;
  margin-right: -8px;
  pointer-events: none;
}

.dropdown .dropdown-title {
  font-size: 13px;
  line-height: 1;
  padding: 12px 12px;
  border-bottom: 1px solid #C0C0C0;
}

.dropdown .dropdown-list {
  max-height: 200px;
  padding: 0.75rem 0;
  overflow: auto;
}

.dropdown .dropdown-list a {
  display: block;
  font-size: 13px;
  line-height: 1.45;
  padding: 6px 14px 6px 8px;
}

@media (min-width: 1121px) {
  .dropdown .dropdown-list a:hover {
    color: #508FF4;
  }
}

.dropdown .scroll-box .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: rgba(17, 17, 17, 0.55);
}

.dropdown .link-all {
  background-color: #EAF2FF;
  display: block;
  font-size: 11px;
  text-align: center;
  padding: 1rem;
  text-decoration: underline;
  -webkit-border-radius: 0 0 3px 3px;
  -moz-border-radius: 0 0 3px 3px;
  -ms-border-radius: 0 0 3px 3px;
  border-radius: 0 0 3px 3px;
}

@media (min-width: 1121px) {
  .dropdown .link-all:hover {
    text-decoration: none;
  }
}

/* ==============================================
 05. Form
=============================================== */
/* layout
----------------------------------------------- */
.form-inline {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
}

.form-primary h3 {
  font-size: 18px;
  color: #508FF4;
  border-left: 3px solid #508FF4;
  line-height: 1.4;
  padding: 0 0 0 1.25rem;
  margin-bottom: 1.75rem;
  margin-top: 3.2rem;
}

@media (max-width: 767px) {
  .form-primary h3 {
    font-size: 16px;
    margin-top: 4rem;
  }
}

.form-primary h3 a {
  font-size: 14px;
  font-weight: normal;
  color: #508FF4;
  display: inline-block;
  line-height: 1;
  text-decoration: underline;
  white-space: nowrap;
}

@media (min-width: 1121px) {
  .form-primary h3 a:hover {
    text-decoration: none;
  }
}

.form-primary .form-block {
  padding-bottom: 3rem;
}

.form-primary .form-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
}

@media (max-width: 1120px) {
  .form-primary .form-group {
    display: block;
  }
}

.form-primary .form-group.form-group-align-top {
  -webkit-align-items: normal;
  -moz-align-items: normal;
  -ms-align-items: normal;
  align-items: normal;
}

.form-primary .form-group.form-group-align-top .form-label {
  margin-top: 1rem;
}

@media (max-width: 1120px) {
  .form-primary .form-group.form-group-align-top .form-label {
    margin-top: 0;
  }
}

.form-primary .form-group .form-required {
  color: #C90303;
  display: inline-block;
  line-height: 1;
  margin-left: 0.5rem;
  font-size: 115%;
}

.form-primary .form-group .form-label {
  margin-bottom: 1.5rem;
  margin-right: 1.5rem;
  min-width: 160px;
}

@media (max-width: 1120px) {
  .form-primary .form-group .form-label {
    margin-right: auto;
    margin-bottom: 0.75rem;
    margin-top: 1.25rem;
  }
}

.form-primary .form-group .form-field {
  flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field .flex {
    min-width: 0px;
  }
  .form-primary .form-group .form-field .flex .form-size-w380 {
    flex: 1;
    width: 100%;
  }
}

.form-primary .form-group .form-field > * {
  margin-bottom: 1.5rem;
}

.form-primary .form-group .form-field .form-full-block {
  width: 100%;
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field .form-sp-full-block {
    width: 100%;
  }
}

.form-primary .form-group .form-field .form-control-text-large {
  max-width: 100%;
  width: 820px;
}

.form-primary .form-group .form-field .form-control-text-full {
  flex: 1;
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field .form-control-text-full {
    flex: initial;
    width: 100%;
  }
}

.form-primary .form-group .form-field .form-field-label {
  color: #777777;
  line-height: 1.35;
  white-space: nowrap;
}

.form-primary .form-group .form-field .form-field-label.form-field-label--dark {
  color: #111111;
}

.form-primary .form-group .form-field .form-select select {
  min-width: 120px;
}

.form-primary .form-group .form-field .form-size-minw4em {
  min-width: 4em;
}

.form-primary .form-group .form-field .form-size-minw6em {
  min-width: 6em;
}

.form-primary .form-group .form-field .form-size-minw12em {
  min-width: 12em;
}

.form-primary .form-group .form-field .form-size-w140 {
  max-width: 100%;
  width: 140px;
}

.form-primary .form-group .form-field .form-size-w200 {
  max-width: 100%;
  width: 200px;
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field .form-size-w200 {
    flex: 1;
    width: auto;
  }
}

.form-primary .form-group .form-field .form-size-w220 {
  max-width: 100%;
  width: 220px;
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field .form-size-w220 {
    flex: 1;
    width: auto;
  }
}

.form-primary .form-group .form-field .form-size-w280 {
  max-width: 100%;
  width: 280px;
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field .form-size-w280 {
    width: 220px;
  }
}

.form-primary .form-group .form-field .form-size-w380 {
  max-width: 100%;
  width: 380px;
}

.form-primary .form-group .form-field .form-size-w520 {
  max-width: 100%;
  width: 520px;
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field .form-sp-full {
    flex: none;
    width: 100%;
  }
}

.form-primary .form-group .form-field .form-add-button {
  background-color: #111111;
  height: 40px;
  position: relative;
  padding: 0;
  min-width: 40px;
}

.form-primary .form-group .form-field .form-add-button::before, .form-primary .form-group .form-field .form-add-button::after {
  content: "";
  background-color: #fff;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.form-primary .form-group .form-field .form-add-button::before {
  height: 2px;
  width: 20px;
}

.form-primary .form-group .form-field .form-add-button::after {
  height: 20px;
  width: 2px;
}

.form-primary .form-group .form-field pre {
  margin: 0 auto;
  padding: 12px 12px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
}

.form-primary .form-group .form-field.form-field-password input {
  margin-right: 2rem;
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field.form-field-password input {
    margin-right: auto;
    flex: none;
    width: 100%;
  }
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field.form-field-password .btn {
    margin-left: auto;
    width: 160px;
  }
}

.form-primary .form-group .form-field.form-disabled-control-field .form-select select[disabled="disabled"] {
  background-color: #f5f5f5;
}

.form-primary .form-group .form-field.form-field-weekly .form-select select {
  width: 200px;
}

.form-primary .form-group .form-field.form-field-weekly .form-checkbox .form-checkbox-label .form-checkbox-text {
  height: 22px;
  padding-left: 0;
  width: 22px;
  vertical-align: middle;
}

.form-primary .form-group .form-field.form-field-interval .form-select select {
  width: 200px;
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field.form-field-interval .form-select select {
    width: auto;
  }
}

.form-primary .form-group .form-field.form-field-startdate .form-select select {
  width: 200px;
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field.form-field-startdate .form-select select {
    width: auto;
  }
}

.form-primary .form-group .form-field.form-field-startdate .form-checkbox .form-checkbox-label .form-checkbox-text {
  height: 22px;
  padding-left: 0;
  width: 22px;
  vertical-align: middle;
}

.form-primary .form-group .form-field.form-field-reception .form-datepicker::before {
  content: none;
}

.form-primary .form-group .form-field.form-field-reception .form-datepicker::after {
  background: url("img/icon_calender_gray.svg") no-repeat 0 0;
  right: auto;
  left: 12px;
  margin-top: -10px;
  height: 20px;
  width: 20px;
}

.form-primary .form-group .form-field.form-field-reception .form-datepicker input {
  padding-right: 12px;
  padding-left: 45px;
  width: 160px;
}

.form-primary .form-group .form-field.form-field-reception .form-select::before {
  content: none;
}

.form-primary .form-group .form-field.form-field-reception .form-select::after {
  background: url("img/icon_clock_gray.svg") no-repeat 0 0;
  background-size: 20px 20px;
  right: auto;
  left: 12px;
  margin-top: -10px;
  top: 50%;
  border: none;
  height: 20px;
  width: 20px;
}

.form-primary .form-group .form-field.form-field-reception .form-select select {
  padding: 10px 12px 10px 45px;
  width: 160px;
}

@media (max-width: 767px) {
  .form-primary .form-group .form-field.form-field-reception .form-select select {
    width: auto;
  }
}

.form-primary textarea.form-control:read-only {
  background-color: #F0F0F0;
}

.form-primary textarea.form-control:read-only:focus {
  border-color: #6F6F6F;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  -ms-box-shadow: none;
  box-shadow: none;
}

.form-primary .form-submit-buttons .btn-submit {
  border: 2px solid #508FF4;
  width: 180px;
}

@media (max-width: 767px) {
  .form-primary .form-submit-buttons .btn-submit {
    margin-left: auto;
    width: 160px;
  }
}

.form-horizon-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.form-horizon-list li {
  margin-right: 4rem;
  width: auto;
  white-space: nowrap;
}

.form-horizon-list li:last-child {
  margin-right: auto;
}

.form-vertical-list li {
  margin-bottom: 2rem;
}

.form-vertical-list li:last-child {
  margin-bottom: auto;
}

/* parts
----------------------------------------------- */
button {
  background-color: transparent;
  border: none;
  font-size: 100%;
  cursor: pointer;
  outline: none;
  padding: 0;
  -webkit-appearance: none;
  appearance: none;
}

/* form default */
input.form-control, input[type="number"].form-control, input[type="email"].form-control, button.form-control, textarea.form-control {
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  padding: 0;
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

@media (max-width: 767px) {
  input.form-control, input[type="number"].form-control, input[type="email"].form-control, button.form-control, textarea.form-control {
    font-size: 13px;
  }
}

input[type="submit"],
input[type="button"],
button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  outline: none;
  padding: 0;
  -webkit-appearance: none;
  appearance: none;
  -webkit-box-sizing: content-box;
  -webkit-appearance: button;
  appearance: button;
  border: none;
  box-sizing: border-box;
  cursor: pointer;
}

input[type="submit"]::-webkit-search-decoration,
input[type="button"]::-webkit-search-decoration {
  display: none;
}

input[type="submit"]:focus,
input[type="button"]:focus,
button:focus {
  outline-offset: -2px;
  outline: 0;
}

input[type="text"].form-control, input[type="number"].form-control, input[type="password"].form-control, input[type="email"].form-control, input[type="tel"].form-control, input[type="url"].form-control, textarea.form-control {
  background-color: #fff;
  border: none;
  padding: 10px 12px;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  max-width: 100%;
  width: 100%;
}

input[type="text"].form-control[readonly="readonly"], input[type="number"].form-control[readonly="readonly"], input[type="password"].form-control[readonly="readonly"], input[type="email"].form-control[readonly="readonly"], input[type="tel"].form-control[readonly="readonly"], input[type="url"].form-control[readonly="readonly"], textarea.form-control[readonly="readonly"] {
  background-color: #F0F0F0;
}

input[type="text"].form-control[readonly="readonly"]:focus, input[type="number"].form-control[readonly="readonly"]:focus, input[type="password"].form-control[readonly="readonly"]:focus, input[type="email"].form-control[readonly="readonly"]:focus, input[type="tel"].form-control[readonly="readonly"]:focus, input[type="url"].form-control[readonly="readonly"]:focus, textarea.form-control[readonly="readonly"]:focus {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  -ms-box-shadow: none;
  box-shadow: none;
}

input[type="text"].form-control:focus, input[type="number"].form-control:focus, input[type="password"].form-control:focus, input[type="email"].form-control:focus, input[type="tel"].form-control:focus, input[type="url"].form-control:focus, textarea.form-control:focus {
  -webkit-box-shadow: 0 1px 3px rgba(80, 143, 244, 0.5) inset;
  -moz-box-shadow: 0 1px 3px rgba(80, 143, 244, 0.5) inset;
  -ms-box-shadow: 0 1px 3px rgba(80, 143, 244, 0.5) inset;
  box-shadow: 0 1px 3px rgba(80, 143, 244, 0.5) inset;
}

input[type="text"].form-zip {
  width: 140px;
}

/* password */
.form-input--password {
  position: relative;
}

.form-input--password::before {
  content: "";
  background: #fff url("img/icon_password01.png") no-repeat 0 0/15px 19px;
  height: 19px;
  left: 16px;
  position: absolute;
  top: 50%;
  margin-top: -9px;
  width: 15px;
  z-index: 1;
}

.form-input--password::after {
  background-color: #cccccc;
  content: "";
  height: 70%;
  position: absolute;
  left: 47px;
  top: 15%;
  width: 1px;
  z-index: 1;
}

.form-input--password input.form-control {
  padding-left: 60px;
}

/* checkbox */
.form-checkbox {
  display: inline-block;
  position: relative;
}

.form-checkbox input[type="checkbox"].form-checkbox-input {
  position: absolute;
  display: none;
  z-index: -1;
}

.form-checkbox .form-checkbox-text {
  display: inline-block;
  position: relative;
  padding-left: 30px;
  line-height: 1.6;
}

@media (max-width: 767px) {
  .form-checkbox .form-checkbox-text {
    padding-top: 1px;
  }
}

.form-checkbox .form-checkbox-text::before {
  content: "";
  background-color: #fff;
  border: 1px solid #C0C0C0;
  height: 22px;
  position: absolute;
  left: 0;
  top: 0;
  width: 22px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  border-radius: 3px;
}

.form-checkbox input[type="checkbox"].form-checkbox-input:checked + .form-checkbox-text::before {
  background-color: #508FF4;
  border: 2px solid #508FF4;
}

.form-checkbox input[type="checkbox"].form-checkbox-input:checked + .form-checkbox-text::after {
  content: "";
  position: absolute;
  left: 4px;
  top: 5px;
  width: 1em;
  height: 0.5em;
  border-bottom: 0.2em solid #fff;
  border-left: 0.2em solid #fff;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.form-checkbox.form-checkbox-no-text .form-checkbox-label {
  display: block;
}

.form-checkbox.form-checkbox-no-text .form-checkbox-text {
  padding-left: 0;
  height: 22px;
  width: 22px;
  display: block;
}

/* radio */
.form-radio {
  display: inline-block;
  position: relative;
}

.form-radio input[type="radio"].form-radio-input {
  position: absolute;
  display: none;
  z-index: -1;
}

.form-radio .form-radio-text {
  display: inline-block;
  position: relative;
  padding-left: 30px;
  line-height: 1.6;
}

@media (max-width: 767px) {
  .form-radio .form-radio-text {
    padding-top: 1px;
  }
}

.form-radio .form-radio-text::before {
  content: "";
  background-color: #fff;
  border: 2px solid #508FF4;
  height: 22px;
  position: absolute;
  left: 0;
  top: 0;
  width: 22px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}

.form-radio input[type="radio"].form-radio-input:checked + .form-radio-text::before {
  background-color: #508FF4;
  border: 2px solid #508FF4;
}

.form-radio input[type="radio"].form-radio-input:checked + .form-radio-text::after {
  background-color: #fff;
  content: "";
  position: absolute;
  left: 6px;
  top: 6px;
  width: 10px;
  height: 10px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}

/* selectbox */
.form-select {
  display: block;
  position: relative;
  width: auto;
  font-size: 14px;
  border: none;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  background: #fff;
  overflow: hidden;
}

@media (max-width: 767px) {
  .form-select {
    font-size: 13px;
  }
}

.form-select::before {
  background-color: #7C7C7C;
  content: "";
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 40px;
}

.form-select::after {
  content: "";
  position: absolute;
  border-style: solid;
  border-width: 11px 8px 0 8px;
  border-color: #fff transparent transparent transparent;
  right: 12px;
  top: 50%;
  margin-top: -5px;
  z-index: 1;
}

.form-select select {
  color: #111111;
  background: none;
  width: 100%;
  font-size: 14px;
  padding: 10px 55px 10px 12px;
  border: none;
  position: relative;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  border-radius: 0;
  border: 0;
  line-height: 1.5;
  background: none transparent;
  vertical-align: middle;
  outline: inherit;
  position: relative;
  z-index: 2;
}

@media (max-width: 767px) {
  .form-select select {
    font-size: 13px;
  }
}

.form-select select.no-value {
  color: #777777;
}

.form-select select.w-280 {
  max-width: 100%;
  width: 280px;
}

.form-select select.form-control-auto {
  width: auto;
}

.form-select select:not(:target) {
  width: 120% \9;
}

.form-select .ui-multiselect {
  border: none;
  font-size: 14px;
  padding: 9px 50px 9px 10px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  z-index: 2;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
}

.form-select .ui-multiselect .ui-multiselect-open {
  display: none;
}

.form-select .ui-multiselect::before {
  background-color: #7C7C7C;
  content: "";
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 40px;
}

.form-select .ui-multiselect::after {
  content: "";
  position: absolute;
  border-style: solid;
  border-width: 10px 8px 0 8px;
  border-color: #fff transparent transparent transparent;
  right: 12px;
  top: 50%;
  margin-top: -5px;
  z-index: 1;
}

.form-select .ui-state-default {
  background-color: #fff;
}

.form-select .ui-state-active {
  background-color: #508FF4;
  color: #fff !important;
}

.form-select .no-value + .ui-multiselect {
  color: #777777;
}

.form-select .select2-container {
  background: none;
  width: 100%;
  font-size: 14px;
  padding: 9px 50px 9px 10px;
  border: none;
  position: relative;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  border-radius: 0;
  border: 0;
  line-height: 1.5;
  background: none transparent;
  vertical-align: middle;
  outline: inherit;
  position: relative;
  z-index: 2;
}

@media (max-width: 767px) {
  .form-select .select2-container {
    font-size: 13px;
  }
}

.form-select .select2-container .select2-selection {
  border: none;
  height: auto;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  border-radius: 0;
}

.form-select .select2-container .select2-selection__rendered {
  color: #111111;
  padding: 0;
  line-height: 1.5;
}

/* datepicker */
.form-datepicker {
  display: inline-block;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
}

.form-datepicker::before {
  content: "";
  background-color: #6F6F6F;
  position: absolute;
  top: 0;
  height: 100%;
  right: 0;
  bottom: 0;
  width: 40px;
}

.form-datepicker::after {
  background: url("img/icon_calender_white.svg") no-repeat 0 0;
  content: "";
  height: 15px;
  top: 50%;
  position: absolute;
  margin-top: -8px;
  right: 12px;
  width: 15px;
}

.form-datepicker input[type="text"].form-control {
  background: none;
  border-color: #6F6F6F;
  padding-right: 50px;
  width: 12em;
  max-width: 100%;
  position: relative;
  z-index: 1;
}

@media (max-width: 767px) {
  .form-datepicker input[type="text"].form-control {
    width: 40vw;
  }
}

.form-datepicker .picker {
  color: #111111;
  font-size: 15px;
  font-family: "Roboto", sans-serif;
}

.form-datepicker .picker .picker__year {
  font-size: 100%;
  font-style: normal;
  color: #111111;
}

.form-datepicker .picker .picker__nav--prev:before {
  border-right-color: #111111;
}

.form-datepicker .picker .picker__nav--next:before {
  border-left-color: #111111;
}

.form-datepicker .picker .picker__button--close:before {
  top: .2em;
}

.form-datepicker .picker .picker__button--clear, .form-datepicker .picker .picker__button--close, .form-datepicker .picker .picker__button--today {
  color: #111111;
}

.form-keyword {
  white-space: nowrap;
}

.form-keyword input[type="text"].form-control {
  border-color: #6F6F6F;
  border-right: none;
  width: 240px;
  -webkit-border-radius: 6px 0 0 6px;
  -moz-border-radius: 6px 0 0 6px;
  -ms-border-radius: 6px 0 0 6px;
  border-radius: 6px 0 0 6px;
}

.form-keyword button {
  background-color: #6F6F6F;
  border: 1px solid #6F6F6F;
  color: #fff;
  padding: 9px 10px;
  -webkit-border-radius: 0 6px 6px 0;
  -moz-border-radius: 0 6px 6px 0;
  -ms-border-radius: 0 6px 6px 0;
  border-radius: 0 6px 6px 0;
}

.form-note {
  margin: 0;
  line-height: 1.6;
  font-size: 12px;
}

.form-file {
  cursor: pointer;
  background-color: #fff;
  border: 1px solid #6F6F6F;
  max-width: 100%;
  position: relative;
  width: 100%;
  overflow: hidden;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
}

.form-file::after {
  background-color: #6F6F6F;
  content: "";
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 40px;
}

.form-file input[type="file"].form-control {
  display: none;
}

.form-file .form-file-text {
  display: block;
  padding: 9px 50px 9px 10px;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.form-file .form-file-text::before, .form-file .form-file-text::after {
  content: "";
  background-color: #fff;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 1;
}

.form-file .form-file-text::before {
  height: 2px;
  right: 10px;
  width: 18px;
}

.form-file .form-file-text::after {
  height: 18px;
  right: 18px;
  width: 2px;
}

/* placeholder */
input::placeholder, textarea::placeholder {
  font-family: "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック体", "Yu Gothic", YuGothic, "メイリオ", Meiryo, Osaka, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif;
  color: #aaa;
  opacity: 1;
}

/* IE */
input:-ms-input-placeholder, textarea:-ms-input-placeholder {
  font-family: "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック体", "Yu Gothic", YuGothic, "メイリオ", Meiryo, Osaka, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif;
  color: #aaa;
  opacity: 1;
}

/* Edge */
input::-ms-input-placeholder, textarea::-ms-input-placeholder {
  font-family: "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック体", "Yu Gothic", YuGothic, "メイリオ", Meiryo, Osaka, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif;
  color: #aaa;
  opacity: 1;
}

/* Chrome, Safari */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox, IE */
input[type="number"] {
  -moz-appearance: textfield;
}

/* Chrome autofill */
input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus, input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0px 1000px #fff inset !important;
}

/* ==============================================
 06. Dialog
=============================================== */
.custom-dialog {
  background-color: #EAF2FF;
  border: none !important;
  max-width: 90%;
  padding: 0;
  z-index: 1001 !important;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
}

.custom-dialog .ui-widget-content {
  background: none;
}

.custom-dialog .ui-widget-header {
  background-color: #fff;
  border-radius: 0px;
  border: none;
  padding: 1.75rem 3rem;
  -webkit-border-radius: 4px 4px 0 0;
  -moz-border-radius: 4px 4px 0 0;
  -ms-border-radius: 4px 4px 0 0;
  border-radius: 4px 4px 0 0;
}

@media (max-width: 767px) {
  .custom-dialog .ui-widget-header {
    padding: 1.25rem 2rem;
  }
}

.custom-dialog .ui-widget-header .ui-dialog-title {
  font-size: 140%;
  font-weight: normal;
  color: #508FF4;
}

@media (max-width: 767px) {
  .custom-dialog .ui-widget-header .ui-dialog-title {
    font-size: 120%;
  }
}

.custom-dialog .ui-widget-header .ui-dialog-titlebar-close {
  display: none;
}

.custom-dialog .custom-dialog-wrapper {
  padding: 2rem 3rem;
}

@media (max-width: 767px) {
  .custom-dialog .custom-dialog-wrapper {
    padding: 2rem;
  }
}

.custom-dialog .custom-dialog-message {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 2rem;
}

@media (max-width: 767px) {
  .custom-dialog .custom-dialog-message {
    font-size: 14px;
  }
}

.custom-dialog .custom-dialog-form .form-group {
  margin-bottom: 1.25rem;
}

@media (max-width: 767px) {
  .custom-dialog .custom-dialog-form .form-group {
    display: block;
  }
}

.custom-dialog .custom-dialog-form .form-group .form-field {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@media (max-width: 767px) {
  .custom-dialog .custom-dialog-form .form-group .form-field {
    display: block;
  }
}

.custom-dialog .custom-dialog-form .form-group .form-field input[type="text"].form-control,
.custom-dialog .custom-dialog-form .form-group .form-field input[type="number"].form-control,
.custom-dialog .custom-dialog-form .form-group .form-field input[type="tel"].form-control,
.custom-dialog .custom-dialog-form .form-group .form-field textarea.form-control {
  flex: 1;
}

.custom-dialog .custom-dialog-form .form-group .form-field input[type="text"].form-control:focus,
.custom-dialog .custom-dialog-form .form-group .form-field input[type="number"].form-control:focus,
.custom-dialog .custom-dialog-form .form-group .form-field input[type="tel"].form-control:focus,
.custom-dialog .custom-dialog-form .form-group .form-field textarea.form-control:focus {
  border-color: #111111;
}

.custom-dialog .custom-dialog-form .form-group .form-field .form-field-label {
  white-space: nowrap;
}

@media (max-width: 767px) {
  .custom-dialog .custom-dialog-form .form-group .form-field .btn-delete {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }
}

.custom-dialog .ui-dialog-buttonpane {
  border-top: none;
  margin-top: auto;
  padding: 0 3rem 2rem;
  -webkit-border-radius: 0 0 4px 4px;
  -moz-border-radius: 0 0 4px 4px;
  -ms-border-radius: 0 0 4px 4px;
  border-radius: 0 0 4px 4px;
}

@media (max-width: 767px) {
  .custom-dialog .ui-dialog-buttonpane {
    padding: 0 2rem 2rem;
  }
}

.custom-dialog .ui-dialog-buttonpane .ui-dialog-buttonset .btn {
  font-family: "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック体", "Yu Gothic", YuGothic, "メイリオ", Meiryo, Osaka, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif;
  border: none;
  margin: 1rem;
}

@media (max-width: 767px) {
  .custom-dialog .ui-dialog-buttonpane .ui-dialog-buttonset .btn {
    margin: 0.75rem;
  }
}

.custom-dialog .ui-dialog-buttonpane .ui-dialog-buttonset .btn:last-child {
  margin-right: auto;
}

.custom-dialog .ui-dialog-buttonpane .ui-dialog-buttonset .btn-primary {
  color: #fff;
  min-width: 140px;
}

@media (max-width: 767px) {
  .custom-dialog .ui-dialog-buttonpane .ui-dialog-buttonset .btn-primary {
    min-width: 100px;
  }
}

.custom-dialog .ui-dialog-buttonpane .ui-dialog-buttonset .btn-primary:hover {
  color: #fff;
}

.custom-dialog .ui-dialog-buttonpane .ui-dialog-buttonset .btn-cancell {
  color: #fff;
  background-color: #E67E7E;
}

.custom-dialog.custom-dialog-center {
  text-align: center;
}

.custom-dialog.custom-dialog-center .custom-dialog-inner {
  padding: 2rem 0 1rem;
}

@media (max-width: 767px) {
  .custom-dialog.custom-dialog-center .custom-dialog-inner {
    padding-bottom: 0;
  }
  .custom-dialog.custom-dialog-center .custom-dialog-inner p {
    margin-bottom: 1rem;
  }
}

.custom-dialog.custom-dialog-center .ui-dialog-buttonpane {
  padding-bottom: 3rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  -ms-justify-content: center;
  justify-content: center;
}

.custom-dialog.custom-dialog-center .ui-dialog-buttonpane .ui-dialog-buttonset {
  float: none;
}

.custom-dialog.custom-dialog-center .ui-dialog-buttonpane .ui-dialog-buttonset .btn {
  margin: .5rem 1rem;
  min-width: 140px;
}

@media (max-width: 767px) {
  .custom-dialog.custom-dialog-center .ui-dialog-buttonpane .ui-dialog-buttonset .btn {
    min-width: 100px;
    margin: .5rem;
  }
}

.ui-widget-overlay {
  background: black;
  z-index: 1000 !important;
}

#dialog-reserve-operation-time .reserve-date-target {
  background-color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  padding: 2rem 2rem;
  margin-bottom: 3rem;
  white-space: nowrap;
}

@media (max-width: 767px) {
  #dialog-reserve-operation-time .reserve-date-target {
    padding: 2rem 1.25rem;
  }
}

#dialog-reserve-operation-time .reserve-date-target .label {
  width: 200px;
}

@media (max-width: 767px) {
  #dialog-reserve-operation-time .reserve-date-target .label {
    margin-right: auto;
    width: auto;
  }
}

#dialog-reserve-operation-time .reserve-date-target .target {
  flex: 1;
}

@media (max-width: 767px) {
  #dialog-reserve-operation-time .reserve-date-target .target {
    margin-left: auto;
  }
}

#dialog-reserve-operation-time .reserve-time-list {
  background-color: #fff;
  padding: 2rem 2rem;
}

@media (max-width: 767px) {
  #dialog-reserve-operation-time .reserve-time-list {
    padding: 1.25rem;
  }
}

#dialog-reserve-operation-time .reserve-time-list li {
  white-space: nowrap;
  margin-bottom: 2rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  width: 100%;
}

@media (max-width: 767px) {
  #dialog-reserve-operation-time .reserve-time-list li {
    display: block;
  }
}

#dialog-reserve-operation-time .reserve-time-list li:last-child {
  margin-bottom: 0;
}

#dialog-reserve-operation-time .reserve-time-list li .time-range {
  width: 200px;
}

@media (max-width: 767px) {
  #dialog-reserve-operation-time .reserve-time-list li .time-range {
    font-weight: bold;
    width: auto;
    margin-bottom: .5rem;
  }
}

#dialog-reserve-operation-time .reserve-time-list li .receiving-quantity {
  margin-right: 3rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
}

@media (max-width: 767px) {
  #dialog-reserve-operation-time .reserve-time-list li .receiving-quantity {
    padding-left: 1.5rem;
    margin-right: auto;
    margin-bottom: .5rem;
  }
}

#dialog-reserve-operation-time .reserve-time-list li .receiving-quantity span {
  border: 1px solid #C0C0C0;
  background-color: #f5f5f5;
  display: inline-block;
  padding: 5px 15px;
  line-height: 1.5;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  border-radius: 3px;
}

@media (max-width: 767px) {
  #dialog-reserve-operation-time .reserve-time-list li .receiving-quantity span {
    background-color: #fff;
    border: none;
    display: block;
    padding: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    border-radius: 0;
  }
}

#dialog-reserve-operation-time .reserve-time-list li .receiving-number {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
}

@media (max-width: 767px) {
  #dialog-reserve-operation-time .reserve-time-list li .receiving-number {
    padding-left: 1.5rem;
  }
}

#dialog-reserve-operation-time .reserve-time-list li .receiving-number input.form-control {
  border: 1px solid #C0C0C0;
  padding: 5px 8px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  border-radius: 3px;
}

#dialog-reserve-operation-time .reserve-time-list li .receiving-number input.form-control:focus {
  border-color: #508FF4;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  -ms-box-shadow: none;
  box-shadow: none;
}

/* ==============================================
 11. Header
=============================================== */
.header {
  background-color: #fff;
  height: 55px;
  margin-bottom: 5rem;
  position: relative;
  z-index: 100;
}

@media (max-width: 767px) {
  .header .container {
    padding-right: 5px;
  }
}

@media (max-width: 1120px) {
  .header {
    margin-bottom: 3rem;
  }
}

.header .header-title {
  margin-right: 3rem;
  height: 55px;
}

@media (max-width: 1120px) {
  .header .header-title {
    margin-right: 1.5rem;
  }
}

.header .header-title a {
  display: block;
}

.header .header-title a img {
  display: block;
}

@media (max-width: 1120px) {
  .header .header-title a img {
    width: 140px;
  }
}

@media (max-width: 767px) {
  .header .header-title a img {
    width: 110px;
  }
}

.header .header-user-name {
  height: 55px;
  line-height: 1.45;
  margin-left: auto;
  white-space: nowrap;
}

@media (max-width: 1120px) {
.header .header-user-name {
font-size: 85%;
}
}

@media (max-width: 767px) {
.header .header-navigation {
background-color: #fff;
position: absolute;
top: 100%;
left: 0;
height: 0;
opacity: 0;
visibility: hidden;
width: 100%;
-webkit-transition: all 0.14s ease-out;
-moz-transition: all 0.14s ease-out;
-ms-transition: all 0.14s ease-out;
-o-transition: all 0.14s ease-out;
transition: all 0.14s ease-out;
}
}

@media (max-width: 767px) {
.header .header-navigation ul {
border-top: 1px solid #C0C0C0;
display: block;
}
}

.header .header-navigation ul li {
margin-right: 3rem;
}

@media (max-width: 767px) {
.header .header-navigation ul li {
border-bottom: 1px solid #C0C0C0;
margin-right: auto;
}
}

.header .header-navigation ul li:last-child {
margin-right: auto;
}

.header .header-navigation ul li a {
color: #508FF4;
display: block;
height: 55px;
line-height: 55px;
position: relative;
}

@media (max-width: 767px) {
.header .header-navigation ul li a {
height: auto;
line-height: 1.4;
padding: 18px 12px;
}
.header .header-navigation ul li a::after {
right: 14px;
content: "";
position: absolute;
top: 50%;
margin-top: -3.5px;
width: 7px;
height: 7px;
border-top: 2px solid #508FF4;
border-right: 2px solid #508FF4;
-webkit-transform: rotate(45deg);
-moz-transform: rotate(45deg);
-ms-transform: rotate(45deg);
-o-transform: rotate(45deg);
transform: rotate(45deg);
}
}

.header .header-navigation ul li a.is-current {
font-weight: bold;
}

.header .header-navigation ul li a.is-current::before {
background-color: #508FF4;
content: "";
height: 2px;
position: absolute;
left: 0;
top: 0;
width: 100%;
}

@media (max-width: 767px) {
.header .header-navigation ul li a.is-current::before {
content: none;
}
}

.header .header-nav-btn {
display: none;
}

@media (max-width: 767px) {
.header .header-nav-btn {
display: block;
}
}

.header .header-nav-btn button {
background-color: #fff;
border: none;
outline: 0;
padding: 0;
display: block;
position: relative;
height: 45px;
width: 45px;
}

.header .header-nav-btn button span {
display: block;
height: 3px;
left: 10px;
width: 25px;
background-color: #6F6F6F;
position: absolute;
-webkit-transition: all 0.14s ease-out;
-moz-transition: all 0.14s ease-out;
-ms-transition: all 0.14s ease-out;
-o-transition: all 0.14s ease-out;
transition: all 0.14s ease-out;
}

.header .header-nav-btn button span:nth-child(1) {
top: 12px;
}

.header .header-nav-btn button span:nth-child(2) {
top: 21px;
}

.header .header-nav-btn button span:nth-child(3) {
top: 30px;
}

.is-nav-open .header .header-navigation {
opacity: 1;
height: auto;
visibility: visible;
}

.is-nav-open .header .header-nav-btn button span:nth-child(1) {
transform: translateY(9px) rotate(-45deg);
}

.is-nav-open .header .header-nav-btn button span:nth-child(2) {
opacity: 0;
}

.is-nav-open .header .header-nav-btn button span:nth-child(3) {
transform: translateY(-9px) rotate(45deg);
}

/* ==============================================
12. Breadcrumbs
=============================================== */
.breadcrumbs {
  margin-top: 1rem;
  margin-bottom: 2.5rem;
  font-size: 0;
  letter-spacing: 0;
}

@media (max-width: 767px) {
  .breadcrumbs {
    margin-bottom: 2rem;
  }
}

.breadcrumbs .container {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.breadcrumbs .container > span {
  font-size: 13px;
}

@media (max-width: 767px) {
  .breadcrumbs .container > span {
    font-size: 11px;
  }
}

.breadcrumbs .container > span > a, .breadcrumbs .container > span > span {
  display: inline-block;
  position: relative;
  margin-left: 25px;
}

.breadcrumbs .container > span > a::before, .breadcrumbs .container > span > span::before {
  content: "";
  position: absolute;
  left: -17px;
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -3px;
  width: 6px;
  height: 6px;
  border-top: 1px solid #111111;
  border-right: 1px solid #111111;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

.breadcrumbs .container > span a.home {
  margin-left: auto;
}

.breadcrumbs .container > span a.home::before {
  content: none;
}

.page-back-link {
  margin-bottom: 1rem;
}

.page-back-link a {
  background: url("img/icon_back_dark.svg") no-repeat 0 2px;
  display: inline-block;
  padding: 0 0 0 23px;
}

/* ==============================================
 13. Aside
=============================================== */
/* ==============================================
 14. Footer
=============================================== */
/* common
----------------------------------------------- */
/* ==============================================
 20. Calender setting
=============================================== */
#js-calender #calender-header {
  margin-bottom: 1.25rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
}

#js-calender #calender-header a {
  cursor: pointer;
  background: none;
  padding: 10px;
  position: relative;
}

#js-calender #calender-header a.is-disabled {
  pointer-events: none;
}

#js-calender #calender-header #calendar-prev-button::after {
  left: 10px;
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -4.5px;
  width: 9px;
  height: 9px;
  border-bottom: 2px solid #508FF4;
  border-left: 2px solid #508FF4;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

#js-calender #calender-header #calendar-prev-button.is-disabled::after {
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -4.5px;
  width: 9px;
  height: 9px;
  border-bottom: 2px solid #C0C0C0;
  border-left: 2px solid #C0C0C0;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

#js-calender #calender-header #calendar-next-button::after {
  right: 10px;
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -4.5px;
  width: 9px;
  height: 9px;
  border-top: 2px solid #508FF4;
  border-right: 2px solid #508FF4;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

#js-calender #calender-header #calendar-next-button.is-disabled::after {
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -4.5px;
  width: 9px;
  height: 9px;
  border-top: 2px solid #C0C0C0;
  border-right: 2px solid #C0C0C0;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

#js-calender #calender-header #calender-month {
  font-size: 18px;
  font-weight: bold;
  margin: 0 1rem;
}

#js-calender #calendar {
  text-align: left;
  width: 100%;
}

#js-calender #calendar table {
  border-collapse: collapse;
  width: 100%;
}

#js-calender #calendar table th, #js-calender #calendar table td {
  border: 1px solid #C0C0C0;
  outline: none;
  padding: 8px;
  text-align: left;
}

#js-calender #calendar table th {
  background-color: #EAF2FF;
  font-weight: normal;
  text-align: center;
}

#js-calender #calendar table tr.dayOfWeek th:nth-child(6n) {
  color: #074DBC;
}

#js-calender #calendar table tr.dayOfWeek th:nth-child(7n) {
  color: #C90303;
}

#js-calender #calendar table td.disabled {
  background-color: #f5f5f5;
}

#js-calender #calendar table td .col-day {
  text-align: right;
  margin-bottom: .75rem;
}

#js-calender #calendar table td .col-count-btn {
  margin-bottom: 1.25rem;
}

#js-calender #calendar table td .col-count-btn .btn {
  font-size: 85%;
  padding: 5px 12px;
}

#js-calender #calendar table td .form-checkbox {
  font-size: 85%;
  margin-bottom: 1rem;
}

#js-calender #calendar table td .form-checkbox .form-checkbox-label {
  margin-left: 1rem;
}

#js-calender #calendar table td .form-checkbox .form-checkbox-label .form-checkbox-text {
  padding: 0;
  height: 20px;
  width: 20px;
  vertical-align: middle;
}

#js-calender #calendar table td .form-checkbox .form-checkbox-label .form-checkbox-text::before {
  height: 20px;
  width: 20px;
}

#js-calender #calendar table td.day {
  background-position: 8px 8px;
  background-repeat: no-repeat;
}

#js-calender #calendar table td.day.reserve-status-1 {
  background-image: url("img/day_mark01.svg");
  background-size: 16px 16px;
}

#js-calender #calendar table td.day.reserve-status-2 {
  background-image: url("img/day_mark02.svg");
  background-size: 16px 16px;
}

#js-calender #calendar table td.day.reserve-status-3 {
  background-image: url("img/day_mark03.svg");
  background-size: 16px 16px;
}

#js-calender #calendar table td.day.reserve-status-none {
  background-image: url("img/day_mark04.svg");
  background-size: 13px 13px;
  background-position: 10px 10px;
}

#js-calender #calendar table td.day.reserve-status-tel {
  background-image: url("img/day_mark05.svg");
  background-size: 24px 12px;
  background-position: 8px 10px;
}

/* ==============================================
 31. Login
=============================================== */
.login-card {
  margin-left: auto;
  margin-right: auto;
  min-height: 100vh;
  min-height: calc(var(--vh, 1vh) * 100);
  width: 100vw;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  align-items: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  -ms-justify-content: center;
  justify-content: center;
}

.login-card .login-card-inner {
  width: 370px;
  max-width: 100%;
}

.login-card h1 {
  color: #508FF4;
  font-size: 18px;
  letter-spacing: 0.08em;
  margin-bottom: 34px;
  text-align: center;
}

.login-card h1 img {
  display: block;
  margin: 0 auto;
}

.login-card .login-card-body .form-block {
  margin-bottom: 30px;
}

.login-card .login-card-body .form-block .form-inner {
  background: #fff;
  overflow: hidden;
  margin-bottom: 35px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
  -webkit-box-shadow: 0 8px 15px #E7EAF0;
  -moz-box-shadow: 0 8px 15px #E7EAF0;
  -ms-box-shadow: 0 8px 15px #E7EAF0;
  box-shadow: 0 8px 15px #E7EAF0;
}

.login-card .login-card-body .form-block .form-field .form-login-input {
  position: relative;
  padding: 12px 12px 12px 65px;
}

.login-card .login-card-body .form-block .form-field .form-login-input.form-login-input--email {
  background: url("img/icon_member_gray.svg") no-repeat 21px 50%;
  background-size: 23px 23px;
}

.login-card .login-card-body .form-block .form-field .form-login-input.form-login-input--password {
  background: url("img/icon_password_gray.svg") no-repeat 22px 50%;
  background-size: 22px 25px;
}

.login-card .login-card-body .form-block .form-field .form-login-input .form-control {
  background: none;
  border: none;
  padding: 18px 0 4px;
  position: relative;
  z-index: 1;
}

.login-card .login-card-body .form-block .form-field .form-login-input .form-control:focus, .login-card .login-card-body .form-block .form-field .form-login-input .form-control.form-entered {
  background: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  -ms-box-shadow: none;
  box-shadow: none;
}

.login-card .login-card-body .form-block .form-field .form-login-input .form-control:focus + .input-name, .login-card .login-card-body .form-block .form-field .form-login-input .form-control.form-entered + .input-name {
  color: #111111;
  top: 12px;
  z-index: 2;
}

.login-card .login-card-body .form-block .form-field .form-login-input .input-name {
  color: #a8adb7;
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  font-size: 12px;
  letter-spacing: 0.07em;
  left: 65px;
  position: absolute;
  top: 16px;
  -webkit-transition: all 0.14s ease-out;
  -moz-transition: all 0.14s ease-out;
  -ms-transition: all 0.14s ease-out;
  -o-transition: all 0.14s ease-out;
  transition: all 0.14s ease-out;
}

.login-card .login-card-body .form-block .form-btn-area {
  text-align: center;
}

.login-card .login-card-body .form-block .form-btn-area .btn {
  background: #508FF4 url("img/btn_arrow01.svg") no-repeat right 10px top 50%;
  background-size: 32px 32px;
  color: #fff;
  display: block;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 0.06em;
  padding: 16px;
  width: 100%;
}

.login-card .reset-login-password {
  text-align: center;
  font-size: 85%;
}

.login-card .reset-login-password a {
  color: #777777;
}

.login-card .reset-login-password a:hover {
  text-decoration: underline;
}

/* ==============================================
 Media Queries
=============================================== */
@media screen and (max-width: 767px) {
  a[href^="tel:"] {
    cursor: pointer;
    pointer-events: auto;
  }
}

/* ==============================================
 Media Queries
=============================================== */
/* hidden
----------------------------------------------- */
@media screen and (min-width: 992px) {
  .hidden-lg {
    display: none !important;
  }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
  .hidden-md {
    display: none !important;
  }
}

@media screen and (min-width: 544px) and (max-width: 767px) {
  .hidden-sm {
    display: none !important;
  }
}

@media screen and (max-width: 543px) {
  .hidden-xs {
    display: none !important;
  }
}

/* column
----------------------------------------------- */
.col-lg-1 {
  width: 12%;
}

.col-lg-2 {
  width: 16.6666%;
}

.col-lg-3 {
  width: 25%;
}

.col-lg-4 {
  width: 33.3333%;
}

.col-lg-5 {
  width: 41.6666%;
}

.col-lg-6 {
  width: 50%;
}

.col-lg-7 {
  width: 57.0833%;
}

.col-lg-8 {
  width: 66.6667%;
}

.col-lg-9 {
  width: 75%;
}

.col-lg-10 {
  width: 83.3334%;
}

.col-lg-11 {
  width: 91.6666%;
}

.col-lg-12 {
  width: 100%;
}

/* pc min
----------------------------------------------- */
@media screen and (max-width: 1119px) {
  /* column
  ----------------------------------------------- */
  .col-md-1 {
    width: 12%;
  }
  .col-md-2 {
    width: 16.6666%;
  }
  .col-md-3 {
    width: 25%;
  }
  .col-md-4 {
    width: 33.3333%;
  }
  .col-md-5 {
    width: 41.6666%;
  }
  .col-md-6 {
    width: 50%;
  }
  .col-md-7 {
    width: 57.0833%;
  }
  .col-md-8 {
    width: 66.6667%;
  }
  .col-md-9 {
    width: 75%;
  }
  .col-md-10 {
    width: 83.3334%;
  }
  .col-md-11 {
    width: 91.6666%;
  }
  .col-md-12 {
    width: 100%;
  }
}

/* tablet
----------------------------------------------- */
@media screen and (max-width: 991px) {
  body.sp-nav-open {
    overflow: hidden !important;
  }
  /* ==============================================
     Layout
    =============================================== */
  /* column
    ----------------------------------------------- */
  .col-sm-1 {
    width: 12%;
  }
  .col-sm-2 {
    width: 16.6666%;
  }
  .col-sm-3 {
    width: 25%;
  }
  .col-sm-4 {
    width: 33.3333%;
  }
  .col-sm-5 {
    width: 41.6666%;
  }
  .col-sm-6 {
    width: 50%;
  }
  .col-sm-7 {
    width: 57.0833%;
  }
  .col-sm-8 {
    width: 66.6667%;
  }
  .col-sm-9 {
    width: 75%;
  }
  .col-sm-10 {
    width: 83.3334%;
  }
  .col-sm-11 {
    width: 91.6666%;
  }
  .col-sm-12 {
    width: 100%;
  }
}

/* column
----------------------------------------------- */
@media screen and (max-width: 767px) {
  /* column
    ----------------------------------------------- */
  .col-xs-1 {
    width: 12%;
  }
  .col-xs-2 {
    width: 16.6666%;
  }
  .col-xs-3 {
    width: 25%;
  }
  .col-xs-4 {
    width: 33.3333%;
  }
  .col-xs-5 {
    width: 41.6666%;
  }
  .col-xs-6 {
    width: 50%;
  }
  .col-xs-7 {
    width: 57.0833%;
  }
  .col-xs-8 {
    width: 66.6667%;
  }
  .col-xs-9 {
    width: 75%;
  }
  .col-xs-10 {
    width: 83.3334%;
  }
  .col-xs-11 {
    width: 91.6666%;
  }
  .col-xs-12 {
    width: 100%;
  }
}

@media print {
  body {
    zoom: 75%;
  }
}
