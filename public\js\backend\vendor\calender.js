// setting
const week = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
const todayDate = new Date().toLocaleString({ timeZone: 'Asia/Tokyo' });
const today = new Date(todayDate);
const minMonth = 0;
const maxMonth = 3;
var showDate = new Date(today.getFullYear(), today.getMonth(), 1);

// サンプル
var jsonDate = [
	{
		"day": "2022-02-01",
		"count": 2,
		"tel": false
	},
	{
		"day": "2022-02-02",
		"count": 5,
		"tel": false
	},
	{
		"day": "2022-02-03",
		"count": 10,
		"tel": true
	},
	{
		"day": "2022-02-04",
		"count": 22,
		"tel": false
	},
	{
		"day": "2022-02-05",
		"count": 16,
		"tel": true
	},
	{
		"day": "2022-02-06",
		"count": 0,
		"tel": false
	},
	{
		"day": "2022-02-07",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-08",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-09",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-10",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-11",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-12",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-13",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-14",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-15",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-16",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-17",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-18",
		"count": 10,
		"tel": false
	},
	{
		"day": "2022-02-19",
		"count": 3,
		"tel": false
	},
	{
		"day": "2022-02-20",
		"count": 1,
		"tel": false
	},
	{
		"day": "2022-02-21",
		"count": 2,
		"tel": false
	},
	{
		"day": "2022-02-22",
		"count": 20,
		"tel": false
	},
	{
		"day": "2022-02-23",
		"count": 20,
		"tel": false
	},
	{
		"day": "2022-02-24",
		"count": 20,
		"tel": false
	},
	{
		"day": "2022-02-25",
		"count": 20,
		"tel": true
	},
	{
		"day": "2022-02-26",
		"count": 20,
		"tel": true
	},
	{
		"day": "2022-02-27",
		"count": 20,
		"tel": true
	},
	{
		"day": "2022-02-28",
		"count": 20,
		"tel": false
	},
	{
		"day": "2022-03-01",
		"count": 20,
		"tel": false
	},
	{
		"day": "2022-03-02",
		"count": 20,
		"tel": false
	},
	{
		"day": "2022-03-03",
		"count": 20,
		"tel": false
	},
	{
		"day": "2022-03-04",
		"count": 20,
		"tel": false
	},
	{
		"day": "2022-03-05",
		"count": 20,
		"tel": false
	},
	{
		"day": "2022-03-06",
		"count": 20,
		"tel": false
	},
];

var jsonDateTime = [
	{
		"day": "2022-02-01",
		"timeData": {
			0: {
        "quantity": 1,
        "number": 2
      },
      1: {
        "quantity": 1,
        "number": 2
      },
      2: {
        "quantity": 61,
        "number": 71
      },
      3: {
        "quantity": 2,
        "number": 72
      },
      4: {
        "quantity": 7,
        "number": 93
      },
      5: {
        "quantity": 1,
        "number": 1
      },
		}
	},
	{
		"day": "2022-02-02",
    "timeData": {
			0: {
        "quantity": 3,
        "number": 3
      },
      1: {
        "quantity": 1,
        "number": 2
      },
      2: {
        "quantity": 6,
        "number": 7
      },
      3: {
        "quantity": 2,
        "number": 7
      },
      4: {
        "quantity": 7,
        "number": 9
      },
      5: {
        "quantity": 1,
        "number": 1
      },
		}
	},
	{
		"day": "2022-02-03",
    "timeData": {
			0: {
        "quantity": 3,
        "number": 3
      },
      1: {
        "quantity": 1,
        "number": 2
      },
      2: {
        "quantity": 6,
        "number": 7
      },
      3: {
        "quantity": 2,
        "number": 7
      },
      4: {
        "quantity": 7,
        "number": 9
      },
      5: {
        "quantity": 1,
        "number": 1
      },
		}
	},
	{
		"day": "2022-02-04",
    "timeData": {
			0: {
        "quantity": 3,
        "number": 3
      },
      1: {
        "quantity": 1,
        "number": 2
      },
      2: {
        "quantity": 6,
        "number": 7
      },
      3: {
        "quantity": 2,
        "number": 7
      },
      4: {
        "quantity": 7,
        "number": 9
      },
      5: {
        "quantity": 1,
        "number": 1
      },
		}
	},
	{
		"day": "2022-02-05",
    "timeData": {
			0: {
        "quantity": 3,
        "number": 3
      },
      1: {
        "quantity": 1,
        "number": 2
      },
      2: {
        "quantity": 6,
        "number": 7
      },
      3: {
        "quantity": 2,
        "number": 7
      },
      4: {
        "quantity": 7,
        "number": 9
      },
      5: {
        "quantity": 1,
        "number": 1
      },
		}
	},
	{
		"day": "2022-02-06",
    "timeData": {
			0: {
        "quantity": 3,
        "number": 3
      },
      1: {
        "quantity": 1,
        "number": 2
      },
      2: {
        "quantity": 6,
        "number": 7
      },
      3: {
        "quantity": 2,
        "number": 7
      },
      4: {
        "quantity": 7,
        "number": 9
      },
      5: {
        "quantity": 1,
        "number": 1
      },
		}
	},
	{
		"day": "2022-02-07",
    "timeData": {
			0: {
        "quantity": 3,
        "number": 3
      },
      1: {
        "quantity": 1,
        "number": 2
      },
      2: {
        "quantity": 6,
        "number": 7
      },
      3: {
        "quantity": 2,
        "number": 7
      },
      4: {
        "quantity": 7,
        "number": 9
      },
      5: {
        "quantity": 1,
        "number": 1
      },
		}
	},
	{
		"day": "2022-02-08",
    "timeData": {
			0: {
        "quantity": 3,
        "number": 3
      },
      1: {
        "quantity": 1,
        "number": 2
      },
      2: {
        "quantity": 6,
        "number": 7
      },
      3: {
        "quantity": 2,
        "number": 7
      },
      4: {
        "quantity": 7,
        "number": 9
      },
      5: {
        "quantity": 1,
        "number": 1
      },
		}
	},
	{
		"day": "2022-02-09",
    "timeData": {
			0: {
        "quantity": 3,
        "number": 3
      },
      1: {
        "quantity": 1,
        "number": 2
      },
      2: {
        "quantity": 6,
        "number": 7
      },
      3: {
        "quantity": 2,
        "number": 7
      },
      4: {
        "quantity": 7,
        "number": 9
      },
      5: {
        "quantity": 1,
        "number": 1
      },
		}
	},
	{
		"day": "2022-02-10",
    "timeData": {
			0: {
        "quantity": 3,
        "number": 3
      },
      1: {
        "quantity": 1,
        "number": 2
      },
      2: {
        "quantity": 6,
        "number": 7
      },
      3: {
        "quantity": 2,
        "number": 7
      },
      4: {
        "quantity": 7,
        "number": 9
      },
      5: {
        "quantity": 1,
        "number": 1
      },
		}
	},
	{
		"day": "2022-02-11",
    "timeData": {
			0: {
        "quantity": 3,
        "number": 3
      },
      1: {
        "quantity": 1,
        "number": 2
      },
      2: {
        "quantity": 6,
        "number": 7
      },
      3: {
        "quantity": 2,
        "number": 7
      },
      4: {
        "quantity": 7,
        "number": 9
      },
      5: {
        "quantity": 1,
        "number": 1
      },
		}
	},
];

// カレンダーのセルにアイコンを表示するためのclass命名
function setCellClassName(count, tel) {
	var className = 'reserve-status-none';
	if (tel) {
		className = 'reserve-status-tel';
	} else {
		if (count > 4) {
			className = 'reserve-status-1';
		} else if(count > 2) {
			className = 'reserve-status-2';
		} else if(count > 0) {
			className = 'reserve-status-3';
		} else if(count == 'tel') {
			className = 'reserve-status-tel';
		}
	}
	return className;
}

// 時間帯
const timeRange = {
	0: '10:00〜11:30',
	1: '11:30〜13:00',
	2: '13:00〜14:30',
	3: '14:30〜16:00',
	4: '16:00〜18:30',
	5: '18:30〜20:00',
}

// 時間選択エリアの生成
function createReserveTime(date) {
	var cell_date = new Date(date);
  var dayOfWeek = cell_date.getDay();
  var dayOfWeekStr = [ "日", "月", "火", "水", "木", "金", "土" ][dayOfWeek];
	var show_date = cell_date.getFullYear()+'年'+(cell_date.getMonth()+1)+'月'+cell_date.getDate()+'日（'+dayOfWeekStr+'）';
	// 日付
	document.getElementById('show-reserve-date').textContent = show_date;
  var format_date = cell_date.getFullYear()+'-'+("0" + (cell_date.getMonth()+1)).slice(-2)+'-'+("0" + cell_date.getDate()).slice(-2);
  document.getElementById('set-reserve-date').value = format_date;
	// 時間選択リスト
	var indexDate = jsonDateTime.filter(function(item, index){
		if (item.day == format_date) return item;
	});
	var targetElm = document.getElementById('reserve-time-list');
	targetElm.innerHTML = '';
	if (indexDate.length > 0) {
		var timeData = indexDate[0].timeData;
		for (let key in timeData) {
			var val = timeData[key];
			var li = '<li>'
             + '<div class="time-range">'+timeRange[key]+'</div>'
             + '<div class="receiving-quantity">受入可能数：<span>'+val.quantity+'</span></div>'
             + '<div class="receiving-number">予約受付数：<input type="number" name="" value="'+val.number+'" class="form-control"></div>'
             + '</li>';
			$(li).appendTo(targetElm);
		}
	} else {
		$('<p>選択できません</p>').appendTo(targetElm);
	}
}

// 初期表示
window.onload = function () {
  showProcess(today, calendar);
};

// 前の月表示
function prevMonth(){
  showDate.setMonth(showDate.getMonth() - 1);
  showProcess(showDate);
}

// 次の月表示
function nextMonth(){
  showDate.setMonth(showDate.getMonth() + 1);
  showProcess(showDate);
}

// カレンダー表示
function showProcess(date) {
  var year = date.getFullYear();
  var month = date.getMonth();
  document.querySelector('#calender-month').innerHTML = (month + 1) + "月 " + year;

  var calendar = createProcess(year, month, jsonDate);
  document.querySelector('#calendar').innerHTML = calendar;

  var prev = document.getElementById('calendar-prev-button');
  if (new Date(today.getFullYear(), today.getMonth() - minMonth, 1) < new Date(year, month, 0)) {
    prev.classList.remove('is-disabled');
  } else {
    prev.classList.add('is-disabled');
  }
  var next = document.getElementById('calendar-next-button');
  if (new Date(today.getFullYear(), today.getMonth() + maxMonth, 1) > new Date(year, month, 1)) {
    next.classList.remove('is-disabled');
  } else {
    next.classList.add('is-disabled');
  }
}

// カレンダー作成
function createProcess(year, month, jsonDate) {
  // 曜日
  var calendar = "<table><tr class='dayOfWeek'>";
  for (var i = 0; i < week.length; i++) {
      calendar += "<th>" + week[i] + "</th>";
  }
  calendar += "</tr>";

  var count = 0;
  var startDayOfWeek = new Date(year, month, 0).getDay();
  var endDate = new Date(year, month + 1, 0).getDate();
  var lastMonthEndDate = new Date(year, month, 0).getDate();
  var row = Math.ceil((startDayOfWeek + endDate) / week.length);

  // 1行ずつ設定
  for (var i = 0; i < row; i++) {
      calendar += "<tr>";
      // 1colum単位で設定
      for (var j = 0; j < week.length; j++) {
          if (i == 0 && j < startDayOfWeek) {
              // 1行目で1日まで先月の日付を設定
              // calendar += "<td class='disabled'>" + (lastMonthEndDate - startDayOfWeek + j + 1) + "</td>";
              calendar += "<td class='disabled'></td>";
          } else if (count >= endDate) {
              // 最終行で最終日以降、翌月の日付を設定
              count++;
              // calendar += "<td class='disabled'>" + (count - endDate) + "</td>";
              calendar += "<td class='disabled'></td>";
          } else {
              // 当月の日付を曜日に照らし合わせて設定
              count++;
              let count_date = year+'-'+("0" + (month+1)).slice(-2)+'-'+("0" + count).slice(-2);
              var indexDate = jsonDate.filter(function(item, index){
                if (item.day == count_date) return item;
              });
              if (indexDate.length > 0) {
        				var className = setCellClassName(indexDate[0].count, indexDate[0].tel);
        			} else {
        				var className = 'reserve-status-none';
        			}
              if(year == today.getFullYear()
                && month == (today.getMonth())
                && count == today.getDate()){
                calendar += "<td class='today day"+className+"'><div class='col-day'>"+count+"</div>";
              } else {
                calendar += "<td class='day "+className+"'><div class='col-day'>"+count+"</div>";
              }
              calendar += "<div class='col-count-btn'><a href='#' class='open-dialog btn btn-primary' data-dialog='dialog-reserve' data-btn-date='"+count_date+"'>予約数入力</a></div>"
              + "<div class='form-checkbox'>TEL<label class='form-checkbox-label'>"
              + "<input class='form-checkbox-input' name='tel_"+count_date+"' type='checkbox'><span class='form-checkbox-text'></span>"
              + "</label></div>"
              + "</td>";

          }
      }
      calendar += "</tr>";
  }
  return calendar;
}
