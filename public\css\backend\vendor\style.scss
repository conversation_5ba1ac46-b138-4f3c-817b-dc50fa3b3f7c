@charset "utf-8";

// color
$FONT_COLOR: #111111;
$DEEP_COLOR: #111111;
$LINK_COLOR: #508FF4;
$MAIN_COLOR: #508FF4;
$DANGER_COLOR: #C90303;
$BORDER_COLOR: #C0C0C0;
$BASE_COLOR: #EAF2FF;
$MUTE_COLOR: #777777;
$FORM_COLOR: #6F6F6F;


// font family
$FONT_FAMILY_SANS: "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック体", "Yu Gothic", YuGothic, "メイリオ", Meiryo, Osaka, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif;
$FONT_FAMILY_EN: 'Roboto', sans-serif;

body {
  font-family: $FONT_FAMILY_SANS;
  margin: 0;
  padding: 0;
  color: $FONT_COLOR;
  font-size: 13px;
  position: relative;
}

// media query
$lg: 1740px;
$md: 1439px;
$minpc: 1279px;
$default: 1000px;
$tab: 1120px;
$mintab: 940px;
$sp: 767px;
$xsp: 360px;
@mixin lgpc {
  @media (max-width: ($lg)) {
    @content;
  }
}
@mixin mdpc {
  @media (max-width: ($md)) {
    @content;
  }
}
@mixin minpc {
  @media (max-width: ($minpc)) {
    @content;
  }
}
@mixin inner {
  @media (max-width: ($default)) {
    @content;
  }
}
@mixin pc {
  @media (min-width: ($tab+1)) {
    @content;
  }
}
@mixin tab {
  @media (max-width: ($tab)) {
    @content;
  }
}
@mixin mintab {
  @media (max-width: ($mintab)) {
    @content;
  }
}
@mixin sp {
  @media (max-width: ($sp)) {
    @content;
  }
}
@mixin xsp {
  @media (max-width: ($xsp)) {
    @content;
  }
}

// mixin
@mixin transition($property: all, $time: 0.14s, $timing: ease-out) {
  -webkit-transition: $property $time $timing;
  -moz-transition: $property $time $timing;
  -ms-transition: $property $time $timing;
  -o-transition: $property $time $timing;
  transition: $property $time $timing;
}

@mixin transform($value) {
  -webkit-transform: $value;
  -moz-transform: $value;
  -ms-transform: $value;
  -o-transform: $value;
  transform: $value;
}

@mixin text-shadow($value...) {
  -webkit-text-shadow: $value;
  -moz-text-shadow: $value;
  -ms-text-shadow: $value;
  text-shadow: $value;
}

@mixin box-shadow($value...) {
  -webkit-box-shadow: $value;
  -moz-box-shadow: $value;
  -ms-box-shadow: $value;
  box-shadow: $value;
}

@mixin border-radius($value: 4px) {
  -webkit-border-radius: $value;
  -moz-border-radius: $value;
  -ms-border-radius: $value;
  border-radius: $value;
}

@mixin flex() {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

@mixin flex-wrap($value: wrap) {
  -webkit-flex-wrap: $value;
  -ms-flex-wrap: $value;
  flex-wrap: $value;
}

@mixin align-items($value: center) {
  -webkit-align-items: $value;
  -moz-align-items: $value;
  -ms-align-items: $value;
  align-items: $value;
}

@mixin justify-content($value: center) {
  -webkit-justify-content: $value;
  -moz-justify-content: $value;
  -ms-justify-content: $value;
  justify-content: $value;
}

@mixin clearfix {
  &::after {
      content: "";
      display: block;
      clear: both;
  }
}

@mixin next-arrow($color: $MAIN_COLOR, $size: 8px, $border: 2px) {
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -$size / 2;
  width: $size;
  height: $size;
  border-top: $border solid $color;
  border-right: $border solid $color;
  @include transform(rotate(45deg));
}

@mixin prev-arrow($color: $MAIN_COLOR, $size: 8px, $border: 2px) {
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -$size / 2;
  width: $size;
  height: $size;
  border-bottom: $border solid $color;
  border-left: $border solid $color;
  @include transform(rotate(45deg));
}

@mixin linear-gradient($top_color: #40b2ff, $bottom_color: #0087e2) {
  background: $top_color; /* Old browsers */
  background: -moz-linear-gradient(top, $top_color 0%, $bottom_color 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top,$top_color 0%, $bottom_color 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, $top_color 0%, $bottom_color 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}


/* =============================================
 *  01. Common settings
 *      - reset
 *      - common
 *      - padding
 *      - margin
 *      - width
 *  02. Layout
 *      - common
 *      - float
 *      - clearfix
 *      - align
 *  03. Font
 *      - font size
 *      - font color
 *      - font family
 *      - contact
 *  04. Parts
 *      - icon
 *      - button
 *      - table
 *      - heading
 *      - pagination
 *  05. Form
 *      - layout
 *      - parts
 *  06. Dialog
 * -----------------------------------
 *  11. Header
 *  12. Navigation
 *  13. Aside
 *  14. Footer
 * -----------------------------------
 *  20. Calender setting
 * -----------------------------------
 *  31. Login
 * ============================================= */
/* ---------------------------------------------------------------------------------------- */

/* ==============================================
 01. Common settings
=============================================== */
/* reset
----------------------------------------------- */
html, body, div, span, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
abbr, address, cite, code,
del, dfn, em, img, ins, kbd, q, samp,
small, strong, sub, sup, var,
b, i,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, figcaption, figure,
footer, header, hgroup, menu, nav, section, summary,
time, mark, audio, video {
    margin:0;
    padding:0;
    border:0;
    outline:0;
    font-size:100%;
    vertical-align:bottom;
}
body {
}
article,aside,details,figcaption,figure,main,
footer,header,hgroup,menu,nav,section {
    display:block;
}
nav ul {
    list-style:none;
}
blockquote, q {
    quotes:none;
}
blockquote:before, blockquote:after,
q:before, q:after {
    content:'';
    content:none;
}
a {
    margin:0;
    padding:0;
    font-size:100%;
    vertical-align:baseline;
    background:transparent;
}
/* change colours to suit your needs */
ins {
    background-color:#ff9;
    color:#000;
    text-decoration:none;
}
/* change colours to suit your needs */
mark {
    background-color:#ff9;
    color:#000;
    font-style:italic;
    font-weight:bold;
}
del {
    text-decoration: line-through;
}
abbr[title], dfn[title] {
    border-bottom:1px dotted;
    cursor:help;
}
table {
    border-collapse: separate;
    border-spacing:0;
}
/* change border colour to suit your needs */
hr {
    display:block;
    height:1px;
    border:0;
    border-top:1px solid #cccccc;
    margin:1em 0;
    padding:0;
}
input, select {
    vertical-align:middle;
}
span {
  vertical-align: baseline;
}

/* common
----------------------------------------------- */
*, *:before, *:after {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
       -o-box-sizing: border-box;
      -ms-box-sizing: border-box;
          box-sizing: border-box;
}

html {
  font-size: 62.5%;
  overflow-y: scroll;
  margin: 0;
  width: 100%;
}

body {
  background: $BASE_COLOR;
  color: $FONT_COLOR;
  font-family: $FONT_FAMILY_SANS;
  font-size: 14px;
  font-size: 1.4rem;
  font-weight: normal;
  font-weight: 400;
  letter-spacing: 0;
  margin: 0;
  padding: 0;
  -webkit-text-size-adjust: 100%;
  width: 100%;
  @include sp {
    font-size: 13px;
    font-size: 1.3rem;
  }
}

.preload * {
  @include transition(none);
}

a {
    color: $FONT_COLOR;
    outline: medium none;
    text-decoration: none;
}

a::before, a::after {
}

a:visited {
    outline: medium none;
}

a:focus {
    outline: medium none;
}

a:active, a:hover {
    outline: medium none;
}

a:hover {
  color: $FONT_COLOR;
  text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
    clear: both;
    font-weight: bold;
    font-weight: 600;
    margin: 0;
}

h1 {
}

h2 {
}

h3 {
}

h4 {
}

h5 {
}

h6 {
}

address {
    font-style: italic;
    margin: 0 0 24px;
}

abbr[title] {
    border-bottom: 1px dotted;
}

b, strong {
    font-weight: bold;
}

dfn {
    font-style: italic;
}

mark {
    background: none repeat scroll 0 0 #FFFF00;
    color: #000000;
}

p {
    line-height: 1.6;
    margin: 0 0 24px;
    max-height: 100%;
}

code, kbd, pre, samp {
    -moz-hyphens: none;
    font-family: monospace,serif;
    font-size: 14px;
}

pre {
    background: none repeat scroll 0 0 #F5F5F5;
    color: #666666;
    font-family: monospace;
    font-size: 14px;
    margin: 20px 0;
    overflow: auto;
    padding: 20px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

blockquote, q {
    -moz-hyphens: none;
    quotes: none;
}

blockquote:before, blockquote:after, q:before, q:after {
    content: none;
}

blockquote {
    font-size: 18px;
    font-style: italic;
    font-weight: 300;
    margin: 24px 40px;
}

blockquote blockquote {
    margin-right: 0;
}

blockquote cite, blockquote small {
    font-size: 14px;
    font-weight: normal;
    text-transform: uppercase;
}

blockquote em, blockquote i {
    font-style: normal;
    font-weight: 300;
}

blockquote strong, blockquote b {
    font-weight: 400;
}

small {
    font-size: 13px;
    font-size: 1.3rem;
}

sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

dl {
    margin: 0;
}

dt {
  line-height: 1.8;
    font-weight: bold;
    font-weight: 600;
    margin: 0;
}

dd {
  line-height: 1.8;
    margin: 0;
}

menu, ol, ul {
    margin: 0;
    padding: 0;
}

ul {
    list-style-type: none;
}

li {
  line-height: 1.8;
}
th {
  font-weight: bold;
}
td {
}

img {
    border: 0 none;
    height: auto;
    max-width: 100%;
    vertical-align: middle;
    -webkit-backface-visibility: hidden
}
input[type="text"]:focus,
textarea:focus {
  outline: 0;
}

a[href^="tel:"] {
  cursor: default;
  pointer-events: none;
}

/* ==============================================
 02. Layout
=============================================== */
/* Common
----------------------------------------------- */
.wrapper {
}
.content {
  padding-bottom: 50px;
}
.container {
  padding-right: 25px;
  padding-left: 25px;
  max-width: 1180px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  @include sp {
    padding-left: 12px;
    padding-right: 12px;
  }
}
.flex {
  @include flex;
}
.flex-wrap {
  @include flex-wrap;
}
.align-items-center {
  @include align-items;
}
.justify-content-center {
  @include justify-content;
}
.flex-root {
  -webkit-flex: 1;
  flex: 1;
}
.flex-column {
  flex-direction: column;
}
.flex-column-fluid {
  -webkit-flex: 1 0 auto;
  flex: 1 0 auto;
}
.flex-row {
  flex-direction: row;
}
.flex-row-fluid {
  -webkit-flex: 1 auto;
  flex: 1 auto;
  min-width: 0;
}
.justify-content-end {
  -webkit-box-pack: end;
	-ms-flex-pack: end;
  justify-content: flex-end;
}
.flex-shrink-0 {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

/* float
----------------------------------------------- */
.fl-left {
    float: left;
}
.fl-right {
    float: right;
}

/* clearfix
----------------------------------------------- */
.cf:before,
.cf:after {
    content: " ";
    display: table;
}
.cf:after {
    clear: both;
}
.cf {
    *zoom: 1;
}

/* align
----------------------------------------------- */
.align-top {
  vertical-align: top;
}
.align-bottom {
  vertical-align: bottom;
}
.align-middle {
  vertical-align: middle;
}
.align-baseline {
  vertical-align: baseline;
}

/* card
----------------------------------------------- */
.card {
  background-color: #fff;
  @include border-radius(6px);
  position: relative;
  min-width: 0;
  .card-header {
    padding: 30px 30px 0;
    @include sp {
      padding: 15px 15px 0;
    }
    .btn-primary {
      min-width: 160px;
      @include sp {
        min-width: 120px;
      }
    }
  }
  .card-body {
    padding: 30px 30px 30px;
    @include sp {
      padding: 15px 15px;
    }
  }
}

/* ==============================================
 03. Font
=============================================== */
/* Font size
----------------------------------------------- */
.fs-12 {
  font-size: 12px;
}
.fs-13 {
  font-size: 13px;
}
.fs-14 {
  font-size: 14px;
}
.fs-15 {
  font-size: 15px;
}
.fs-16 {
  font-size: 14px;
}
.fs-17 {
  font-size: 17px;
}
.fs-18 {
  font-size: 18px;
}
.fs-19 {
  font-size: 19px;
}
.fs-20 {
  font-size: 20px;
}
.fs-asterisk {
  font-size: 120%;
  display: inline-block;
  line-height: 1;
}

/* Font color
----------------------------------------------- */
.fc-red {
  color: $DANGER_COLOR;
}
.fc-blue {
  color: $LINK_COLOR;
}
.fc-mute {
  color: $MUTE_COLOR;
}

/* Font family
----------------------------------------------- */
.ff-serif {
  font-family: "游明朝", YuMincho, "ヒラギノ明朝 ProN W3", "Hiragino Mincho ProN", "HG明朝E", "ＭＳ Ｐ明朝", "ＭＳ 明朝", serif;
}
.ff-en {
  font-family: $FONT_FAMILY_EN;
}

/* Text align
----------------------------------------------- */
.ta-center {
    text-align: center;
}
.ta-left {
    text-align: left;
}
.ta-right {
    text-align: right;
}

/* Text indent
----------------------------------------------- */
.indent-1 {
  text-indent: 1em;
}

/* ==============================================
 04. Parts
=============================================== */
/* icon
----------------------------------------------- */
.svg-icon svg [fill]:not(.permanent):not(g) {
  @include transition;
  fill: #a5a4bf;
}

/* button
----------------------------------------------- */
.btn {
  display: inline-block;
  font-size: 14px;
  line-height: 1.5;
  padding: 9px 30px;
  text-align: center;
  white-space: nowrap;
  max-width: 100%;
  @include border-radius(.475rem);
  @include transition;
  &:not(.disabled):not(.btn-text):hover {
    transform: translateY(-1px);
    @include box-shadow(0 4px 8px rgba(33, 33, 43, .08), 0 3px 6px rgba(33, 33, 43, .06));
  }
  &.btn-lg {
    width: 200px;
    @include sp {
      width: 120px;
    }
  }
  &.btn-sm {
    padding: 9px 15px;
  }
  &.btn-h-sm {
    padding: 7px 30px;
  }
  @include sp {
    font-size: 13px;
    padding: 9px 16px;
  }
  &.disabled {
    cursor: default;
  }
}
.btn-primary {
  color: #fff;
  background: $LINK_COLOR;
  &:hover {
    color: #fff;
    background: $LINK_COLOR;
  }
  &.disabled {
    background: #A7A7A7;
  }
}
.btn-secondary {
  background: #8E8E8E;
  color: #fff;
  &:hover {
    background: #8E8E8E;
    color: #fff;
  }
}
.btn-more {
  background-color: #fff;
  border: 1px solid #ccc;
  color: #888888;
  width: 180px;
  @include sp {
    width: 120px;
  }
}
.btn-text {
  background: none;
  color: $FONT_COLOR;
  padding: 10px 15px;
  @include border-radius(0);
  &.disabled {
    color: $MUTE_COLOR;
    text-decoration: none;
  }
  &:hover {
    background: none;
  }
  @include sp {
    font-size: 13px;
    padding: 7px 0;
  }
}
.btn-text-underline {
  text-decoration: underline;
  @include pc {
    &:hover {
      text-decoration: none;
    }
  }
}
.btn-icon {
  background: none;
  padding: 0;
  min-width: 40px;
  &:hover {
    transform: translateY(0) !important;
    @include box-shadow(none !important);
  }
}
.btn-add {
  background-color: $DEEP_COLOR;
  height: 40px;
  position: relative;
  padding: 0;
  min-width: 40px;
  &::before, &::after {
    content: "";
    background-color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    @include transform(translate(-50%,-50%));
  }
  &::before {
    height: 2px;
    width: 16px;
  }
  &::after {
    height: 16px;
    width: 2px;
  }
}
.prev-page-btn {
  .btn {
    background-color: #fff;
    border: 2px solid $LINK_COLOR;
    color: $LINK_COLOR;
    font-size: 116%;
    font-weight: bold;
    padding: 7px 24px;
    @include border-radius(50px);
    .icon-arrow {
      background: url("../images/icon_back.svg") no-repeat 0 50%;
      background-size: 22px 22px;
      display: inline-block;
      padding: 3px 0 3px 30px;
      white-space: nowrap;
    }
  }
}

/* table
----------------------------------------------- */
.table {
  border-bottom: 1px solid $BORDER_COLOR;
  width: 100%;
  th, td {
    font-weight: normal;
    text-align: left;
    padding: 0.75rem 1.75rem;
    white-space: nowrap;
    @include sp {
      padding: 0.75rem 1.25rem;
    }
    &.row-wrap-normal {
      white-space: normal;
    }
    p {
      margin-bottom: 0;
    }
  }
  tr {
    th:first-child,
    td:first-child {
    }
  }
  &.align-middle {
    th, td {
      vertical-align: middle;
    }
  }
  &.table-row-dashed {
    border-collapse: collapse;
    tr:not(:last-child) {
      border-bottom: 1px dashed $BORDER_COLOR;
    }
  }
  thead {
    th {
      background-color: #B7B7B7;
      color: #fff;
      padding: .5rem 1.75rem;
      vertical-align: middle;
      @include sp {
        padding: .5rem 1.25rem;
      }
    }
  }
  tbody {
    tr:nth-child(odd) {
      background-color: #F5F5F5;
    }
    th, td {
      border-top: 1px solid $BORDER_COLOR;
    }
  }
}
.responsive-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
@include tab {
  .responsive-container-tab {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@include sp {
  .responsive-container-sp {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* page title
----------------------------------------------- */
.page-title {
  margin-bottom: 3rem;
  @include sp {
    margin-bottom: 2rem;
  }
  a:not(.btn) {
    color: $LINK_COLOR;
    text-decoration: underline;
    @include pc {
      &:hover {
        text-decoration: none;
      }
    }
  }
  .btn-primary {
    color: #fff;
    min-width: 160px;
    @include sp {
      min-width: none;
    }
  }
}
/* heading
----------------------------------------------- */
.heading-1 {
  border-left: 3px solid $MAIN_COLOR;
  color: $MAIN_COLOR;
  font-size: 20px;
  font-weight: bold;
  line-height: 1.25;
  padding-left: 1.75rem;
  position: relative;
  white-space: nowrap;
  @include sp {
    font-size: 18px;
    padding-left: 1.25rem;
  }
  .form-required {
    color: $DANGER_COLOR;
    display: inline-block;
    line-height: 1;
    margin-left: 0.5rem;
    font-size: 80%;
    vertical-align: top;
  }
}
.heading-2 {
  font-size: 16px;
  line-height: 1.4;
}

/* title
----------------------------------------------- */
.post-block-title {
  margin-bottom: 16px;
  margin-top: 44px;
}

/* scrollbox
----------------------------------------------- */
.scroll-area {
  overflow: auto;
  position: relative;
  &.ps--active-x {
    padding-bottom: 10px;
    .ps__rail-x {
     opacity: 1;
     .ps__thumb-x {
       height: 10px;
       height: 4px;
     }
   }
  }
}

/* pagination
----------------------------------------------- */
.pagination {
  font-size: 0;
  letter-spacing: 0;
  .page-item {
    font-size: 14px;
    display: inline-block;
    margin: 0 4px;
    vertical-align: middle;
    @include sp {
      font-size: 12px;
    }
    &:first-child {
      margin-left: auto;
    }
    &:last-child {
      margin-right: auto;
    }
    a, span {
      color: $MUTE_COLOR;
      display: inline-block;
      padding: 0;
      height: 32px;
      line-height: 32px;
      vertical-align: middle;
      @include sp {
        height: 30px;
        line-height: 30px;
      }
    }
    .current-page {
      border: 1px solid $FORM_COLOR;
      color: $MUTE_COLOR;
      height: 32px;
      line-height: 32px;
      text-align: center;
      padding: 0 10px;
      @include sp {
        height: 30px;
        line-height: 30px;
      }
    }
    .page-link {
      background-color: $FORM_COLOR;
      height: 32px;
      text-indent: -9999px;
      overflow: hidden;
      position: relative;
      width: 32px;
      @include sp {
        height: 30px;
        width: 30px;
      }
      &::after {
        content: "";
        position: absolute;
        top: 50%;
        @include transform(translateY(-50%));
      }
    }
    .page-prev {
      margin-right: 0.25rem;
      &::after {
        left: 14px;
        @include prev-arrow(#fff, 10px, 1px);
      }
    }
    .page-next {
      margin-left: 0.25rem;
      &::after {
        right: 14px;
        @include next-arrow(#fff, 10px, 1px);
      }
    }
  }
}

.page-controls {
  font-size: 14px;
  @include sp {
    font-size: 12px;
  }
}


/* dropdown
----------------------------------------------- */
.dropdown {
  position: relative;
  .dropdown-content {
    background: #fff;
    top: 0;
    left: 50%;
    z-index: 1000;
    display: none;
    width: 20rem;
    padding: 0;
    margin: .55rem 0 0;
    @include border-radius(3px);
    @include box-shadow(0 1px 5px 1px rgba(0,0,0,0.2));
    @include sp {
    }
  }
  &[aria-opened="true"] .dropdown-content {
    display: block;
  }
  .dropdown-contain {
    position: relative;
  }
  .dropdown-contain::after {
    position: absolute;
    display: block;
    content: '';
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 10px solid #fff;
    top: -20px;
    right: 50%;
    margin-right: -8px;
    pointer-events: none;
  }
  .dropdown-title {
    font-size: 13px;
    line-height: 1;
    padding: 12px 12px;
    border-bottom: 1px solid $BORDER_COLOR;
  }
  .dropdown-list {
    max-height: 200px;
    padding: 0.75rem 0;
    overflow: auto;
  }
  .dropdown-list a {
    display: block;
    font-size: 13px;
    line-height: 1.45;
    padding: 6px 14px 6px 8px;
    @include pc {
      &:hover {
        color: $LINK_COLOR;
      }
    }
  }
  .scroll-box .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
    background-color: rgba($FONT_COLOR, 0.55);
  }
  .link-all {
    background-color: $BASE_COLOR;
    display: block;
    font-size: 11px;
    text-align: center;
    padding: 1rem;
    text-decoration: underline;
    @include border-radius(0 0 3px 3px);
    @include pc {
      &:hover {
        text-decoration: none;
      }
    }
  }
}

/* ==============================================
 05. Form
=============================================== */
/* layout
----------------------------------------------- */
.form-inline {
  @include flex;
  @include align-items;
}
.form-primary {
  h3 {
    font-size: 18px;
    color: $LINK_COLOR;
    border-left: 3px solid $LINK_COLOR;
    line-height: 1.4;
    padding: 0 0 0 1.25rem;
    margin-bottom: 1.75rem;
    margin-top: 3.2rem;
    @include sp {
      font-size: 16px;
      margin-top: 4rem;
    }
    a {
      font-size: 14px;
      font-weight: normal;
      color: $LINK_COLOR;
      display: inline-block;
      line-height: 1;
      text-decoration: underline;
      white-space: nowrap;
      @include pc {
        &:hover {
          text-decoration: none;
        }
      }
      @include sp {

      }
    }
  }
  .form-block {
    padding-bottom: 3rem;
  }
  .form-group {
    @include flex;
    @include align-items;
    @include tab {
      display: block;
    }
    &.form-group-align-top {
      @include align-items(normal);
      .form-label {
        margin-top: 1rem;
        @include tab {
          margin-top: 0;
        }
      }
    }
    .form-required {
      color: $DANGER_COLOR;
      display: inline-block;
      line-height: 1;
      margin-left: 0.5rem;
      font-size: 115%;
    }
    .form-label {
      margin-bottom: 1.5rem;
      margin-right: 1.5rem;
      min-width: 160px;
      @include tab {
        margin-right: auto;
        margin-bottom: 0.75rem;
        margin-top: 1.25rem;
      }
    }
    .form-field {
      flex: 1;
      @include flex;
      @include align-items;
      @include flex-wrap;
      @include sp {
        .flex {
          min-width: 0px;
          .form-size-w380 {
            flex: 1;
            width: 100%;
          }
        }
      }
      > * {
        margin-bottom: 1.5rem;
      }
      .form-full-block {
        width: 100%;
      }
      .form-sp-full-block {
        @include sp {
          width: 100%;
        }
      }
      .form-control-text-large {
        max-width: 100%;
        width: 820px;
      }
      .form-control-text-full {
        flex: 1;
        @include sp {
          flex: initial;
          width: 100%;
        }
      }
      .form-field-label {
        color: $MUTE_COLOR;
        line-height: 1.35;
        white-space: nowrap;
        &.form-field-label--dark {
          color: $FONT_COLOR;
        }
      }
      .form-select {
        select {
          min-width: 120px;
        }
      }
      .form-size-minw4em {
        min-width: 4em;
        @include sp {

        }
      }
      .form-size-minw6em {
        min-width: 6em;
        @include sp {

        }
      }
      .form-size-minw12em {
        min-width: 12em;
        @include sp {

        }
      }
      .form-size-w140 {
        max-width: 100%;
        width: 140px;
        @include sp {

        }
      }
      .form-size-w200 {
        max-width: 100%;
        width: 200px;
        @include sp {
          flex: 1;
          width: auto;
        }
      }
      .form-size-w220 {
        max-width: 100%;
        width: 220px;
        @include sp {
          flex: 1;
          width: auto;
        }
      }
      .form-size-w280 {
        max-width: 100%;
        width: 280px;
        @include sp {
          width: 220px;
        }
      }
      .form-size-w380 {
        max-width: 100%;
        width: 380px;
      }
      .form-size-w520 {
        max-width: 100%;
        width: 520px;
      }
      .form-sp-full {
        @include sp {
          flex: none;
          width: 100%;
        }
      }
      .form-add-button {
        background-color: $DEEP_COLOR;
        height: 40px;
        position: relative;
        padding: 0;
        min-width: 40px;
        &::before, &::after {
          content: "";
          background-color: #fff;
          position: absolute;
          left: 50%;
          top: 50%;
          @include transform(translate(-50%,-50%));
        }
        &::before {
          height: 2px;
          width: 20px;
        }
        &::after {
          height: 20px;
          width: 2px;
        }
      }
      pre {
        margin: 0 auto;
        padding: 12px 12px;
        @include border-radius(6px);
      }
      &.form-field-password {
        input {
          margin-right: 2rem;
          @include sp {
            margin-right: auto;
            flex: none;
            width: 100%;
          }
        }
        .btn {
          @include sp {
            margin-left: auto;
            width: 160px;
          }
        }
      }
      &.form-disabled-control-field {
        .form-select {
          select {
            &[disabled="disabled"] {
              background-color: #f5f5f5;
            }
          }
        }
      }
      &.form-field-weekly {
        .form-select {
          select {
            width: 200px;
          }
        }
        .form-checkbox {
          .form-checkbox-label {
            .form-checkbox-text {
              height: 22px;
              padding-left: 0;
              width: 22px;
              vertical-align: middle;
            }
          }
        }
      }
      &.form-field-interval {
        .form-select {
          select {
            width: 200px;
            @include sp {
              width: auto;
            }
          }
        }
      }
      &.form-field-startdate {
        .form-select {
          select {
            width: 200px;
            @include sp {
              width: auto;
            }
          }
        }
        .form-checkbox {
          .form-checkbox-label {
            .form-checkbox-text {
              height: 22px;
              padding-left: 0;
              width: 22px;
              vertical-align: middle;
            }
          }
        }
      }
      &.form-field-reception {
        .form-datepicker {
          &::before {
            content: none;
          }
          &::after {
            background: url("../images/icon_calender_gray.svg") no-repeat 0 0;
            right: auto;
            left: 12px;
            margin-top: -10px;
            height: 20px;
            width: 20px;
          }
          input {
            padding-right: 12px;
            padding-left: 45px;
            width: 160px;
          }
        }
        .form-select {
          &::before {
            content: none;
          }
          &::after {
            background: url("../images/icon_clock_gray.svg") no-repeat 0 0;
            background-size: 20px 20px;
            right: auto;
            left: 12px;
            margin-top: -10px;
            top: 50%;
            border: none;
            height: 20px;
            width: 20px;
          }
          select {
            padding: 10px 12px 10px 45px;
            width: 160px;
            @include sp {
              width: auto;
            }
          }
        }
      }
    }
  }
  textarea.form-control:read-only {
    background-color: #F0F0F0;
    &:focus {
      border-color: $FORM_COLOR;
      @include box-shadow(none);
    }
  }
  .form-submit-buttons {
    .btn-submit {
      border: 2px solid $LINK_COLOR;
      width: 180px;
    }
    @include sp {
      .btn-submit {
        margin-left: auto;
        width: 160px;
      }
    }
  }
}

.form-horizon-list {
  @include flex;
  @include flex-wrap;
  li {
    margin-right: 4rem;
    width: auto;
    white-space: nowrap;
    @include sp {
    }
    &:last-child {
      margin-right: auto;
    }
  }
}
.form-vertical-list {
  li {
    margin-bottom: 2rem;
    &:last-child {
      margin-bottom: auto;
    }
  }
}

/* parts
----------------------------------------------- */
button{
  background-color: transparent;
  border: none;
  font-size: 100%;
  cursor: pointer;
  outline: none;
  padding: 0;
  -webkit-appearance: none;
  appearance: none;
}
/* form default */
input.form-control, input[type="number"].form-control,input[type="email"].form-control, button.form-control, textarea.form-control {
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  padding: 0;
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  @include sp {
    font-size: 13px;
  }
}
input[type="submit"],
input[type="button"],
button {
  background-color: transparent;
  border: none;
  cursor: pointer;
  outline: none;
  padding: 0;
  -webkit-appearance: none;
  appearance: none;
  -webkit-box-sizing: content-box;
  -webkit-appearance: button;
  appearance: button;
  border: none;
  box-sizing: border-box;
  cursor: pointer;
}
input[type="submit"]::-webkit-search-decoration,
input[type="button"]::-webkit-search-decoration {
  display: none;
}
input[type="submit"]:focus,
input[type="button"]:focus,
button:focus {
  outline-offset: -2px;
  outline: 0;
}
input[type="text"].form-control, input[type="number"].form-control, input[type="password"].form-control, input[type="email"].form-control, input[type="tel"].form-control, input[type="url"].form-control, textarea.form-control {
  background-color: #fff;
  border: none;
  padding: 10px 12px;
  @include border-radius(6px);
  max-width: 100%;
  width: 100%;
  @include sp {
  }
  &[readonly="readonly"] {
    background-color: #F0F0F0;
    &:focus {
      @include box-shadow(none);
    }
  }
}
input[type="text"].form-control:focus, input[type="number"].form-control:focus, input[type="password"].form-control:focus, input[type="email"].form-control:focus, input[type="tel"].form-control:focus, input[type="url"].form-control:focus, textarea.form-control:focus {
  @include box-shadow(0 1px 3px rgba($LINK_COLOR,.5) inset);
}
input[type="text"].form-zip {
  width: 140px;
}

input[type="text"].form-control:required, input[type="number"].form-control:required, input[type="password"].form-control:required, input[type="email"].form-control:required, input[type="tel"].form-control:required, input[type="url"].form-control:required, textarea.form-control:required {
}

/* password */
.form-input--password {
  position: relative;
  &::before {
    content: "";
    background: #fff url("../images/common/icon_password01.png") no-repeat 0 0 / 15px 19px;
    height: 19px;
    left: 16px;
    position: absolute;
    top: 50%;
    margin-top: -9px;
    width: 15px;
    z-index: 1;
  }
  &::after {
    background-color: #cccccc;
    content: "";
    height: 70%;
    position: absolute;
    left: 47px;
    top: 15%;
    width: 1px;
    z-index: 1;
  }
  input.form-control {
    padding-left: 60px;
  }
}

/* checkbox */
.form-checkbox {
  display: inline-block;
  position: relative;
  input[type="checkbox"].form-checkbox-input {
    position: absolute;
    display: none;
    z-index: -1;
  }
  .form-checkbox-text {
    display: inline-block;
    position: relative;
    padding-left: 30px;
    line-height: 1.6;
    @include sp {
      padding-top: 1px;
    }
    &::before {
      content: "";
      background-color: #fff;
      border: 1px solid $BORDER_COLOR;
      height: 22px;
      position: absolute;
      left: 0;
      top: 0;
      width: 22px;
      @include border-radius(3px);
    }
  }
  input[type="checkbox"].form-checkbox-input:checked + .form-checkbox-text {
    &::before {
      background-color: $LINK_COLOR;
      border: 2px solid $LINK_COLOR;
    }
    &::after {
      content: "";
      position: absolute;
      left: 4px;
      top: 5px;
      width: 1em;
    	height: 0.5em;
    	border-bottom: 0.2em solid #fff;
    	border-left: 0.2em solid #fff;
      -webkit-transform: rotate(-45deg);
    	transform: rotate(-45deg);
    }
  }
  &.form-checkbox-no-text {
    .form-checkbox-label {
      display: block;
    }
    .form-checkbox-text {
      padding-left: 0;
      height: 22px;
      width: 22px;
      display: block;
    }
  }
}

/* radio */
.form-radio {
  display: inline-block;
  position: relative;
  input[type="radio"].form-radio-input {
    position: absolute;
    display: none;
    z-index: -1;
  }
  .form-radio-text {
    display: inline-block;
    position: relative;
    padding-left: 30px;
    line-height: 1.6;
    @include sp {
      padding-top: 1px;
    }
    &::before {
      content: "";
      background-color: #fff;
      border: 2px solid $LINK_COLOR;
      height: 22px;
      position: absolute;
      left: 0;
      top: 0;
      width: 22px;
      @include border-radius(50%);
    }
  }
  input[type="radio"].form-radio-input:checked + .form-radio-text {
    &::before {
      background-color: $LINK_COLOR;
      border: 2px solid $LINK_COLOR;
    }
    &::after {
      background-color: #fff;
      content: "";
      position: absolute;
      left: 6px;
      top: 6px;
      width: 10px;
    	height: 10px;
      @include border-radius(50%);
    }
  }
}

/* selectbox */
.form-select {
  display: block;
  position: relative;
  width: auto;
  font-size: 14px;
  border: none;
  @include border-radius(6px);
  background: #fff;
  overflow: hidden;
  @include sp {
    font-size: 13px;
  }
  &::before {
    background-color: #7C7C7C;
    content: "";
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 40px;
  }
  &::after {
    content: "";
    position: absolute;
    border-style: solid;
    border-width: 11px 8px 0 8px;
    border-color: #fff transparent transparent transparent;
    right: 12px;
    top: 50%;
    margin-top: -5px;
    z-index: 1;
  }
  select {
    color: $FONT_COLOR;
    background: none;
    width: 100%;
    font-size: 14px;
    padding: 10px 55px 10px 12px;
    border: none;
    position: relative;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
    border-radius: 0;
    border: 0;
    line-height: 1.5;
    background: none transparent;
    vertical-align: middle;
    outline: inherit;
    position: relative;
    z-index: 2;
    @include sp {
      font-size: 13px;
    }
    &.no-value {
      color: $MUTE_COLOR;
    }
    &.w-280 {
      max-width: 100%;
      width: 280px;
    }
  }
  select.form-control-auto {
    width: auto;
  }
  select:not(:target) {
    width: 120% \9;
  }
  .ui-multiselect {
    border: none;
    font-size: 14px;
    padding: 9px 50px 9px 10px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
    z-index: 2;
    @include border-radius(0);
    .ui-multiselect-open {
      display: none;
    }
    &::before {
      background-color: #7C7C7C;
      content: "";
      height: 100%;
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 40px;
    }
    &::after {
      content: "";
      position: absolute;
      border-style: solid;
      border-width: 10px 8px 0 8px;
      border-color: #fff transparent transparent transparent;
      right: 12px;
      top: 50%;
      margin-top: -5px;
      z-index: 1;
    }
  }
  .ui-state-default {
    background-color: #fff;
  }
  .ui-state-active {
    background-color: $LINK_COLOR;
    color: #fff !important;
  }
  .no-value + .ui-multiselect {
    color: $MUTE_COLOR;
  }
  .select2-container {
    background: none;
    width: 100%;
    font-size: 14px;
    padding: 9px 50px 9px 10px;
    border: none;
    position: relative;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    -moz-appearance: none;
    -webkit-appearance: none;
    appearance: none;
    border-radius: 0;
    border: 0;
    line-height: 1.5;
    background: none transparent;
    vertical-align: middle;
    outline: inherit;
    position: relative;
    z-index: 2;
    @include sp {
      font-size: 13px;
    }
    .select2-selection {
      border: none;
      height: auto;
      @include border-radius(0);
    }
    .select2-selection__rendered {
      color: $FONT_COLOR;
      padding: 0;
      line-height: 1.5;
    }
  }
}

/* datepicker */
.form-datepicker {
  display: inline-block;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  @include border-radius(6px);
  &::before {
    content: "";
    background-color: $FORM_COLOR;
    position: absolute;
    top: 0;
    height: 100%;
    right: 0;
    bottom: 0;
    width: 40px;
  }
  &::after {
    background: url("../images/icon_calender_white.svg") no-repeat 0 0;
    content: "";
    height: 15px;
    top: 50%;
    position: absolute;
    margin-top: -8px;
    right: 12px;
    width: 15px;
  }
  input[type="text"].form-control {
    background: none;
    border-color: $FORM_COLOR;
    padding-right: 50px;
    width: 12em;
    max-width: 100%;
    position: relative;
    z-index: 1;
    @include sp {
      width: 40vw;
    }
  }
  .picker {
    color: $FONT_COLOR;
    font-size: 15px;
    font-family: $FONT_FAMILY_EN;
    .picker__year {
      font-size: 100%;
      font-style: normal;
      color: $FONT_COLOR;
    }
    .picker__nav--prev:before {
      border-right-color: $DEEP_COLOR;
    }
    .picker__nav--next:before {
      border-left-color: $DEEP_COLOR;
    }
    .picker__button--close:before {
      top: .2em;
    }
    .picker__button--clear, .picker__button--close, .picker__button--today {
      color: $FONT_COLOR;
    }
  }
}

.form-keyword {
  white-space: nowrap;
  input[type="text"].form-control {
    border-color: $FORM_COLOR;
    border-right: none;
    width: 240px;
    @include border-radius(6px 0 0 6px);
  }
  button {
    background-color: $FORM_COLOR;
    border: 1px solid $FORM_COLOR;
    color: #fff;
    padding: 9px 10px;
    @include border-radius(0 6px 6px 0);
  }
}

.form-note {
  margin: 0;
  line-height: 1.6;
  font-size: 12px;
}

.form-file {
  cursor: pointer;
  background-color: #fff;
  border: 1px solid $FORM_COLOR;
  max-width: 100%;
  position: relative;
  width: 100%;
  overflow: hidden;
  @include border-radius(6px);
  &::after {
    background-color: $FORM_COLOR;
    content: "";
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 40px;
  }
  input[type="file"].form-control {
    display: none;
  }
  .form-file-text {
    display: block;
    padding: 9px 50px 9px 10px;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    &::before, &::after {
      content: "";
      background-color: #fff;
      position: absolute;
      top: 50%;
      @include transform(translateY(-50%));
      z-index: 1;
    }
    &::before {
      height: 2px;
      right: 10px;
      width: 18px;
    }
    &::after {
      height: 18px;
      right: 18px;
      width: 2px;
    }
  }
}

/* placeholder */
input::placeholder, textarea::placeholder  {
  font-family: $FONT_FAMILY_SANS;
  color: #aaa;
  opacity: 1;
}
/* IE */
input:-ms-input-placeholder, textarea:-ms-input-placeholder {
  font-family: $FONT_FAMILY_SANS;
  color: #aaa;
  opacity: 1;
}
/* Edge */
input::-ms-input-placeholder, textarea::-ms-input-placeholder {
  font-family: $FONT_FAMILY_SANS;
  color: #aaa;
  opacity: 1;
}

/* Chrome, Safari */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
/* Firefox, IE */
input[type="number"] {
  -moz-appearance:textfield;
}

/* Chrome autofill */
input:-webkit-autofill, input:-webkit-autofill:hover, input:-webkit-autofill:focus, input:-webkit-autofill:active  {
    -webkit-box-shadow: 0 0 0px 1000px #fff inset !important;
}

/* ==============================================
 06. Dialog
=============================================== */
.custom-dialog {
  background-color: $BASE_COLOR;
  border: none !important;
  max-width: 90%;
  padding: 0;
  z-index: 1001 !important;
  @include border-radius(4px);
  .ui-widget-content {
    background: none;
  }
  .ui-widget-header {
    background-color: #fff;
    border-radius: 0px;
    border: none;
    padding: 1.75rem 3rem;
    @include border-radius(4px 4px 0 0);
    @include sp {
      padding: 1.25rem 2rem;
    }
    .ui-dialog-title {
      font-size: 140%;
      font-weight: normal;
      color: $MAIN_COLOR;
      @include sp {
        font-size: 120%;
      }
    }
    .ui-dialog-titlebar-close {
      display: none;
    }
  }
  .custom-dialog-wrapper {
    padding: 2rem 3rem;
    @include sp {
      padding: 2rem;
    }
  }
  .custom-dialog-message {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 2rem;
    @include sp {
      font-size: 14px;
    }
  }
  .custom-dialog-form {
    .form-group {
      margin-bottom: 1.25rem;
      @include sp {
        display: block;
      }
      .form-field {
        @include flex;
        @include align-items;
        @include flex-wrap;
        @include sp {
          display: block;
        }
        input[type="text"].form-control,
        input[type="number"].form-control,
        input[type="tel"].form-control,
        textarea.form-control {
          flex: 1;
        }
        input[type="text"].form-control:focus,
        input[type="number"].form-control:focus,
        input[type="tel"].form-control:focus,
        textarea.form-control:focus {
          border-color: $DEEP_COLOR;
        }
        .form-field-label {
          white-space: nowrap;
        }
        .btn-delete {
          @include sp {
            @include flex;
            -webkit-box-pack: end;
            -ms-flex-pack: end;
            justify-content: flex-end;
            -ms-flex-negative: 0;
            flex-shrink: 0;
          }
        }
      }
    }
  }
  .ui-dialog-buttonpane {
    border-top: none;
    margin-top: auto;
    padding: 0 3rem 2rem;
    @include border-radius(0 0 4px 4px);
    @include sp {
      padding: 0 2rem 2rem;
    }
    .ui-dialog-buttonset {
      .btn {
        font-family: $FONT_FAMILY_SANS;
        border: none;
        margin: 1rem;
        @include sp {
          margin: 0.75rem;
        }
        &:last-child {
          margin-right: auto;
        }
      }
      .btn-primary {
        color: #fff;
        min-width: 140px;
        @include sp {
          min-width: 100px;
        }
        &:hover {
          color: #fff;
        }
      }
      .btn-cancell {
        color: #fff;
        background-color: #E67E7E;
      }
    }
  }
  &.custom-dialog-center {
    text-align: center;
    .custom-dialog-inner {
      padding: 2rem 0 1rem;
      @include sp {
        padding-bottom: 0;
        p {
          margin-bottom: 1rem;
        }
      }
    }
    .ui-dialog-buttonpane {
      padding-bottom: 3rem;
      @include flex;
      @include justify-content;
      .ui-dialog-buttonset {
        float: none;
        .btn {
          margin: .5rem 1rem;
          min-width: 140px;
          @include sp {
            min-width: 100px;
            margin: .5rem;
          }
        }
      }
    }
  }
}
.ui-widget-overlay {
  background: rgba(#000, 1);
  z-index: 1000 !important;
}

#dialog-reserve-opetation-time {
  .reserve-date-target {
    background-color: #fff;
    @include flex;
    @include align-items;
    padding: 2rem 2rem;
    margin-bottom: 3rem;
    white-space: nowrap;
    @include sp {
      padding: 2rem 1.25rem;
    }
    .label {
      width: 200px;
      @include sp {
        margin-right: auto;
        width: auto;
      }
    }
    .target {
      flex: 1;
      @include sp {
        margin-left: auto;
      }
    }
  }
  .reserve-time-list {
    background-color: #fff;
    padding: 2rem 2rem;
    @include sp {
      padding: 1.25rem;
    }
    li {
      white-space: nowrap;
      margin-bottom: 2rem;
      @include flex;
      @include align-items;
      width: 100%;
      @include sp {
        display: block;
      }
      &:last-child {
        margin-bottom: 0;
      }
      .time-range {
        width: 200px;
        @include sp {
          font-weight: bold;
          width: auto;
          margin-bottom: .5rem;
        }
      }
      .receiving-quantity {
        margin-right: 3rem;
        @include flex;
        @include align-items;
        @include sp {
          padding-left: 1.5rem;
          margin-right: auto;
          margin-bottom: .5rem;
        }
        span {
          border: 1px solid $BORDER_COLOR;
          background-color: #f5f5f5;
          display: inline-block;
          padding: 5px 15px;
          line-height: 1.5;
          @include border-radius(3px);
          @include sp {
            background-color: #fff;
            border: none;
            display: block;
            padding: 0;
            @include border-radius(0);
          }
        }
      }
      .receiving-number {
        @include flex;
        @include align-items;
        @include sp {
          padding-left: 1.5rem;
        }
        input.form-control {
          border: 1px solid $BORDER_COLOR;
          padding: 5px 8px;
          @include border-radius(3px);
          &:focus {
            border-color: $LINK_COLOR;
            @include box-shadow(none);
          }
        }
      }
    }
  }
}

/* ==============================================
 11. Header
=============================================== */
.header {
  background-color: #fff;
  height: 55px;
  margin-bottom: 5rem;
  position: relative;
  z-index: 100;
  .container {
    @include sp {
      padding-right: 5px;
    }
  }
  @include tab {
    margin-bottom: 3rem;
  }
  .header-title {
    margin-right: 3rem;
    height: 55px;
    @include tab {
      margin-right: 1.5rem;
    }
    a {
      display: block;
      img {
        display: block;
        @include tab {
          width: 140px;
        }
        @include sp {
          width: 110px;
        }
      }
    }
  }
  .header-user-name {
    height: 55px;
    line-height: 1.45;
    margin-left: auto;
    white-space: nowrap;
    @include tab {
      font-size: 85%;
    }
    .text {
    }
  }
  .header-navigation {
    @include sp {
      background-color: #fff;
      position: absolute;
      top: 100%;
      left: 0;
      height: 0;
      opacity: 0;
      visibility: hidden;
      width: 100%;
      @include transition;
    }
    ul {
      @include sp {
        border-top: 1px solid $BORDER_COLOR;
        display: block;
      }
      li {
        margin-right: 3rem;
        @include sp {
          border-bottom: 1px solid $BORDER_COLOR;
          margin-right: auto;
        }
        &:last-child {
          margin-right: auto;
        }
        a {
          color: $LINK_COLOR;
          display: block;
          height: 55px;
          line-height: 55px;
          position: relative;
          @include sp {
            height: auto;
            line-height: 1.4;
            padding: 18px 12px;
            &::after {
              right: 14px;
              @include next-arrow($LINK_COLOR, 7px, 2px);
            }
          }
          &.is-current {
            font-weight: bold;
            &::before {
              background-color: $LINK_COLOR;
              content: "";
              height: 2px;
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              @include sp {
                content: none;
              }
            }
          }
        }
      }
    }
  }
  .header-nav-btn {
    display: none;
    @include sp {
      display: block;
    }
    button {
      background-color: #fff;
      border: none;
      outline: 0;
      padding: 0;
      display: block;
      position: relative;
      height: 45px;
      width: 45px;
      span {
        display: block;
        height: 3px;
        left: 10px;
        width: 25px;
        background-color: $FORM_COLOR;
        position: absolute;
        @include transition;
        &:nth-child(1) {
          top: 12px;
        }
        &:nth-child(2) {
          top: 21px;
        }
        &:nth-child(3) {
          top: 30px;
        }
      }
    }
  }
}

.is-nav-open {
  .header {
    .header-navigation {
      opacity: 1;
      height: auto;
      visibility: visible;
    }
    .header-nav-btn {
      button {
        span {
          &:nth-child(1) {
            transform: translateY(9px) rotate(-45deg);
          }
          &:nth-child(2) {
            opacity: 0;
          }
          &:nth-child(3) {
            transform: translateY(-9px) rotate(45deg);
          }
        }
      }
    }
  }
}

/* ==============================================
 12. Breadcrumbs
=============================================== */
.breadcrumbs {
  margin-top: 1rem;
  margin-bottom: 2.5rem;
  font-size: 0;
  letter-spacing: 0;
  @include sp {
    margin-bottom: 2rem;
  }
  .container {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    > span {
      font-size: 13px;
      @include sp {
        font-size: 11px;
      }
      > a, > span {
        display: inline-block;
        position: relative;
        margin-left: 25px;
        &::before {
          content: "";
          position: absolute;
          left: -17px;
          @include next-arrow($FONT_COLOR, 6px, 1px);
        }
      }
      a.home {
        margin-left: auto;
        &::before {
          content: none;
        }
      }
    }
  }
}
.page-back-link {
  margin-bottom: 1rem;
  a {
    background: url("../images/icon_back_dark.svg") no-repeat 0 2px;
    display: inline-block;
    padding: 0 0 0 23px;
  }
}

/* ==============================================
 13. Aside
=============================================== */

/* ==============================================
 14. Footer
=============================================== */
/* common
----------------------------------------------- */

/* ==============================================
 20. Calender setting
=============================================== */
#js-calender {
  #calender-header {
    margin-bottom: 1.25rem;
    @include flex;
    @include align-items;
    a {
      cursor: pointer;
      background: none;
      padding: 10px;
      position: relative;
      &.is-disabled {
        pointer-events: none;
      }
    }
    #calendar-prev-button {
      &::after {
        left: 10px;
        @include prev-arrow($LINK_COLOR, 9px, 2px);
      }
      &.is-disabled {
        &::after {
          @include prev-arrow($BORDER_COLOR, 9px, 2px);
        }
      }
    }
    #calendar-next-button {
      &::after {
        right: 10px;
        @include next-arrow($LINK_COLOR, 9px, 2px);
      }
      &.is-disabled {
        &::after {
        @include next-arrow($BORDER_COLOR, 9px, 2px);
        }
      }
    }
    #calender-month {
      font-size: 18px;
      font-weight: bold;
      margin: 0 1rem;
    }
  }
  #calendar {
    text-align: left;
    width: 100%;
    table {
      border-collapse: collapse;
      width: 100%;
      th, td {
        border: 1px solid $BORDER_COLOR;
        outline: none;
        padding: 8px;
        text-align: left;
      }
      th {
        background-color: $BASE_COLOR;
        font-weight: normal;
        text-align: center;
      }
      tr.dayOfWeek {
        th:nth-child(6n) {
          color: #074DBC;
        }
        th:nth-child(7n) {
          color: $DANGER_COLOR;
        }
      }
      td.disabled {
        background-color: #f5f5f5;
      }
      td.today {
      }
      td {
        .col-day {
          text-align: right;
          margin-bottom: .75rem;
        }
        .col-count-btn {
          margin-bottom: 1.25rem;
          .btn {
            font-size: 85%;
            padding: 5px 12px;
          }
        }
        .form-checkbox {
          font-size: 85%;
          margin-bottom: 1rem;
          .form-checkbox-label {
            margin-left: 1rem;
            .form-checkbox-text {
              padding: 0;
              height: 20px;
              width: 20px;
              vertical-align: middle;
              &::before {
                height: 20px;
                width: 20px;
              }
            }
          }
        }
      }
      td.day {
        background-position: 8px 8px;
        background-repeat: no-repeat;
        &.reserve-status-1 {
          background-image: url("../images/day_mark01.svg");
          background-size: 16px 16px;
        }
        &.reserve-status-2 {
          background-image: url("../images/day_mark02.svg");
          background-size: 16px 16px;
        }
        &.reserve-status-3 {
          background-image: url("../images/day_mark03.svg");
          background-size: 16px 16px;
        }
        &.reserve-status-none {
          background-image: url("../images/day_mark04.svg");
          background-size: 13px 13px;
          background-position: 10px 10px;
        }
        &.reserve-status-tel {
          background-image: url("../images/day_mark05.svg");
          background-size: 24px 12px;
          background-position: 8px 10px;
        }
      }
    }
  }
}



/* ==============================================
 31. Login
=============================================== */
.login-card {
  margin-left: auto;
  margin-right: auto;
  min-height: 100vh;
  min-height: calc(var(--vh, 1vh) * 100);
  width: 100vw;
  @include flex;
  @include align-items;
  @include justify-content;
  .login-card-inner {
    width: 370px;
    max-width: 100%;
    @include sp {
    }
  }
  h1 {
    color: $MAIN_COLOR;
    font-size: 18px;
    letter-spacing: 0.08em;
    margin-bottom: 34px;
    text-align: center;
    img {
      display: block;
      margin: 0 auto;
    }
  }
  .login-card-body {
    .form-block {
      margin-bottom: 30px;
      @include sp {
      }
      .form-inner {
        background: #fff;
        overflow: hidden;
        margin-bottom: 35px;
        @include sp {
        }
        @include border-radius(10px);
        @include box-shadow(0 8px 15px #E7EAF0);
      }
      .form-field {
        .form-login-input {
          position: relative;
          padding: 12px 12px 12px 65px;
          &.form-login-input--email {
            background: url("../images/icon_member_gray.svg") no-repeat 21px 50%;
            background-size: 23px 23px;
          }
          &.form-login-input--password {
            background: url("../images/icon_password_gray.svg") no-repeat 22px 50%;
            background-size: 22px 25px;
          }
          .form-control {
            background: none;
            border: none;
            padding: 18px 0 4px;
            position: relative;
            z-index: 1;
            &:focus, &.form-entered {
              background: none;
              @include box-shadow(none);
              + .input-name {
                color: $DEEP_COLOR;
                top: 12px;
                z-index: 2;
              }
            }
          }
          .input-name {
            color: #a8adb7;
            font-family: $FONT_FAMILY_EN;
            font-weight: 500;
            font-size: 12px;
            letter-spacing: 0.07em;
            left: 65px;
            position: absolute;
            top: 16px;
            @include transition;
          }
        }
      }
      .form-btn-area {
        text-align: center;
        .btn {
          background: $MAIN_COLOR url("../images/btn_arrow01.svg") no-repeat right 10px top 50%;
          background-size: 32px 32px;
          color: #fff;
          display: block;
          font-size: 14px;
          font-weight: bold;
          letter-spacing: 0.06em;
          padding: 16px;
          width: 100%;
        }
      }
    }
  }
  .reset-login-password {
    text-align: center;
    font-size: 85%;
    a {
      color: $MUTE_COLOR;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

/* ==============================================
 Media Queries
=============================================== */
@media screen and (max-width: 767px) {
  a[href^="tel:"] {
    cursor: pointer;
    pointer-events: auto;
  }
}

/* ==============================================
 Media Queries
=============================================== */
/* hidden
----------------------------------------------- */
@media screen and (min-width: 992px) {
  .hidden-lg {
    display: none !important
  }
}
@media screen and (min-width: 768px) and (max-width: 991px) {
  .hidden-md {
    display: none !important
  }
}
@media screen and (min-width: 544px) and (max-width: 767px) {
  .hidden-sm {
    display: none !important
  }
}
@media screen and (max-width: 543px) {
  .hidden-xs {
    display: none !important
  }
}


/* column
----------------------------------------------- */
.col-lg-1 {
    width: 12%;
}
.col-lg-2 {
    width: 16.6666%;
}
.col-lg-3 {
    width: 25%;
}
.col-lg-4 {
    width: 33.3333%;
}
.col-lg-5 {
    width: 41.6666%;
}
.col-lg-6 {
    width: 50%;
}
.col-lg-7 {
    width: 57.0833%;
}
.col-lg-8 {
    width: 66.6667%;
}
.col-lg-9 {
    width: 75%;
}
.col-lg-10 {
    width: 83.3334%;
}
.col-lg-11 {
    width: 91.6666%;
}
.col-lg-12 {
    width: 100%;
}


/* pc min
----------------------------------------------- */
@media screen and (max-width: 1119px) {

  /* column
  ----------------------------------------------- */
  .col-md-1 {
      width: 12%;
  }
  .col-md-2 {
      width: 16.6666%;
  }
  .col-md-3 {
      width: 25%;
  }
  .col-md-4 {
      width: 33.3333%;
  }
  .col-md-5 {
      width: 41.6666%;
  }
  .col-md-6 {
      width: 50%;
  }
  .col-md-7 {
      width: 57.0833%;
  }
  .col-md-8 {
      width: 66.6667%;
  }
  .col-md-9 {
      width: 75%;
  }
  .col-md-10 {
      width: 83.3334%;
  }
  .col-md-11 {
      width: 91.6666%;
  }
  .col-md-12 {
      width: 100%;
  }

}

/* tablet
----------------------------------------------- */
@media screen and (max-width: 991px) {
    body.sp-nav-open {
      overflow: hidden !important;
    }

    /* ==============================================
     Layout
    =============================================== */

    /* column
    ----------------------------------------------- */
    .col-sm-1 {
        width: 12%;
    }
    .col-sm-2 {
        width: 16.6666%;
    }
    .col-sm-3 {
        width: 25%;
    }
    .col-sm-4 {
        width: 33.3333%;
    }
    .col-sm-5 {
        width: 41.6666%;
    }
    .col-sm-6 {
        width: 50%;
    }
    .col-sm-7 {
        width: 57.0833%;
    }
    .col-sm-8 {
        width: 66.6667%;
    }
    .col-sm-9 {
        width: 75%;
    }
    .col-sm-10 {
        width: 83.3334%;
    }
    .col-sm-11 {
        width: 91.6666%;
    }
    .col-sm-12 {
        width: 100%;
    }

}
/* column
----------------------------------------------- */
@media screen and (max-width: 767px) {

    /* column
    ----------------------------------------------- */
    .col-xs-1 {
        width: 12%;
    }
    .col-xs-2 {
        width: 16.6666%;
    }
    .col-xs-3 {
        width: 25%;
    }
    .col-xs-4 {
        width: 33.3333%;
    }
    .col-xs-5 {
        width: 41.6666%;
    }
    .col-xs-6 {
        width: 50%;
    }
    .col-xs-7 {
        width: 57.0833%;
    }
    .col-xs-8 {
        width: 66.6667%;
    }
    .col-xs-9 {
        width: 75%;
    }
    .col-xs-10 {
        width: 83.3334%;
    }
    .col-xs-11 {
        width: 91.6666%;
    }
    .col-xs-12 {
        width: 100%;
    }
}

@media print {
    body {
        zoom: 75%;
    }
}
