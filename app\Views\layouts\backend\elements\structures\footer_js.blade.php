<?php
$jsFiles = [
    'vendor/app_custom',
    'vendor/popper.min',
    'vendor/bootstrap.min',
    'vendor/bootstrap-datepicker.min',
    'vendor/bootstrap-datepicker.ja.min',
    'vendor/utils/loadingoverlay.min',
    'vendor/utils/loadingoverlay_progress.min',
    'vendor/utils/moment.min',
    'vendor/utils/min',
    'vendor/utils/common',
    'vendor/utils/xhr',
    'vendor/utils/system',
    'vendor/bootstrap-timepicker.min',
    'vendor/ja',
    'vendor/jquery.maskedinput.min',
    'vendor/jquery-ui.min',
    'vendor/jquery.timepicker',
    'vendor/jquery.ui.touch-punch.min',
    'vendor/perfect-scrollbar.min',
    'vendor/pickadate/picker',
    'vendor/pickadate/picker.date',
    'vendor/pickadate/picker.time',
    'vendor/pickadate/legacy',
    'vendor/pickadate/lang-ja',
    'vendor/form',
    'vendor/common',
];
?>
{!! loadFiles($jsFiles, $area, 'js') !!}

@php
    $constant = config('constant');
@endphp
<script>
    var $constant = {!! json_encode($constant) !!};
</script>

@include('layouts.elements.footer_autoload')
<script>
    $('#sandbox-container .input-daterange').datepicker({
        language: "ja",
        todayHighlight: true
    });
</script>
