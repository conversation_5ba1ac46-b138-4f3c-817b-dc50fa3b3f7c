APP_ENV=local
APP_KEY=base64:sOr6MCzz1zb2e+I/S02DrRCbN+gwXUhMFmNOcS4UHx0=
APP_DEBUG=true


# domain
USE_SSL=false
APP_URL=http://reserve.local
API_DOMAIN=http://reserve.local
BACKEND_DOMAIN=http://reserve.local
FRONTEND_DOMAIN=http://reserve.local
API_ALIAS="v1"
BACKEND_ALIAS="management"
FRONTEND_ALIAS="/"

#fill cloudwatch for save the logs to cloudwatch
DEFAULT_STORAGE="public" #Supported Drivers: "public", "ftp", "s3", "rackspace"
APP_LOG_TYPE=local
APP_LOG_LEVEL=debug
SQL_LOG=false
APP_LOG_PARAM=false
ENABLED_LOG_VIEWER=false

#DB
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=reserve_one
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_DRIVER=sync

# For Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# For Mailer
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=paraline2017
MAIL_ENCRYPTION=tls
MAIL_FROM=info@localhost
MAIL_FROM_NAME="App Name"
MAIL_TO=

# For FireBase
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=

# For S3
AWS_KEY=""
AWS_SECRET=""
AWS_REGION=""
AWS_BUCKET=""

# For CloudWatch
AWC_KEY=""
AWC_SECRET=""
AWC_REGION=""
AWC_GROUP_NAME=""
AWC_RETENTION_DAYS="360"

# For Webpack
USE_WEBPACK=false

# For payment
# bundle id
APPLE_BUNDLE_ID="com.mindmobapp.MindMob"
GOOGLE_BUNDLE_ID="com.example.app"

APPLE_PRODUCT_ID="com.mindmobapp.MindMob"
GOOGLE_PRODUCT_ID="com.example.app"
# password
APPLE_SHARED_SECRET="1234..."
 # sandbox | production
APPLE_MODE="sandbox"
GOOGLE_MODE="sandbox"

# chatwork
CHATWORK_ROOM_ID=
CHATWORK_TOKEN=
