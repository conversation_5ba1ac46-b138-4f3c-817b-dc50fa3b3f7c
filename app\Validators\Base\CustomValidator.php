<?php namespace App\Validators\Base;

use App\Model\Base\Base;
use App\Model\Base\ModelSoftDelete;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Validator;
use Carbon\Carbon;

/**
 * Class CustomValidator
 * @package App\Validator
 */
class CustomValidator extends Validator
{
    /**
     * @var array
     */
    private $_customMessages = [
        'greater_than' => 'The :attribute format is invalid !',
        'greater_than_equal' => 'The :attribute format is invalid !',
        'greater_than_equal_for_time_stamp' => 'The :attribute format is invalid !',
        'not_japanese' => ':attributeは不正な値です。',
    ];

    /**
     * CustomValidator constructor.
     * @param \Illuminate\Contracts\Translation\Translator $translator
     * @param array $data
     * @param array $rules
     * @param array $messages
     * @param array $customAttributes
     */
    public function __construct($translator, $data, $rules,
                                $messages = array(), $customAttributes = array())
    {
        parent::__construct($translator, $data, $rules, $messages, $customAttributes);
        $this->_setCustomMessages();
    }

    /**
     * Setup custom error messages
     */
    protected function _setCustomMessages()
    {
        $this->setCustomMessages($this->_customMessages);
    }

    /**
     * Custom validator method
     */
    public function validateGreaterThan($attribute, $value, $parameters)
    {
        return $value > $parameters[0];
    }
    /**
     * Custom validator method
     */
    public function validateLessThan($attribute, $value, $parameters)
    {
        return $value < $parameters[0];
    }
    /**
     * Custom validator method
     */
    public function validateLessOrEqualThan($attribute, $value, $parameters)
    {
        return $value <= $parameters[0];
    }

    /**
     * Custom validator method
     */
    public function validateGreaterThanField($attribute, $value, $parameters, $validator)
    {
        $field = $parameters[0];
        $data = $validator->getData();
        $otherValue = Arr::get($data, $field);
        return $value > $otherValue;
    }

    /**
     * Custom validator method
     */
    public function validateGreaterThanOrEqualField($attribute, $value, $parameters, $validator)
    {
        $field = $parameters[0];
        $data = $validator->getData();
        $otherValue = Arr::get($data, $field);
        return $value >= $otherValue;
    }

    /**
     * Custom validator method
     */
    public function validateGreaterThanOrEqualTimeField($attribute, $value, $parameters, $validator)
    {
        $field = $parameters[0];
        $data = $validator->getData();
        $otherValue = Arr::get($data, $field);
        strlen($value) == 4 ? $value = '0' . $value : null;
        strlen($otherValue) == 4 ? $otherValue = '0' . $otherValue : null;
        return $value >= $otherValue;
    }

    /**
     * Custom validator method
     */
    public function validateLessThanField($attribute, $value, $parameters, $validator)
    {
        $field = $parameters[0];
        $data = $validator->getData();
        $otherValue = Arr::get($data, $field);
        return $value < $otherValue;
    }

    /**
     * Custom validator method
     */
    public function validateLessOrEqualThanField($attribute, $value, $parameters, $validator)
    {
        $field = $parameters[0];
        $data = $validator->getData();
        $otherValue = Arr::get($data, $field);
        return $value <= $otherValue;
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @return bool
     */
    public function validateGreaterThanEqual($attribute, $value, $parameters)
    {
        return $value >= $parameters[0];
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @return bool
     */
    public function validateGreaterThanEqualForTimeStamp($attribute, $value, $parameters)
    {
        $value = date('Y-m-d H:i:s', strtotime($value));
        $other = date('Y-m-d H:i:s', strtotime($parameters[0]));

        if ($value == $other) {
            return true;
        }
        return $value > $other;
    }

    protected function replaceGreaterThanField($message, $attribute, $rule, $parameters)
    {
        return str_replace(':other', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    protected function replaceGreaterThanOrEqualField($message, $attribute, $rule, $parameters)
    {
        return str_replace(':other', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    protected function replaceGreaterThanOrEqualTimeField($message, $attribute, $rule, $parameters)
    {
        return str_replace(':other', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    protected function replaceGreaterThanOrEqual($message, $attribute, $rule, $parameters)
    {
        return str_replace(':other', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    protected function replaceGreaterThanEqualForTimeStamp($message, $attribute, $rule, $parameters)
    {
        return str_replace(':other', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    protected function replaceLessThanField($message, $attribute, $rule, $parameters)
    {
        return str_replace(':other', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    protected function replaceLessThanOrEqualField($message, $attribute, $rule, $parameters)
    {
        return str_replace(':other', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    protected function replaceLessThanOrEqualTimeField($message, $attribute, $rule, $parameters)
    {
        return str_replace(':other', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    protected function replaceLessThanFieldOrEqual($message, $attribute, $rule, $parameters)
    {
        return str_replace(':other', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    protected function replaceLessThanEqualForTimeStamp($message, $attribute, $rule, $parameters)
    {
        return str_replace(':other', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @return bool
     */
    public function validateKatakana($attribute, $value, $parameters)
    {
        $result = preg_match('/^([゠ァアィイゥウェエォオカガキギクグケゲコゴサザシジスズセゼソゾタダチヂッツヅテデトドナニヌネノハバパヒビピフブプヘベペホボポマミムメモャヤュユョヨラリルレロヮワヰヱヲンヴヵヶヷヸヹヺ・ーヽヾヿ]+)$/u', $value, $matches);
        return $result ? true : false;
    }

    /*
     * Validate that an attribute matches a date format.
     *
     * @param  string $attribute
     * @param  mixed $value
     * @param  array $parameters
     * @return bool
     */
    public function validateDateFormat($attribute, $value, $parameters)
    {
        $date = ['Y-m-d H:i:s' => getConstant('DEFAULT_DATE_TIME_VALUE'), 'Y-m-d' => getConstant('DEFAULT_DATE_VALUE'), 'H:i:s' => getConstant('DEFAULT_TIME_VALUE')];
        $vFormat = isset($parameters[0]) ? $parameters[0] : '';
        if (!$vFormat) {
            return parent::validateDateFormat($attribute, $value, $parameters);
        }
        foreach ($date as $format => $item) {
            if ($format === $vFormat && $item === $value) {
                return true;
            }
        }
        return parent::validateDateFormat($attribute, $value, $parameters);
    }

    /**
     * @param $attribute
     * @param $value
     * @return bool
     */
    public function validateArrayRequired($attribute, $value)
    {
        if (!is_array($value) && empty($value) && '0' !== (string)$value) {
            return false;
        }
        if (is_array($value)) {
            $value = array_filter($value);
            if (empty($value)) {
                return false;
            }
            foreach ($value as $item) {
                if (!$this->validateArrayRequired($attribute, $item)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @return bool
     */
    public function validatePhone($attribute, $value, $parameters)
    {
        $regex = "/^\d{9,11}$/";
        if (strpos($value, '+') !== false) {
            $regex = "/^\+?(?:[0-9] ?){9,11}[0-9]$/";
        }
        return preg_match($regex, $value);
    }

    /**
     * @param $attribute
     * @param $values
     * @param $parameters
     * @return bool
     */
    public function validateRequiredOne($attribute, $values, $parameters)
    {
        if (empty($parameters) || !is_array($parameters)) {
            return false;
        }
        $field = array_shift($parameters);
        foreach ($values as $index => $item) {
            if (isset($item[$field]) && !empty($item[$field])) {
                return true;
            }
            if (is_array($item)) {
                foreach ($item as $idx => $child) {
                    if (isset($child[$field]) && !empty($child[$field])) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * @param $attribute
     * @param $values
     * @param $parameters
     * @return bool
     */
    public function validateCustomExists($attribute, $values, $parameters)
    {
        if (empty($parameters[0]) || !is_array($parameters)) {
            return false;
        }
        $model = new $parameters[0]();
        $query = $model->getQuery();
        unset($parameters[0]);
        $parameters = array_values($parameters);

        // set file value default
        if(empty($parameters)){
            $parameters[] = $attribute;
            $parameters[] = $values;
        }

        $count = count($parameters);
        for ($i = 0; $i < $count; $i += 2) {
            $field = $parameters[$i];
            $value = explode('*_*', $parameters[$i + 1]);
            if(count($value) == 1){
                if (Arr::get($value, 0) == 'isnull') {
                    $query->whereNull($field);
                } else {
                    $query->where($field, array_first($value));
                }
            }else{
                $query->whereIn($field, $value);
            }
        }
        if ($model instanceof ModelSoftDelete) {
            $query->where(function ($q) {
                return $q->where(getDelFlagColumn(), '=', getDelFlagColumn('active'))->orWhereNull(
                    getDelFlagColumn()
                );
            });
        }
        return $query->count() >= 1;
    }

    public function validateCustomExistsArray($attribute, $values, $parameters)
    {
        if (empty($parameters[0]) || !is_array($parameters)) {
            return false;
        }
        $model = new $parameters[0]();
        $query = $model->getQuery();
        unset($parameters[0]);
        $parameters = array_values($parameters);
        if (empty($parameters)) {
            $parameters = (array)$model->getKeyName();
        }
        foreach ($parameters as $parameter) {
            $query->where($parameter, Arr::get($this->data, $parameter));
        }
        if ($model instanceof ModelSoftDelete) {
            $query->where(function ($q) {
                return $q->where(getDelFlagColumn(), '=', getDelFlagColumn('active'))->orWhereNull(
                    getDelFlagColumn()
                );
            });
        }
        return $query->count() >= 1;
    }

    /**
     * @param $attribute
     * @param $values
     * @param $parameters
     * @return bool
     */
    public function validateCustomUnique($attribute, $values, $parameters)
    {
        if (empty($parameters[0]) || !is_array($parameters)) {
            return false;
        }
        $model = new $parameters[0]();
        $query = $model->getQuery();
        unset($parameters[0]);
        $parameters = array_values($parameters);

        $keys = (array)$model->getKeyName();
        $keysData = [];
        foreach ($keys as $key) {
            if (array_key_exists($key, $this->data)) {
                $keysData[$key] = $this->data[$key];
            }
        }
        if (!empty($keysData)) {
            foreach ($keysData as $key => $value) {
                $query->whereNotIn($key, (array)$value);
            }
        }
        // set file value default
        if(empty($parameters)){
            $parameters[] = $attribute;
            $parameters[] = $values;
        }
        $count = count($parameters);
        for ($i = 0; $i < $count; $i += 2) {
            $field = $parameters[$i];
            $value = explode('*_*', $parameters[$i + 1]);
            if(count($value) == 1){
                if (Arr::get($value, 0) == 'isnull') {
                    $query->whereNull($field);
                } else {
                    $query->where($field, array_first($value));
                }
            }else{
                $query->whereIn($field, $value);
            }

        }

        if ($model instanceof ModelSoftDelete) {
            $query->where(function ($q) {
                return $q->where(getDelFlagColumn(), '=', getDelFlagColumn('active'))->orWhereNull(
                    getDelFlagColumn()
                );
            });
        }
        return $query->count() <= 0;
    }

    public function validateUpperCase($attribute, $values, $parameters)
    {
        return strtoupper($values) === $values;
    }

    public function validateLimitArray($attribute, $values, $parameters)
    {
        if (is_array($values) && count($values) > $parameters[0]) {
            return false;
        }

        return true;
    }

    /**
     * @param $attribute
     * @param $value
     * @return bool
     */
    public function validateArrayAlphaNum($attribute, $value)
    {
        if (!is_array($value)) {
            if (! is_string($value) && ! is_numeric($value)) {
                return false;
            }
            if (!preg_match('/^[\pL\pM\pN]+$/u', $value) > 0) {
                return false;
            }
            return true;
        }
        foreach ($value as $item) {
            if (! is_string($item) && ! is_numeric($item)) {
                return false;
            }
            if (!preg_match('/[0-9a-zA-A]/', $item) > 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param $attribute
     * @param $value
     * @return bool
     */
    public function validateArrayNumeric($attribute, $value)
    {
        if (!is_array($value)) {
            return is_numeric($value);
        }
        foreach ($value as $item) {
            if (!is_numeric($item)) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @return bool
     */
    public function validateArrayLength($attribute, $value, $parameters)
    {
        if (!is_array($value)) {
            return true;
        }

        return count($value) <= $parameters[0];
    }
    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @return bool
     */
    public function validateLength($attribute, $value, $parameters)
    {
        if(!is_string($value)){
            return false;
        }
       return mb_strlen($value) == $parameters[0];
    }

    protected function replaceLength($message, $attribute, $rule, $parameters)
    {
        return str_replace(':length', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @return bool
     */
    public function validateMaxLength($attribute, $value, $parameters)
    {
        if(!is_string($value)){
            return false;
        }
        return mb_strlen($value) <= $parameters[0];
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @return bool
     */
    public function validateMinLength($attribute, $value, $parameters)
    {
        if (!is_string($value)) {
            return false;
        }
        return mb_strlen($value) >= $parameters[0];
    }

    protected function replaceMaxLength($message, $attribute, $rule, $parameters)
    {
        return str_replace(':length', $this->getDisplayableAttribute($parameters[0]), $message);
    }

    public function validateNotJapanese($attribute, $value, $parameters)
    {
        return !$this->_isJapanese($value);
    }

    protected function _isKanji($str)
    {
        return preg_match('/[\x{4E00}-\x{9FBF}]/u', $str) > 0;
    }

    /**
     * Detect Hiragana character
     *
     * @param $str
     * @return bool
     */
    protected function _isHiragana($str)
    {
        return preg_match('/[\x{3040}-\x{309F}]/u', $str) > 0;
    }

    /**
     * Detect Katakana character
     *
     * @param $str
     * @return bool
     */
    protected function _isKatakana($str)
    {
        return preg_match('/[\x{30A0}-\x{30FF}]/u', $str) > 0;
    }

    /**
     * Detect Japanese
     *
     * @param $str
     * @return bool
     */
    protected function _isJapanese($str)
    {
        return $this->_isKanji($str) || $this->_isHiragana($str) || $this->_isKatakana($str) || strlen($str) != strlen(utf8_decode($str));
    }

    public function validateCustomInteger($attribute, $value, $parameters)
    {
        $re = preg_replace('/[^0-9]/', '', $value);
        return $re == $value;
    }

    public function validateIntegerWithNegative($attribute, $value, $parameters)
    {
        if (is_array($value) || str_contains($value, '.')) {
            return false;
        }
        return is_numeric($value);
    }

    /**
     * Validate that an attribute is between a given number of digits.
     *
     * @param  string $attribute
     * @param  mixed $value
     * @param  array $parameters
     * @return bool
     */
    public function validateDigitsWithNegativeBetween($attribute, $value, $parameters)
    {
        $this->requireParameterCount(2, $parameters, 'digits_with_negative_between');

        $length = mb_strlen($value);
        return $length >= $parameters[0] && $length <= $parameters[1];
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     * @return bool
     */
    public function validateNotBlank($attribute, $value, $parameters)
    {
        return !empty($value) && (string)$value !== '0';
    }

    public function validateGreaterThanWithNegative($attribute, $value, $parameters)
    {
        return intval($value) > $parameters[0];
    }

    public function validateLessThanWithNegative($attribute, $value, $parameters)
    {
        return intval($value) < $parameters[0];
    }

    public function validateRuleIn($attribute, $value, $parameters)
    {
        return in_array($value, $parameters, true);
    }

    public function validateMinNum($attribute, $value, $parameters)
    {
        return intval($value) >= $parameters[0];
    }

    public function validateMaxNum($attribute, $value, $parameters)
    {
        return intval($value) <= $parameters[0];
    }

    /**
     * Validate that an attribute matches a date format.
     *
     * @param string $attribute
     * @param mixed $value
     * @param array $parameters
     * @return bool
     */
    public function validateDateFormatMulti($attribute, $value, $parameters)
    {
        if (!parent::validateDate($attribute, $value)) {
            return false;
        }

        // format time from '1:1:1' to '01:01:01'
        if (strpos($value, ' ')) {
            list($day, $time) = explode(' ', $value);
            if (strpos($time, ':')) {
                $timeArray = array_map(function ($item) {
                    return ljust($item);
                },
                    explode(':', $time)
                );
                $value = $day . ' ' . implode(':', $timeArray);
            }
        }

        foreach ($parameters as $parameter) {
            if (parent::validateDateFormat($attribute, $value, [$parameter])) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     */
    public function ValidateCheckReserveDenialsFrom($attribute, $value, $parameters)
    {
        $index = (int)data_get(explode('.', $attribute), 1);

        $data = $this->getData();

        $denial_time_to = $data['data'][$index]['denial_time_to'];

        if (strtotime($value) >= strtotime($denial_time_to)) {
            return false;
        }

        return true;
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     */
    public function ValidateUniquePeriodTime($attribute, $value, $parameters)
    {
        $data = $this->getData();

        $index = (int)data_get(explode('.', $attribute), 1);

        $dataThis = [
            'denial_time_from' => $data['data'][$index]['denial_time_from'],
            'denial_time_to' => $data['data'][$index]['denial_time_to'],
        ];

        if (!empty($data['data'])) {
            foreach ($data['data'] as $key => $item) {
                if ($key != $index && $dataThis['denial_time_from'] == $item['denial_time_from'] && $dataThis['denial_time_to'] == $item['denial_time_to']) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * @param $attribute
     * @param $value
     * @param $parameters
     */
    public function ValidateUniqueReserveTime($attribute, $value, $parameters)
    {
        $data = $this->getData();

        $index = (int)data_get(explode('.', $attribute), 1);
        $reserve_time = $data['data'][$index]['reserve_time'];

        if (!empty($data['data'])) {
            foreach ($data['data'] as $key => $item) {
                if ($key != $index && $reserve_time == $item['reserve_time']) {
                    return false;
                }
            }
        }

        return true;
    }
}

