<?php

use \App\Database\Migration\CustomBlueprint as Blueprint;
use App\Database\Migration\Schema;

class CreateReserveTelTable extends \App\Database\Migration\Create
{
    protected $_table = 'reserve_tel';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable($this->getTable())) {
            Schema::create($this->getTable(), function (Blueprint $table) {
                $table->increments('id');
                $table->integer('shop_id')->comment('店舗ID');
                $table->date('reserve_date')->comment('予約日付');
                $table->char('tel_flg', 1)->comment('TELフラグ');
                $table->actionBy();
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }
}
