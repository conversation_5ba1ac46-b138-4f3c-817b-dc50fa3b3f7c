<?php

namespace App\Model\Entities;

use App\Model\Base\Base;
use App\Model\Base\CustomSoftDeletes;
use App\Model\Base\ModelSoftDelete;

class ReserveDenials extends ModelSoftDelete
{
    protected $table = 'reserve_denials';
    protected $_alias = 'reserve_denials';

    protected $fillable = [
        'shop_id',
        'denial_time_from',
        'denial_time_to',
        'ins_id',
        'upd_id',
        'ins_date',
        'upd_date',
        'del_flag',
    ];
}
