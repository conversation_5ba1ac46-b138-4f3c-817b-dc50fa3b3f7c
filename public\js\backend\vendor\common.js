// Loading
$(window).on('load',function(){
  $('body').removeClass('preload');
});

$(function () {
  /* IE smooth scroll false
   * ------------------------------------------------------- */
  if(navigator.userAgent.match(/MSIE 10/i) || navigator.userAgent.match(/Trident\/7\./) || navigator.userAgent.match(/Edge\/12\./)) {
   $('body').on("mousewheel", function () {
   (event.preventDefault) ? event.preventDefault():event.returnValue=false;
   var wd = event.wheelDelta;
   var csp = window.pageYOffset;
   window.scrollTo(0, csp - wd);
   });
  }
  /* Click event
   * ------------------------------------------------------- */
  var clickEventType=((window.ontouchstart!==null)?'click':'touchstart');

  /* set height
   * ------------------------------------------------------- */
   if(document.getElementsByClassName('vh-100').length) {
     const setFillHeight = () => {
       const vh = window.innerHeight * 0.01;
       document.getElementsByClassName('vh-100')[0].style.setProperty('--vh', `${vh}px`);
     }
     let vw = window.innerWidth;
     window.addEventListener('resize', () => {
       if (vw === window.innerWidth) {
         return;
       }
       vw = window.innerWidth;
       setFillHeight();
     });
     setFillHeight();
   }

  /* Accordion
   * ------------------------------------------------------- */
  var $ac_btn = $('.ac-btn');
  $ac_btn.each(function() {
  	$(this).on(clickEventType, function(){
  		$(this).next('.ac-content').stop().slideToggle('fast');
  		$(this).toggleClass('is-open');
  		return false;
  	});
  });

  /* Dropdown
   * ------------------------------------------------------- */
   $('.dropdown').each(function(){
     var $btn = $(this).find('.dropdown-btn'),
         $cont = $(this).find('.dropdown-content');
     $btn.on('click', function(e){
       var parent = $(this).closest('.dropdown'),
           opened = parent.attr('aria-opened');
       if (opened == 'true') {
         parent.attr('aria-opened',false);
       } else {
         parent.attr('aria-opened',true);
         $('.dropdown').not(parent).attr('aria-opened',false);
       }
     });
     var btnH = $btn.outerHeight();
     $cont.css({
       position: 'absolute',
       'transform': 'translate3d(-50%, '+btnH+'px, 0px)'
     });
   });
   $(document).on('click', function(e){
     if (!$(e.target).closest('[aria-opened="true"]').length) {
       $('.dropdown').attr('aria-opened',false);
       $('.dropdown').attr('aria-opened',false);
     }
   });

  /* SP Navigation toggle
   * ------------------------------------------------------- */
   $(document).on('click', '#header-toggle-btn' ,function(){
     if ($('body').hasClass('is-nav-open')) {
       $('body').removeClass('is-nav-open');
     } else {
       $('body').addClass('is-nav-open');
     }
   });

   /* PerfectScrollbar
    * ------------------------------------------------------- */
    if($('.scroll-area').length){
      var ps = new PerfectScrollbar('.scroll-area');
    }
    $(window).resize(function() {
      if($('.scroll-area').length){
        ps.update();
      }
    });

  /* Dialog
   * ------------------------------------------------------- */
   function fluidDialog() {
     var $visible = $(".ui-dialog:visible");
     // Each open dialog
     $visible.each(function () {
       var $this = $(this);
       var dialog = $this.find(".ui-dialog-content").data("ui-dialog");
       var wWidth = $(window).width();
       // Check window width against dialog width.
       if (wWidth < (parseInt(dialog.options.maxWidth, 10) + 50)) {
         // Keep dialog from filling entire screen
         $this.css("max-width", "90%");
         $this.css("width", "auto");
       } else {
         // Fix maxWidth bug.
         $this.css("max-width", dialog.options.maxWidth + "px");
       }
       // Reposition dialog.
       dialog.option("position", dialog.options.position);
     });
   }

   $(window).resize(function () {
     fluidDialog();
   });

   $(document).on("dialogopen", ".ui-dialog", function () {
     fluidDialog();
   });
});
