.form-primary .form-group .form-field {
    display: block;
}

.form-select select {
    cursor: pointer;
    padding-top: 7px !important;
}

.form-control {
    color: black;
    font-size: 14px;
}
/* custom checkbox */
.checkbox-styled .item:hover {
    cursor: pointer;
}

.checkbox-styled .item input:not(:checked),
.checkbox-styled .item input:checked {
    position: absolute;
    left: -9999px;
}

.checkbox-styled .item input:not(:checked)+label,
.checkbox-styled .item input:checked+label {
    position: relative;
    cursor: pointer;
    display: inline-block;
    word-break: break-all;
}

.checkbox-styled .item input:not(:checked)+label,
.checkbox-styled .item input:checked+label {
    padding-left: 22px;
    padding-top: 12px;
    font-weight: 400
}

.checkbox-styled .item input:not(:checked)+label:after,
.checkbox-styled .item input:checked+label:after {
    content: '';
    position: absolute;
    top: 4px;
    left: 2px;
}

.checkbox-styled .item input:not(:checked)+label:before,
.checkbox-styled .item input:checked+label:before {
    content: '';
    position: absolute;
    left: 75px;
    top: 11px;
    width: 22px;
    height: 22px;
    background: #fff;
    border-radius: 3px;
    border: 1px solid #C0C0C0;
}

.checkbox-styled .item input:checked+label:before {
    background-color: #508FF4;
    border: 2px solid #508FF4;
}

.checkbox-styled .item input:checked+label:after {
    background-size: contain;
    left: 80px;
    top: 16px !important;
    width: 1em;
    height: 0.5em;
    border-bottom: 0.2em solid #fff;
    border-left: 0.2em solid #fff;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

/*checkbox-tel*/
.checkbox-styled-tel .item-tel:hover {
    cursor: pointer;
}

.checkbox-styled-tel .item-tel input:not(:checked),
.checkbox-styled-tel .item-tel input:checked {
    position: absolute;
    left: -9999px;
}

.checkbox-styled-tel .item-tel input:not(:checked)+label,
.checkbox-styled-tel .item-tel input:checked+label {
    position: relative;
    cursor: pointer;
    display: inline-block;
    word-break: break-all;
}

.checkbox-styled-tel .item-tel input:not(:checked)+label,
.checkbox-styled-tel .item-tel input:checked+label {
    padding-left: 22px;
    padding-top: 4px;
    font-weight: 400
}

.checkbox-styled-tel .item-tel input:not(:checked)+label:after,
.checkbox-styled-tel .item-tel input:checked+label:after {
    content: '';
    position: absolute;
    top: 4px;
    left: 2px;
}

.checkbox-styled-tel .item-tel input:not(:checked)+label:before,
.checkbox-styled-tel .item-tel input:checked+label:before {
    content: '';
    position: absolute;
    left: 140px;
    top: 1px;
    width: 22px;
    height: 22px;
    background: #fff;
    border-radius: 3px;
    border: 1px solid #C0C0C0;
}

.checkbox-styled-tel .item-tel input:checked+label:before {
    background-color: #508FF4;
    border: 2px solid #508FF4;
}

.checkbox-styled-tel .item-tel input:checked+label:after {
    background-size: contain;
    left: 145px;
    top: 6px !important;
    width: 1em;
    height: 0.5em;
    border-bottom: 0.2em solid #fff;
    border-left: 0.2em solid #fff;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);}

/* add cursor */
.form-control-datepicker {
    cursor: pointer !important;
}

.ui-datepicker {
    display: none !important;
}

.class-error {
    border: 1px solid #a94442 !important;
}

select.edit {
    pointer-events: none;
    background: #f5f5f5;
    opacity: 0.8 !important;
}

.checkbox-styled.edit .item {
    pointer-events: none;
}

.text-add {
    padding-left: 30px;
}
.form-control[disabled] {
    opacity: 0.8 !important;
}

@media (min-width: 768px) {
    .form-control {
        height: fit-content;
    }

    .form-select select {
        height: 41px !important;
    }
}
@media (min-height: 33.875em) {
    .picker__weekday {
        padding-left: 36px;
    }
}
