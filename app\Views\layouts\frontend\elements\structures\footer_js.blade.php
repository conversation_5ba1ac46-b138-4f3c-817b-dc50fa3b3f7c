<?php
$jsFiles = [
    'vendor/popper.min',
    'vendor/bootstrap.min',
    'vendor/bootstrap-datepicker.min',
    'vendor/bootstrap-datepicker.ja.min',
    'vendor/utils/loadingoverlay.min',
    'vendor/utils/loadingoverlay_progress.min',
    'vendor/utils/moment.min',
    'vendor/utils/min',
    'vendor/utils/common',
    'vendor/utils/xhr',
    'vendor/utils/system',
    'vendor/bootstrap-timepicker.min',
    'vendor/ja',
    'vendor/jquery-3.6.0.min',
    'vendor/jquery-ui.min',
    'vendor/jquery.timepicker',
    'vendor/jquery.maskedinput.min',
    'vendor/jquery.ui.touch-punch.min',
    'vendor/perfect-scrollbar.min',
    'vendor/fullcalendar/main.min',
    'vendor/fullcalendar/locales-all.min',
    'vendor/fullcalendar/ja',
    'vendor/form',
    'vendor/common',];
?>
{!! loadFiles($jsFiles, $area, 'js') !!}
@include('layouts.elements.footer_autoload')
<script>
    $('#sandbox-container .input-daterange').datepicker({
        language: "ja",
        todayHighlight: true
    });
</script>
