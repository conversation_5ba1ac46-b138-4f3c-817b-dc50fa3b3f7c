<?php

namespace App\Services\Backend;

use App\Database\Migration\Create;
use App\Model\Entities\Reserve;
use App\Model\Entities\ReserveDenials;
use App\Model\Entities\ShopInfo;
use App\Repositories\ReserveDenialsRepository;
use App\Repositories\ReserveRepository;
use App\Repositories\ReserveTelRepository;
use App\Repositories\ShopInfoRepository;
use App\Services\Base\BaseService;
use Carbon\Carbon;
use DateInterval;
use DatePeriod;
use DateTime;
use Illuminate\Support\Facades\DB;

class CalendarService extends BaseService
{
    protected $reserveTelRepository;
    protected $shopInfoRepository;
    protected $reserveDenialsRepository;
    protected $reserveRepository;

    public function __construct(
        ReserveTelRepository     $reserveTelRepository,
        ShopInfoRepository       $shopInfoRepository,
        ReserveDenialsRepository $reserveDenialsRepository,
        ReserveRepository        $reserveRepository
    )
    {
        $this->reserveTelRepository = $reserveTelRepository;
        $this->shopInfoRepository = $shopInfoRepository;
        $this->reserveDenialsRepository = $reserveDenialsRepository;
        $this->reserveRepository = $reserveRepository;
    }

    /**
     * @param $shopId
     * @param $start
     * @param $end
     *
     * return array (key:date, value:tel_flg)
     */
    public function getDataReserveTelByShop($shopId, $start, $end)
    {
        $queryReserveTel = [
            'shop_id_eq' => $shopId,
            'reserve_date_gteq' => $start,
            'reserve_date_lteqt' => $end,
        ];

        return $this->reserveTelRepository->search($queryReserveTel, ['reserve_date', 'tel_flg'])->get()->pluck('tel_flg', 'reserve_date')->toArray();
    }

    /**
     * @param $shopId
     * @param $date
     * @return array
     */
    public function getTotalReserveNum($shopId, $date)
    {
        $date = Carbon::createFromFormat('Y/m/d', $date);
        $start = $date->startOfMonth('Y-m-d')->format('Y-m-d');
        $end = $date->endOfMonth()->format('Y-m-d');

        $query = $this->reserveRepository->getTotalReserveNum($shopId, $start, $end);

        $dataTotalReserveNum = array_map('intval', $query);

        // make array datetime in month
        $period = new DatePeriod(
            new DateTime($start),
            new DateInterval('P1D'),
            new DateTime($date->endOfMonth()->addDay(1)->format('Y-m-d'))
        );

        $data = [];

        foreach ($period as $value) {
            $data[$value->format('Y-m-d')] = 0;
        }

        if (!empty($dataTotalReserveNum)) {
            foreach ($dataTotalReserveNum as $date => $totalByDate) {
                $data[$date] = $totalByDate;
            }
        }

        return $data;
    }

    /**
     * @param $shopId
     * @param $date
     * @return array
     */
    public function getTotalReserveNumByDate($shopId, $start, $end)
    {
        $query = $this->reserveRepository->getTotalReserveNum($shopId, $start, $end);
        $dataTotalReserveNum = array_map('intval', $query);

        // make array datetime in month
        $period = new DatePeriod(
            new DateTime($start),
            new DateInterval('P1D'),
            new DateTime(Carbon::createFromFormat('Y-m-d', $end)->addDay(1)->format('Y-m-d'))
        );

        $data = [];

        foreach ($period as $value) {
            $data[$value->format('Y-m-d')] = 0;
        }

        if (!empty($dataTotalReserveNum)) {
            foreach ($dataTotalReserveNum as $date => $totalByDate) {
                $data[$date] = $totalByDate;
            }
        }

        return $data;
    }

    /**
     * @param $dataShop
     * @return array
     */
    public function getHolidayByWeekday($dataShop)
    {
        $weekMap = getConstant('WEEK_MAP');
        $data = [];

        foreach ($weekMap as $index => $strDay) {
            $fieldName = $strDay . '_closing_flg';
            $data[$index] = (int)$dataShop->$fieldName;
        }

        return $data;
    }

    /**
     * Make array datetime in month
     *
     * @param $dataDateTime
     * @param $date
     * @return array
     */
    public function makePeriodDatetimeInMonth($date)
    {
        $period = new DatePeriod(
            new DateTime($date->firstOfMonth()->format('Y-m-d')),
            new DateInterval('P1D'),
            new DateTime($date->endOfMonth()->addDay(1)->format('Y-m-d'))
        );

        // array datetime in month
        $dataDateTime = [];
        foreach ($period as $value) {
            $dataDateTime[] = $value->format('Y-m-d');
        }

        return $dataDateTime;
    }

    /**
     * @param $start
     * @param $end
     * @return array
     */
    public function makePeriodDatetime($start, $end)
    {
        $period = new DatePeriod(
            new DateTime($start),
            new DateInterval('P1D'),
            new DateTime(Carbon::createFromFormat('Y-m-d', $end)->addDay(1)->format('Y-m-d'))
        );

        // array datetime in month
        $dataDateTime = [];
        foreach ($period as $value) {
            $dataDateTime[] = $value->format('Y-m-d');
        }

        return $dataDateTime;
    }

    /**
     * @param $entity
     * @param $data
     *
     * @return $entity
     */
    public function createOrUpdateFlgTel($entity, $data)
    {
        // do create & update
        DB::beginTransaction();
        try {
            if (!empty($entity)) {
                // for update
                $entity->tel_flg = (int)$data['tel_flg'];
                $entity->upd_id = getCurrentUserId();
                $entity->save();
            } else {
                // for create
                $entity = $this->reserveTelRepository->create([
                    'tel_flg' => getConstant('RESERVE_TEL_FLAG.ON'),
                    'shop_id' => (int)$data['shop_id'],
                    'reserve_date' => $data['reserve_date'],
                    'ins_id' => getCurrentUserId(),
                ]);
            }
            DB::commit();
            return $entity;
        } catch (\Exception $e) {
            logError($e->getMessage());
            DB::rollBack();
            return false;
        }
    }

    /**
     * @param $operation
     * @param $dataShop
     * @param $weekday
     *
     * @return array
     */
    public function getOperationByWeekday($dataShop, $weekday)
    {
        $weekMap = getConstant('WEEK_MAP');

        $fieldFrom = $weekMap[$weekday] . '_operation_from';
        $fieldTo = $weekMap[$weekday] . '_operation_to';
        $fieldClosingFlg = $weekMap[$weekday] . '_closing_flg';

        $operation = [
            'from' => formatTime($dataShop->$fieldFrom),
            'to' => formatTime($dataShop->$fieldTo),
            'flg' => (int)$dataShop->$fieldClosingFlg
        ];

        return $operation;
    }

    /**
     * create, update, remove reserveDenials
     *
     * @param $shopId
     * @param $data
     *
     * @return boolean
     */
    public function saveReserveDenials($shopId, $data)
    {
        $dataCreate = [];
        $dataUpdate = [];
        $dataRemoveIds = [];
        $dataIds = [];

        /** @var ReserveDenialsRepository $reserveDenyIds */
        $reserveDenyIds = $this->reserveDenialsRepository->findWhere(['shop_id' => $shopId])->pluck('id')->toArray();

        // handle data: create, update, remove
        if (!empty($data)) {
            foreach ($data as $key => $value) {
                if (empty($value['id'])) {
                    // data create
                    $dataCreate[] = [
                        'shop_id' => $shopId,
                        'denial_time_from' => $value['denial_time_from'],
                        'denial_time_to' => $value['denial_time_to'],
                        'ins_id' => getCurrentUserId(),
                        'ins_date' => Carbon::now(),
                        'upd_date' => getConstant('DEFAULT_UPD_DATE')
                    ];

                    continue;
                }

                // data update
                $dataUpdate[] = [
                    'id' => (int)$value['id'],
                    'shop_id' => $shopId,
                    'denial_time_from' => $value['denial_time_from'],
                    'denial_time_to' => $value['denial_time_to'],
                ];

                // array ids by shop
                $dataIds[] = (int)$value['id'];
            }
        }

        // data ids remove
        if (!empty($reserveDenyIds)) {
            $dataRemoveIds = array_diff($reserveDenyIds, $dataIds);
        }

        // do create & update & remove
        DB::beginTransaction();
        try {
            // insert
            if (!empty($dataCreate)) {
                ReserveDenials::insert($dataCreate);
            }
            // update
            if (!empty($dataUpdate)) {
                foreach ($dataUpdate as $key => $value) {
                    $reserveDenial = $this->reserveDenialsRepository->find($value['id']);
                    $reserveDenial->denial_time_from = $value['denial_time_from'];
                    $reserveDenial->denial_time_to = $value['denial_time_to'];
                    $reserveDenial->upd_id = getCurrentUserId();
                    $reserveDenial->save();
                }
            }
            // remove
            if (!empty($dataRemoveIds)) {
                foreach ($dataRemoveIds as $id) {
                    $reserveDenial = $this->reserveDenialsRepository->findById($id);
                    $reserveDenial->del_flag = getConstant('DEL_FLAG_ON');
                    $reserveDenial->save();
                }
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            logError($e->getMessage());
            DB::rollBack();
            return false;
        }
    }

    /**
     * @param $dataShop
     * @param $dataRequest
     *
     * @return string
     */
    public function getHtmlOperationTime($dataShop, $dataRequest)
    {
        $listReserve = $this->reserveRepository->getReserveInDayByShop($dataRequest);
        $intervalMin = (int)$dataShop->interval_min;
        $arrayOperationTime = [];

        // case make data update
        if (sizeof($listReserve) > 0) {
            foreach ($listReserve as $key => $value) {
                $arrayOperationTime[] = [
                    'id' => $value->id,
                    'from' => formatTime($value->reserve_time),
                    'to' => date('H:i', strtotime($value->reserve_time) + (int)$dataShop->interval_min * 60),
                    'reserve_num' => $value->reserve_num,
                    'deny' => getConstant('DENY.OFF'),
                ];
            }
        } else { // case make data create
            $date = Carbon::createFromFormat('Y-m-d', $dataRequest['reserve_date']);
            $weekday = $date->dayOfWeek;

            // get operation shop by weekday. eg: ['from' => '00:00', 'to' => '01:30', 'flg' => 0]
            $operation = $this->getOperationByWeekday($dataShop, $weekday);

            // handler period
            $periodSeconds = strtotime($operation['to']) - strtotime($operation['from']);
            $periodNumMinutes = $periodSeconds / 60; // convert second to minute
            $length = floor($periodNumMinutes / $intervalMin);
            $start = strtotime($operation['from']);
            $temp = $start;

            for ($i = 0; $i < $length; $i++) {
                $arrayOperationTime[] = [
                    'from' => date('H:i', $temp),
                    'to' => date('H:i', $temp + $intervalMin * 60),
                    'deny' => getConstant('DENY.OFF'),
                ];

                $temp += $intervalMin * 60;
            }
        }

        $publishFromTime = Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_from_dt)->format('H:i:s');
        $publishToTime = !empty($dataShop->publish_to_dt)
                         ? Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_to_dt)->format('H:i:s')
                         : null;

        $publishFromDate = Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_from_dt)->format('Y-m-d');
        $publishToDate = !empty($dataShop->publish_to_dt)
                         ? Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_to_dt)->format('Y-m-d')
                         : null;

        $today = date('Y-m-d');
        $currentTime = date('H:i:s');

        // check time denials by shop
        $denials = $this->reserveDenialsRepository->getTimeDenyByShop($dataShop->id);

        foreach ($arrayOperationTime as $key => $value) {
            // period operation time
            $operation_time = [
                toMinute($value['from']),
                toMinute($value['to'])
            ];

            // today: check publish time
            if ($publishFromDate == $dataRequest['reserve_date'] && $publishFromDate == $today) {
                // publish time from
                $time_end = toMinute($publishFromTime) > toMinute($currentTime)
                          ? toMinute($publishFromTime)
                          : toMinute($currentTime);

                if ($this->isOverlapped($operation_time, [0, $time_end])) {
                    $arrayOperationTime[$key]['deny'] = getConstant('DENY.ON');
                }
            }

            if (!empty($publishToDate) && $publishToDate == $dataRequest['reserve_date']) {
                // publish time to
                if ($this->isOverlapped($operation_time, [toMinute($publishToTime), 1440])) {
                    $arrayOperationTime[$key]['deny'] = getConstant('DENY.ON');
                }
            }
            // end check publish time

            if (!empty($denials)) {
                foreach ($denials as $deny) {
                    // period time deny
                    $deny_time = [
                        $deny['from'],
                        $deny['to'],
                    ];
                    // check overlap time
                    if ($this->isOverlapped($operation_time, $deny_time)) {
                        $arrayOperationTime[$key]['deny'] = getConstant('DENY.ON');
                        break;
                    }
                }
            }
        }

        $html = view('backend.calendar.dialog._list_operation_time')
            ->with('dataShop', $dataShop)
            ->with('reserve_date', $dataRequest['reserve_date'])
            ->with('data', $arrayOperationTime)
            ->render();

        return $html;
    }

    /**
     * @param $time1 // period time
     * @param $time2 // period time
     * @return bool
     */
    public function isOverlapped($time1, $time2)
    {
        $overlap = max(-1, min($time1[1], $time2[1]) - max($time1[0], $time2[0]));
        if ($overlap > 0) {
            return true;
        }

        return false;
    }

    /**
     * @param $shopId
     * @param $dataRequest
     * @return array
     */
    public function handleDataCalendar($dataRequest)
    {
        $shopId = (int)$dataRequest['shop_id'];
        $date = Carbon::createFromFormat('Y/m/d', $dataRequest['date']);
        $start = $date->firstOfMonth()->format('Y-m-d');
        $end = $date->endOfMonth()->format('Y-m-d');

        // get data
        $dataShop = $this->shopInfoRepository->findWhere(['id' => $shopId])->first();
        if (empty($dataShop)) {
            return [];
        }

        $dataReserveTel = $this->getDataReserveTelByShop($shopId, $start, $end);
        $timeFramesInDay = $this->shopInfoRepository->getNumFramesWithDeny($dataShop);
        $totalReserveNumByDate = $this->getTotalReserveNum($shopId, $dataRequest['date']);
        $holidayByWeekday = $this->getHolidayByWeekday($dataShop);

        $publishFromDate = Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_from_dt)->format('Y-m-d');
        $publishFromTime = Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_from_dt)->format('H:i:s');

        $publishToDate = !empty($dataShop->publish_to_dt) ? Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_to_dt)->format('Y-m-d') : null;
        $publishToTime = !empty($dataShop->publish_to_dt) ? Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_to_dt)->format('H:i:s') : null;

        $today = date('Y-m-d');
        $now = date('H:i:s');

        // array period datetime in month
        $arrayDate = $this->makePeriodDatetimeInMonth($date);
        $toEnd = false;

        $result = [];
        foreach ($arrayDate as $date) {
            // handle stock
            $weekday = Carbon::createFromFormat('Y-m-d', $date)->weekday();

            // frame: check by time deny & publish datetime
            $totalFrames = $this->totalFrameWithPublishDatetime($dataShop, $date);
            $stock = (int)$dataShop->reservation_frame_num * $totalFrames - $totalReserveNumByDate[$date];
            $stock = $stock >= 0 ? $stock : 0;

            // handle closing flag
            $disable = $holidayByWeekday[$weekday];
            if (strtotime($publishFromDate) >= strtotime($today) && strtotime($date) < strtotime($publishFromDate)) {
                $disable = getConstant('CLOSING_FLG.ON');
            }
            if (strtotime($publishFromDate) < strtotime($today) && strtotime($date) < strtotime($publishFromDate)) {
                $disable = getConstant('CLOSING_FLG.ON');
            }
            if (!empty($publishToDate) && strtotime($publishToDate) < strtotime($date)) {
                $disable = getConstant('CLOSING_FLG.ON');
                $toEnd = true;
            }
            if ($toEnd) {
                $disable = getConstant('CLOSING_FLG.ON');
            }

            // item day
            $result[] = [
                'day' => $date,
                'count' => $stock,
                'tel' => isset($dataReserveTel[$date]) && $dataReserveTel[$date] ? true : false,
                'disable' => $disable,
                'holiday' => $holidayByWeekday[$weekday]
            ];
        }

        return $result;
    }

    /**
     * @param $shopId
     * @param $date
     * @return bool
     */
    public function isHoliday($shopId, $date)
    {
        $shop = $this->shopInfoRepository->findById($shopId);
        $weekday = Carbon::createFromFormat('Y-m-d', $date)->weekday();
        $holidayByWeekday = $this->getHolidayByWeekday($shop);

        return $holidayByWeekday[$weekday] == getConstant('HOLIDAY');
    }

    /**
     * @param $entity
     * @param $data
     *
     * @return $entity
     */
    public function insertOrUpdateReserve($data)
    {
        $dataShop = $this->shopInfoRepository->findById((int)$data['shop_id']);
        if (empty($dataShop) || empty($data['data'])) {
            return false;
        }

        // check publish date time
        $dataShop = $this->shopInfoRepository->findById($data['shop_id']);
        $intervalMin = (int)$dataShop->interval_min;

        $publishFromDate = Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_from_dt)->format('Y-m-d');
        $publishFromTime = Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_from_dt)->format('H:i:s');

        $publishToDate = !empty($dataShop->publish_to_dt) ? Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_to_dt)->format('Y-m-d') : null;
        $publishToTime = !empty($dataShop->publish_to_dt) ? Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_to_dt)->format('H:i:s') : null;

        // handle & check time publish & date now: set reserve_one = 0
        foreach ($data['data'] as $key => $value) {
            $period_time = [
                toMinute($value['reserve_time']),
                toMinute($value['reserve_time']) + $intervalMin,
            ];

            // check: publish from time
            if ($data['reserve_date'] === $publishFromDate) {
                if ($this->isOverlapped($period_time, [0, toMinute($publishFromTime)])) {
                    $data['data'][$key]['reserve_num'] = 0;
                }
            }

            // check: publish to time
            if (!empty($publishToDate) && $data['reserve_date'] === $publishToDate) {
                if ($this->isOverlapped($period_time, [toMinute($publishToTime), 1440])) {
                    $data['data'][$key]['reserve_num'] = 0;
                }
            }
        }

        // handle data create or update
        $dataCreate = [];
        $dataUpdate = [];

        foreach ($data['data'] as $key => $value) {
            // data create
            if (empty($value['id'])) {
                $dataCreate[] = [
                    'shop_id' => $data['shop_id'],
                    'reserve_date' => $data['reserve_date'],
                    'reserve_time' => $value['reserve_time'],
                    'reserve_num' => (int)$value['reserve_num'],
                    'ins_id' => getCurrentUserId(),
                    'ins_date' => Carbon::now(),
                    'upd_date' => getConstant('DEFAULT_UPD_DATE')
                ];

                continue;
            }

            // data update
            $dataUpdate[] = [
                'id' => (int)$value['id'],
                'reserve_date' => $data['reserve_date'],
                'reserve_time' => $value['reserve_time'],
                'reserve_num' => (int)$value['reserve_num'],
            ];
        }

        // do insert, update
        DB::beginTransaction();
        try {
            // insert
            if (!empty($dataCreate)) {
                Reserve::insert($dataCreate);
            } else {
                // update
                foreach ($dataUpdate as $value) {
                    $entity = $this->reserveRepository->findById($value['id']);
                    $entity->reserve_date = $value['reserve_date'];
                    $entity->reserve_time = $value['reserve_time'];
                    $entity->reserve_num = $value['reserve_num'];
                    $entity->upd_id = getCurrentUserId();
                    $entity->save();
                }
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            logError($e->getMessage());
            DB::rollBack();
            return false;
        }
    }

    /**
     * handle data reserve by time deny
     *
     * @param $data
     */
    public function handleDataByTimeDenial(&$data)
    {
        $dataShop = $this->shopInfoRepository->findById($data['shop_id']);
        $interval = (int)$dataShop->interval_min;
        $denials = $this->reserveDenialsRepository->getTimeDenyByShop($data['shop_id']);

        if (sizeof($data['data']) > 0) {
            foreach ($data['data'] as $key => $value) {
                $period = [
                    toMinute($value['reserve_time']),
                    toMinute($value['reserve_time']) + $interval,
                ];

                foreach ($denials as $deny) {
                    $periodDeny = [
                        $deny['from'],
                        $deny['to'],
                    ];

                    if ($this->isOverlapped($period, $periodDeny)) {
                        $data['data'][$key]['reserve_num'] = !empty($value['id']) ? (int)$dataShop->reservation_frame_num : 0;
                        break;
                    }
                }
            }
        }
    }

    /**
     * get time range date of shop
     *
     * @param $shopId
     * @param $weekday
     * @return array
     */
    public function getOperationTimeRangeOfDay($dataShop, $weekday, $isDataInt = false)
    {
        $interval = $dataShop->interval_min;

        // get operation shop by weekday. eg: ['from' => '00:00', 'to' => '01:30', 'flg' => 0]
        $operation = $this->getOperationByWeekday($dataShop, $weekday);

        // handle period
        $periodSeconds = strtotime($operation['to']) - strtotime($operation['from']);
        $periodNumMinutes = $periodSeconds / 60; // convert second to minute
        $length = floor($periodNumMinutes / $interval);
        $start = strtotime($operation['from']);
        $temp = $start;

        $data = [];
        for ($i = 0; $i < $length; $i++) {
            $data[] = [
                'from' => date('H:i', $temp),
                'to' => date('H:i', $temp + $interval * 60)
            ];

            $temp += $interval * 60;
        }

        // check deny
        $denyTime = $this->reserveDenialsRepository->getTimeDenyByShop($dataShop->id);
        if (sizeof($data) > 0 && sizeof($denyTime) > 0) {
            foreach ($data as $key => $value) {
                $time = [
                    toMinute($value['from']),
                    toMinute($value['to']),
                ];

                // check is overlapped
                foreach ($denyTime as $deny) {
                    $deny = [$deny['from'], $deny['to']];
                    if (isOverlapped($time, $deny)) {
                        unset($data[$key]);
                        break;
                    }
                }
            }
        }
        if ($isDataInt) {
            foreach ($data as $key => $value) {
                $data[$key] = [
                    toMinute($value['from']),
                    toMinute($value['to']),
                ];
            }
        }

        return $data;
    }

    /**
     * @param $dataShop
     * @return array
     */
    public function operationTimeRangeEachDays($dataShop)
    {
        $weekMap = getConstant('WEEK_MAP');
        $data = [];
        foreach ($weekMap as $key => $strWeekDay) {
            $data[$key] = $this->getOperationTimeRangeOfDay($dataShop, $key);
        }

        return $data;
    }

    /**
     * @param $shopId
     * @param $date
     *
     * @return array
     */
    public function getDateCalendarMonth($request)
    {
        $shopId = $request['shop_id'];
        $start = $request['start_date'];
        $end = $request['end_date'];
        $dataShop = $this->shopInfoRepository->findById($shopId);

        // BEGIN: check publish time from
        if (time() < strtotime($dataShop->publish_from_dt)) {
            return ['arrayDatetime' => [], 'timeRange' => []];
        }

        $publishTo = $dataShop->publish_to_dt;
        $reservable_period_days = $dataShop->reservable_period_days;
        $maxDateAllow = Carbon::now()->addDay($dataShop->reservable_period_days + 1)->format('Y-m-d');

        if (empty($publishTo)) {
            if ($reservable_period_days > 0) {
                $startTEL = $maxDateAllow;
            }
        } else {
            if ($reservable_period_days > 0) {
                if (strtotime($maxDateAllow) < strtotime($end)) {
                    $startTEL = $maxDateAllow;
                }
            }
            $end = $publishTo < $end ? Carbon::createFromFormat('Y-m-d H:i:s', $publishTo)->format('Y-m-d') : $end;
        }
        // END: check publish time from

        $totalReserveNumByDate = $this->getTotalReserveNumByDate($shopId, $start, $end); /** [key: datetime, value: number] */

        $dataReserveTel = $this->getDataReserveTelByShop($dataShop->id, $start, $end);

        $periodDays = $this->makePeriodDatetime($start, $end); /** ['2022-03-01', '2022-03-01',... ] */

        $arrayTimeRange = $this->operationTimeRangeEachDays($dataShop); /** [key: weekday, value: array period] */

        $dataReserveAllow = $this->reserveRepository->getDataReserve($shopId, $start, $end); /** ['2022-01-01' => ['reserve_time' => '08:00, 'reserve_num' => 1],] */

        $arrayDatetime = [];
        $timeRange = [];
        $interval = (int)$dataShop->interval_min;

        foreach ($periodDays as $day) {
            $weekday = getWeekDay($day);
            // handle data reserve_num by timeData
            $timeData = [];
            // case: exists in database
            if (array_key_exists($day, $dataReserveAllow)) {
                foreach ($dataReserveAllow[$day] as $key => $value) {
                    $timeData[$key] = $dataShop->reservation_frame_num - $value['reserve_num'] > 0
                                    ? $dataShop->reservation_frame_num - $value['reserve_num']
                                    : 0;
                    $from = $value['reserve_time'];
                    $to = date('H:i', strtotime($value['reserve_time']) + $interval * 60);
                    $timeRange[$weekday][$key] = $from . ' 〜 ' . $to;
                }
            } else {
                // case: not exists in database
                $range = $arrayTimeRange[$weekday];
                foreach ($range as $key => $period) {
                    $timeData[$key] = $dataShop->reservation_frame_num;
                    $timeRange[$weekday][$key] = $period['from'] . ' 〜 ' . $period['to'];
                }
            }
            // handle stock: reservation_frame_num x total frames - total reserve_num

            $totalFrames = $this->totalFrameWithPublishDatetime($dataShop, $day);
            $stock = (int)$dataShop->reservation_frame_num * $totalFrames - $totalReserveNumByDate[$day];
            $stock = $stock > 0 ? $stock : 0;

            $arrayDatetime[] = [
                'day' => $day,
                'count' => $stock,
                'tel' => isset($dataReserveTel[$day]) && $dataReserveTel[$day] ? true : false,
                'timeData' => $timeData,
            ];
        }

        /**
         * SET DISPLAY TEL
         *
         * 表示開始日時 (shops.early_reservable_tel_flg) ON
         *      [today + shops.reservable_period_days] ~ publish_to_dt show 'TEL'
         *
         * 表示開始日時 (shops.early_reservable_tel_flg) OFF
         *      [today + shops.reservable_period_days] ~ publish_to_dt show 'X'
         *
         */
        if (isset($startTEL)) {
            foreach ($arrayDatetime as $key => $value) {
                if (strtotime($startTEL) > strtotime($value['day'])) {
                    continue;
                }

                if ($dataShop->early_reservable_tel_flg == 1) {
                    $arrayDatetime[$key]['tel'] = true; // show TEL
                    continue;
                }

                $arrayDatetime[$key]['count'] = 0; // show X
            }
        }

        return compact('arrayDatetime', 'timeRange');
    }

    public function totalFrameWithPublishDatetime($dataShop, $date) {
        $timeFramesInDay = $this->shopInfoRepository->getNumFramesWithDeny($dataShop);
        $weedDay = getWeekDay($date);

        $publishFromDate = Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_from_dt)->format('Y-m-d');
        $publishFromTime = Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_from_dt)->format('H:i:s');
        $publishToDate = !empty($dataShop->publish_to_dt) ? Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_to_dt)->format('Y-m-d') : null;
        $publishToTime = !empty($dataShop->publish_to_dt) ? Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_to_dt)->format('H:i:s') : null;
        $operationTimeWithDeny = $this->getOperationTimeRangeOfDay($dataShop, $weedDay, true);
        $today = date('Y-m-d');
        $now = date('H:i:s');

        // publish from time
        if ($publishFromDate == $date && $publishFromDate == $today) {
            // check deny & publish_from
            $endTime = strtotime($publishFromTime) > strtotime($now) ? $publishFromTime : $now;
            $totalFrames = sizeof($operationTimeWithDeny) - numberOverlapped($operationTimeWithDeny, [[0, toMinute($endTime)]]);
        } else if (!empty($publishToDate) && $date == $publishToDate) {
            $totalFrames = sizeof($operationTimeWithDeny) - numberOverlapped($operationTimeWithDeny, [[toMinute($publishToTime), 1440]]);
        } else {
            $totalFrames = (int)$timeFramesInDay[$weedDay];
        }

        return $totalFrames;
    }

    public function handleStock($shop_id, $date)
    {
        $dataShop = $this->shopInfoRepository->findById($shop_id);
        $totalReserveNumByDate = $this->getTotalReserveNum($dataShop->id, Carbon::createFromFormat('Y-m-d', $date)->format('Y/m/d'));
        $totalFrames = $this->totalFrameWithPublishDatetime($dataShop, $date);
        $stock = (int)$dataShop->reservation_frame_num * $totalFrames - $totalReserveNumByDate[$date];
        if ($stock < 0) return 0;

        return $stock;
    }

    /**
     * @param $dataShop
     * @return int
     */
    public function getNumberMonthsNext($dataShop)
    {
        if (empty($dataShop->publish_to_dt)) {
            return 0;
        }

        $publishToDate = Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_to_dt);

        return Carbon::now()->diffInMonths($publishToDate) + 1;
    }
}

