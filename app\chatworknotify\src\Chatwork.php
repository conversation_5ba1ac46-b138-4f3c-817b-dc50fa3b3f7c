<?php

namespace App\chatworknotify\src;


use App\chatworknotify\src\Helpers\ChatworkAPI;

/**
 * Class Chatwork
 * @package App\Helpers
 */
class Chatwork
{
    /**
     * @var ChatworkAPI
     */
    protected $chatworkApi;
    protected $roomId;

    public function __construct()
    {
        $this->roomId = config('chatwork.room_id');
        $this->chatworkApi = new ChatworkAPI();
    }

    /**
     * @param $request
     * @param $exception
     * @return bool
     * @throws \Exception
     * Push exception handle
     */
    public function writeException($request, $exception)
    {
        $message = "[info][title]Url: " . $request->fullUrl() . " - " . date('Y-m-d H:i:s') . "[/title]" . $exception->getMessage()
            . "[code]" . $exception->getTraceAsString() . "[/code][/info]";

        return $this->writeMessage($message);
    }
    /**
     * @param null $message
     * @param bool $toAll
     * @return bool
     * @throws \Exception
     * Post message
     */
    public function writeMessage($message = null, $toAll = false)
    {
        // Parameter settings
        $param = array();
        $param['room_id'] = $this->roomId;
        $param['body'] = $toAll ? $this->_buildToAll($message) : $message;
        $objParam = $this->chatworkApi->createParams($param);

        // Write execution
        $result = (array)$this->chatworkApi->postRoomMessage($objParam);

        if (empty($result['message_id'])) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param $title
     * @param $message
     * @param bool $toAll
     * @return bool
     * @throws \Exception
     * Post messages info
     */
    public function writeMessageInfo($title, $message, $toAll = false)
    {
        // Parameter settings
        $param = array();
        $param['room_id'] = $this->roomId;
        $message = $this->_buildInfo($title, $message);
        $param['body'] = $toAll ? $this->_buildToAll($message) : $message;
        $objParam = $this->chatworkApi->createParams($param);

        // Write execution
        $result = (array)$this->chatworkApi->postRoomMessage($objParam);

        if (empty($result['message_id'])) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param $message
     * @param bool $toAll
     * @return bool
     * @throws \Exception
     * Post messages code
     */
    public function writeMessageCode($message, $toAll = false)
    {
        // Parameter settings
        $param = array();
        $param['room_id'] = $this->roomId;
        $message = $this->_buildCode($message);
        $param['body'] = $toAll ? $this->_buildToAll($message) : $message;
        $objParam = $this->chatworkApi->createParams($param);

        // Write execution
        $result = (array)$this->chatworkApi->postRoomMessage($objParam);

        if (empty($result['message_id'])) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param $message
     * @param bool $mention
     * @return string
     * Build message to all
     */
    protected function _buildToAll($message, $mention = true)
    {
        $toAllText = $mention ? '[toall]' : 'TO ALL >>>';
        return "{$toAllText}\n{$message}";
    }

    /**
     * @param $title
     * @param $message
     * @return string
     * Build message info
     */
    protected function _buildInfo($title, $message)
    {
        $message = "[info][title] {$title} [/title]" . $message . "[/info]";

        return "$message";
    }

    /**
     * @param $message
     * @return string
     * Build message code
     */
    protected function _buildCode($message)
    {
        $message = "[code] {$message} [/code]";

        return "$message";
    }
}
