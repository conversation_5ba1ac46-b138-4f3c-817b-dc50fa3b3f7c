<?php

namespace App\Validators\Backend;

use App\Model\Entities\ReserveDenials;
use App\Validators\Base\BaseValidator;

/**
 * Class ReserveDenial
 * @package App\Validator
 */
class ReserveDenial extends BaseValidator
{
    /**
     * @param $data
     * @return bool
     */
    public function validate($data)
    {
        $rules = array(
            'shop_id' => 'required',
            'data.*.denial_time_from' => 'required|check_reserve_denials_from|unique_period_time',
            'data.*.denial_time_to' => 'required|date_format:H:i'
        );

        $messages = [
            'shop_id.required' => 'shopは必須です。',
            'data.*.denial_time_from.required' => '受入不可時間FROM は必須です',
            'data.*.denial_time_to.required' => '受入不可時間TO は必須です',
            'data.*.denial_time_from.check_reserve_denials_from' => '受入不可終了時間は受入不可開始時間より後の日付にしてください。',
            'data.*.denial_time_to.date_format' => '受入不可時間TOが不正です',
            'data.*.denial_time_from.date_format' => '受入不可時間FROM が不正です',
            'data.*.denial_time_from.unique_period_time' => '時間帯が重複しています。他の時間帯を選択してください。',
        ];

        return $this->_addRules($rules, $messages)->with($data)->passes();
    }
}

