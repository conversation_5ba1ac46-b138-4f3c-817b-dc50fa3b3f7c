<?php

namespace App\Model\Entities;

use App\Model\Base\Auth\User;
use App\Model\Base\CustomSoftDeletes;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;

class  ShopInfo extends User
{
    use Notifiable;
    use \App\Model\Presenters\ShopInfo;

    protected $table = "shops";
    protected $hidden = ['password'];
    public $timestamps = false;

    public function getAuthPassword()
    {
        return $this->shop_user_pwd;
    }

    public function setLoginPasswordAttribute($value)
    {
        $this->attributes['shop_user_pwd'] = genPassword($value);
    }

    public function setShopUserPwdAttribute($value)
    {
        $this->attributes['shop_user_pwd'] = genPassword($value);
    }

    public function findById($id) {
        return $this->where('id', $id)->first();
    }
}

