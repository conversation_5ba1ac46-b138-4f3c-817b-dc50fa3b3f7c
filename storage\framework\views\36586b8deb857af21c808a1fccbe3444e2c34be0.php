<!DOCTYPE html>
<html lang="ja">
<?php echo $__env->make('layouts.backend.elements.structures.head', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<body class="<?php echo e(getBodyClass()); ?>">
<?php if(backendGuard()->check()): ?>
    <?php echo $__env->make('layouts.backend.elements.structures.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php endif; ?>
<div class="main flex flex-column flex-root">
    <div class="page flex flex-row flex-column-fluid">
        <!-- start::Wrapper -->
        <div class="wrapper flex flex-column flex-row-fluid">
            <?php echo $__env->yieldContent('content'); ?>
            <?php echo $__env->make('layouts.backend.elements.structures.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
        <!-- end::Wrapper -->
    </div>
</div>
<?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\reserve_one_system\app\Views/layouts/backend/layouts/error.blade.php ENDPATH**/ ?>