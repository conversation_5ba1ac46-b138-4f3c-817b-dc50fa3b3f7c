<?php

namespace App\Model\Base;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Arr;
/**
 * Class CustomBuilder
 */
class CustomQueryBuilder extends Builder
{

    public function getCountForPagination($columns = ['*'])
    {
        try {
            return parent::getCountForPagination($columns); // TODO: Change the autogenerated stub
        } catch (\Exception $exception) {
            DB::logQuery($exception->getSql(), $exception->getBindings(), 0);

        }
        return 0;
    }


    public function aggregate($function, $columns = ['*'])
    {
        try {
            return parent::aggregate($function, $columns); // TODO: Change the autogenerated stub
        } catch (\Exception $exception) {
            DB::logQuery($exception->getSql(), $exception->getBindings(), 0);
        }
        return 0;
    }

    public function whereRaw($sql, $bindings = [], $boolean = 'and')
    {
        $matches = [];
        preg_match_all('/\:\w+/', $sql, $matches);
        $newBindings = [];
        if (!Arr::get($matches, 0)) {
            return parent::whereRaw($sql, $bindings, $boolean); // TODO: Change the autogenerated stub
        }

        $matches = Arr::get($matches, 0);
        foreach ($matches as $key) {
            $sql = str_replace_first($key, '?', $sql);
            $newKey = str_replace(':', '', $key);
            $newBindings[] = Arr::get($bindings, $newKey);
        }
        return parent::whereRaw($sql, $newBindings, $boolean); // TODO: Change the autogenerated stub
    }

}
