$(function () {
  /* alert
   * --------------------------------------------------------- */
   function confirmDeleteDisp() {
     if(window.confirm('登録がある場合、空欄となります。本当に削除しますか？')){
       window.alert('削除しました。');
 	   }
 	   else {
 		   window.alert('キャンセルされました。');
     }
   }
   $('.js-confirm-delete').on('click',function(){
     confirmDeleteDisp();
   });

  /* login input
   * ------------------------------------------------------- */
  $('.form-login-input .form-control').each(function() {
    let error;
    let value = $(this).val();
    if(value == "") {
      error = true;
    } else if(!value.match(/[^\s\t]/)) {
      error = true;
    }
    if(!error) {
      $(this).addClass('form-entered');
    }
  });

  $('.form-login-input input.form-control').on('keyup',function(e){
    let error;
    let value = $(this).val();
    if(value == "") {
      error = true;
    } else if(!value.match(/[^\s\t]/)) {
      error = true;
    }
    if(error) {
      $(this).removeClass('form-entered');
    } else {
      $(this).addClass('form-entered');
    }
  });

  /* select box
   * ------------------------------------------------------- */
   $('.form-select select').each(function() {
     let value = $(this).val();
     if(value == "") {
       $(this).addClass('no-value');
     }
   });
   $(document).on('change', '.form-select select' ,function(e){
     let value = $(this).val();
     if(value == "") {
       $(this).addClass('no-value');
     } else {
       $(this).removeClass('no-value');
     }
   });
   $('form').change(function(e) {
     $('.form-control-address, .form-control-city').each(function() {
       let value = $(this).val();
       if(value == "") {
         $(this).addClass('no-value');
       } else {
         $(this).removeClass('no-value');
       }
     });
   });

   /* readonly control
   * ------------------------------------------------------- */
   $(document).on('change', '.form-readonly-control' ,function(e){
     let val = $(e.target).val();
     let target = $(e.target).data('readonly-target');
     if (val > 0) {
       $('.readonly-target-' + target).attr('readonly', false);
     } else {
       $('.readonly-target-' + target).attr('readonly', true);
     }
   });
   $('.form-readonly-control').each(function() {
     let name = $(this).attr('name');
     let val = $('input[name="'+name+'"]:checked').val();
     let target = $(this).data('readonly-target');
     if (val > 0) {
       $('.readonly-target-' + target).attr('readonly', false);
     } else {
       $('.readonly-target-' + target).attr('readonly', true);
     }
   });

   $(document).on('change', '.form-disabled-control-field .form-checkbox-input' ,function(e){
     let target = $(this).closest('.form-disabled-control-field').find('select');
     if ($(this).is(':checked')) {
       $(target).attr('disabled', true);
     } else {
       $(target).attr('disabled', false);
     }
   });

   $('.form-disabled-control-field').each(function() {
     let input = $(this).find('.form-checkbox-input');
     let checked = $(input).attr('checked');
     let target = $(this).find('select');
     if ($(input).is(':checked')) {
       $(target).attr('disabled', true);
     } else {
       $(target).attr('disabled', false);
     }
   });

   /* hidden control
   * ------------------------------------------------------- */
   $(document).on('change', '.form-hidden-control' ,function(e){
     let val = $(e.target).val();
     let target = $(e.target).data('hidden-target');
     if (val > 0) {
       $('.hidden-target-' + target).show();
     } else {
       $('.hidden-target-' + target).hide();
     }
   });
   $('.form-hidden-control').each(function() {
     let name = $(this).attr('name');
     let val = $('input[name="'+name+'"]:checked').val();
     let target = $(this).data('hidden-target');
     if (val > 0) {
       $('.hidden-target-' + target).show();
     } else {
       $('.hidden-target-' + target).hide();
     }
   });

   /* all checkbox
    * ------------------------------------------------------- */
    $('.all-select-btn').on('click',function(e){
      var target = $(this).data('target'),
          aria = $(this).attr('aria-checked');
      if (aria == 'true') {
        $('#'+target+' input[type="checkbox"]').each(function() {
          $(this).prop('checked', false);
        });
        $(this).attr('aria-checked', false);
      } else {
        $('#'+target+' input[type="checkbox"]').each(function() {
          $(this).prop('checked', true);
        });
        $(this).attr('aria-checked', true);
      }
    });


    /* datepicker
     * ------------------------------------------------------- */
    // sample today set
    var today = new Date().toLocaleString({ timeZone: 'Asia/Tokyo' });
    $(window).on("focus", function () {
      $(document.activeElement).blur();
    });
    var $datepicker = $('.datepicker').pickadate({
      format: "yyyy/mm/dd",
      // min: today,
      onSet: function(context) {
        //console.log('Just set stuff:', context)
      }
    });
});
