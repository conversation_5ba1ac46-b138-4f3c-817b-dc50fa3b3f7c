<?php

namespace App\Http\Middleware\Backend;

use Closure;

/**
 * Class CheckPermission
 * @package App\Http\Middleware\Backend
 */
class CheckPermission
{
    public function handle($request, Closure $next, $guard = null)
    {
        if (shopGuard()->user()) {
            $banRoutes = ['shop.create', 'shop.store' ,'shop.index'];
            $currentRoute = $request->route() ? $request->route()->getName() : null;

            if(in_array($currentRoute, $banRoutes)) {
                return redirect()->to(buildShopInfoUrl())->withErrors(trans('messages.not_permission'));
            }
            elseif (!empty(request('shop')) && request('shop') != shopGuard()->user()->id) {
                return redirect()->to(buildShopInfoUrl())->withErrors(trans('messages.not_permission'));
            }
            elseif (!empty(request('shop_id')) && request('shop_id') != shopGuard()->user()->id) {
                return redirect()->to(buildShopInfoUrl())->withErrors(trans('messages.not_permission'));
            }

            return $next($request);
        }
        return $next($request);
    }
}
