@extends('layouts.backend.layouts.auth')
@section('content')
    <div class="login-card vh-100 flex">
        <div class="login-card-inner container">
            <h1><img src="{{ public_url('images/backend/logo.svg') }}" alt="RESERVE ONE Based on DIGITALEYES" width="245" height="50"></h1>
            <div class="login-card-body">
                {{MyForm::open(['route'=>'backend.login','class'=>'form-signin'])}}
                     @include('layouts.backend.elements.messages')
                    <div class="form-block">
                        <div class="form-inner">
                            <div class="form-group">
                                <div class="form-field">
                                    <div class="form-login-input form-login-input--email">
                                        {!! MyForm::text('email', "", ['class' => 'form-control', 'defaultClass'=> false]) !!}
                                        <label class="input-name">EMAIL</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-field">
                                    <div class="form-login-input form-login-input--password">
                                        {!! MyForm::password('password', ['class' => 'form-control', 'defaultClass'=> false]) !!}
                                        <label class="input-name">PASSWORD</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-btn-area">
                            <button name="Submit" type="Submit"  class="btn btn-lg btn-success btn-block login-btn">ログイン</button>
                        </div>
                    </div>
                {!! MyForm::hidden('return_url') !!}
                {!! MyForm::close() !!}
            </div>
        </div>
    </div>
@stop
