<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

/**
 * Class SuperAdminAuthenticated
 * @package App\Http\Middleware
 */
class SuperAdminAuthenticated extends Authenticated
{
    /**
     *
     */
    public function init()
    {
        $this->setGuard($this->_guard());
    }

    public function _guard()
    {
        $guard = backendGuard();
        if(shopGuard()->check()) {
            $guard = shopGuard();
        }
        return $guard;
    }

    /**
     * @param $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    protected function _toLogin($request)
    {
        if (!$this->getGuard()->user()->isSuperAdmin()) {
            return parent::_toLogin($request)->with('error', trans('auth.permission_denied')); // TODO: Change the autogenerated stub
        }
        return parent::_toLogin($request);
    }
}
