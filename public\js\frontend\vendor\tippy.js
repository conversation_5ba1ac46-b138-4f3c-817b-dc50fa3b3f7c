(function(e,t){'object'==typeof exports&&'undefined'!=typeof module?module.exports=t():'function'==typeof define&&define.amd?define(t):e.tippy=t()})(this,function(){'use strict';function t(e){Ie.forEach(function(t){var o=t.popper,i=t.tippyInstance,n=t.settings,r=n.appendTo,s=n.hideOnClick,a=n.trigger;if(r.contains(o)){var p=!0===s||-1!==a.indexOf('focus'),l=!e||o!==e.popper;p&&l&&i.hide(o)}})}function o(t,o){var i=Element.prototype.closest||function(t){for(var o=this;o;){if(e.call(o,t))return o;o=o.parentElement}};return i.call(t,o)}function n(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function r(){var i=function(){De.touch=!0,De.iOS()&&document.body.classList.add('tippy-touch'),De.dynamicInputDetection&&window.performance&&document.addEventListener('mousemove',r)},r=function(){var e;return function(){var t=performance.now();20>t-e&&(De.touch=!1,document.removeEventListener('mousemove',r),!De.iOS()&&document.body.classList.remove('tippy-touch')),e=t}}();document.addEventListener('click',function(e){if(!(e.target instanceof Element))return t();var i=o(e.target,He.TOOLTIPPED_EL),r=o(e.target,He.POPPER);if(r){var s=n(Ie,function(e){return e.popper===r}),a=s.settings.interactive;if(a)return}if(i){var p=n(Ie,function(e){return e.el===i}),l=p.settings,d=l.hideOnClick,c=l.multiple,f=l.trigger;if(!c&&De.touch||!c&&-1!==f.indexOf('click'))return t(p);if(!0!==d||-1!==f.indexOf('click'))return}o(e.target,He.CONTROLLER)||!document.querySelector(He.POPPER)||t()}),document.addEventListener('touchstart',i),window.addEventListener('blur',function(){var t=document,o=t.activeElement;o&&o.blur&&e.call(o,He.TOOLTIPPED_EL)&&o.blur()}),!De.SUPPORTS_TOUCH&&(0<navigator.maxTouchPoints||0<navigator.msMaxTouchPoints)&&document.addEventListener('pointerdown',i)}function s(){return!s.done&&(s.done=!0,r(),!0)}function a(e){window.requestAnimationFrame(function(){setTimeout(e,0)})}function p(e){for(var t=[!1,'webkit'],o=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<t.length;n++){var i=t[n],r=i?''+i+o:e;if('undefined'!=typeof window.document.body.style[r])return r}return null}function l(e,t){return Array.prototype.findIndex?e.findIndex(t):e.indexOf(n(e,t))}function d(e){var t=e.getAttribute('title');t&&e.setAttribute('data-original-title',t),e.removeAttribute('title')}function c(e){var t=e.getBoundingClientRect();return 0<=t.top&&0<=t.left&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)}function f(e,t){t?window.getComputedStyle(t)[p('transform')]:window.getComputedStyle(e).opacity}function m(e,t){e.forEach(function(e){e&&t(e.classList)})}function h(e){return{tooltip:e.querySelector(He.TOOLTIP),circle:e.querySelector(He.CIRCLE),content:e.querySelector(He.CONTENT)}}function u(t,o){t.forEach(function(t){if(t){var i=e.call(t,He.CONTENT),n=i?Ce(o/1.3):o;t.style[p('transitionDuration')]=n+'ms'}})}function g(e){return'visible'===e.style.visibility}function b(){}function y(e){return!!e&&'[object Object]'===e.toString()}function v(e){return e.replace(/-.+/,'')}function E(t){var e,o,i=this,r=n(Ie,function(e){return e.el===i}),s=r.popper,a=r.settings.offset,l=v(s.getAttribute('x-placement')),d=Ce(s.offsetWidth/2),c=Ce(s.offsetHeight/2),f=5,m=document.documentElement.offsetWidth||document.body.offsetWidth,h=t.pageX,u=t.pageY;'top'===l?(e=h-d+a,o=u-2*c):'bottom'===l?(e=h-d+a,o=u+10):'left'===l?(e=h-2*d,o=u-c+a):'right'===l?(e=h+5,o=u-c+a):void 0;('top'===l||'bottom'===l)&&(h+f+d+a>m&&(e=m-f-2*d),0>h-f-d+a&&(e=f)),s.style[p('transform')]='translate3d('+e+'px, '+o+'px, 0)'}function w(e){if(e instanceof Element||y(e))return[e];if(e instanceof NodeList)return[].slice.call(e);if(Array.isArray(e))return e;try{return[].slice.call(document.querySelectorAll(e))}catch(e){return[]}}function O(e,t,o){if(!t)return o();var i=h(e.popper),n=i.tooltip,r=!1,s=function(e){n[e+'EventListener']('webkitTransitionEnd',a),n[e+'EventListener']('transitionend',a)},a=function(t){t.target!==n||r||(r=!0,s('remove'),o())};s('add'),clearTimeout(e._transitionendTimeout),e._transitionendTimeout=setTimeout(function(){r||(s('remove'),o())},t)}function L(e){return e&&'[object Function]'==={}.toString.call(e)}function T(e,t){if(1!==e.nodeType)return[];var o=getComputedStyle(e,null);return t?o[t]:o}function S(e){return'HTML'===e.nodeName?e:e.parentNode||e.host}function k(e){if(!e)return document.body;switch(e.nodeName){case'HTML':case'BODY':return e.ownerDocument.body;case'#document':return e.body;}var t=T(e),o=t.overflow,i=t.overflowX,n=t.overflowY;return /(auto|scroll)/.test(o+n+i)?e:k(S(e))}function A(e){var t=e&&e.offsetParent,o=t&&t.nodeName;return o&&'BODY'!==o&&'HTML'!==o?-1!==['TD','TABLE'].indexOf(t.nodeName)&&'static'===T(t,'position')?A(t):t:e?e.ownerDocument.documentElement:document.documentElement}function x(e){var t=e.nodeName;return'BODY'!==t&&('HTML'===t||A(e.firstElementChild)===e)}function P(e){return null===e.parentNode?e:P(e.parentNode)}function C(e,t){if(!e||!e.nodeType||!t||!t.nodeType)return document.documentElement;var o=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,i=o?e:t,n=o?t:e,r=document.createRange();r.setStart(i,0),r.setEnd(n,0);var s=r.commonAncestorContainer;if(e!==s&&t!==s||i.contains(n))return x(s)?s:A(s);var a=P(e);return a.host?C(a.host,t):C(e,P(t).host)}function D(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:'top',o='top'===t?'scrollTop':'scrollLeft',i=e.nodeName;if('BODY'===i||'HTML'===i){var n=e.ownerDocument.documentElement,r=e.ownerDocument.scrollingElement||n;return r[o]}return e[o]}function I(e,t){var o=2<arguments.length&&void 0!==arguments[2]&&arguments[2],i=D(t,'top'),n=D(t,'left'),r=o?-1:1;return e.top+=i*r,e.bottom+=i*r,e.left+=n*r,e.right+=n*r,e}function H(e,t){var o='x'===t?'Left':'Top',i='Left'==o?'Right':'Bottom';return parseFloat(e['border'+o+'Width'],10)+parseFloat(e['border'+i+'Width'],10)}function R(e,t,o,i){return Pe(t['offset'+e],t['scroll'+e],o['client'+e],o['offset'+e],o['scroll'+e],ze()?o['offset'+e]+i['margin'+('Height'===e?'Top':'Left')]+i['margin'+('Height'===e?'Bottom':'Right')]:0)}function N(){var e=document.body,t=document.documentElement,o=ze()&&getComputedStyle(t);return{height:R('Height',e,t,o),width:R('Width',e,t,o)}}function B(e){return Ge({},e,{right:e.left+e.width,bottom:e.top+e.height})}function W(e){var t={};if(ze())try{t=e.getBoundingClientRect();var o=D(e,'top'),i=D(e,'left');t.top+=o,t.left+=i,t.bottom+=o,t.right+=i}catch(e){}else t=e.getBoundingClientRect();var n={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},r='HTML'===e.nodeName?N():{},s=r.width||e.clientWidth||n.right-n.left,a=r.height||e.clientHeight||n.bottom-n.top,p=e.offsetWidth-s,l=e.offsetHeight-a;if(p||l){var d=T(e);p-=H(d,'x'),l-=H(d,'y'),n.width-=p,n.height-=l}return B(n)}function M(e,t){var o=ze(),i='HTML'===t.nodeName,n=W(e),r=W(t),s=k(e),a=T(t),p=parseFloat(a.borderTopWidth,10),l=parseFloat(a.borderLeftWidth,10),d=B({top:n.top-r.top-p,left:n.left-r.left-l,width:n.width,height:n.height});if(d.marginTop=0,d.marginLeft=0,!o&&i){var c=parseFloat(a.marginTop,10),f=parseFloat(a.marginLeft,10);d.top-=p-c,d.bottom-=p-c,d.left-=l-f,d.right-=l-f,d.marginTop=c,d.marginLeft=f}return(o?t.contains(s):t===s&&'BODY'!==s.nodeName)&&(d=I(d,t)),d}function U(e){var t=e.ownerDocument.documentElement,o=M(e,t),i=Pe(t.clientWidth,window.innerWidth||0),n=Pe(t.clientHeight,window.innerHeight||0),r=D(t),s=D(t,'left'),a={top:r-o.top+o.marginTop,left:s-o.left+o.marginLeft,width:i,height:n};return B(a)}function _(e){var t=e.nodeName;return'BODY'===t||'HTML'===t?!1:'fixed'===T(e,'position')||_(S(e))}function F(e,t,o,i){var n={top:0,left:0},r=C(e,t);if('viewport'===i)n=U(r);else{var s;'scrollParent'===i?(s=k(S(t)),'BODY'===s.nodeName&&(s=e.ownerDocument.documentElement)):'window'===i?s=e.ownerDocument.documentElement:s=i;var a=M(s,r);if('HTML'===s.nodeName&&!_(r)){var p=N(),l=p.height,d=p.width;n.top+=a.top-a.marginTop,n.bottom=l+a.top,n.left+=a.left-a.marginLeft,n.right=d+a.left}else n=a}return n.left+=o,n.top+=o,n.right-=o,n.bottom-=o,n}function q(e){var t=e.width,o=e.height;return t*o}function Y(e,t,o,i,n){var r=5<arguments.length&&void 0!==arguments[5]?arguments[5]:0;if(-1===e.indexOf('auto'))return e;var s=F(o,i,r,n),a={top:{width:s.width,height:t.top-s.top},right:{width:s.right-t.right,height:s.height},bottom:{width:s.width,height:s.bottom-t.bottom},left:{width:t.left-s.left,height:s.height}},p=Object.keys(a).map(function(e){return Ge({key:e},a[e],{area:q(a[e])})}).sort(function(e,t){return t.area-e.area}),l=p.filter(function(e){var t=e.width,i=e.height;return t>=o.clientWidth&&i>=o.clientHeight}),d=0<l.length?l[0].key:p[0].key,c=e.split('-')[1];return d+(c?'-'+c:'')}function z(e,t,o){var i=C(t,o);return M(o,i)}function j(e){var t=getComputedStyle(e),o=parseFloat(t.marginTop)+parseFloat(t.marginBottom),i=parseFloat(t.marginLeft)+parseFloat(t.marginRight),n={width:e.offsetWidth+i,height:e.offsetHeight+o};return n}function V(e){var t={left:'right',right:'left',bottom:'top',top:'bottom'};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function K(e,t,o){o=o.split('-')[0];var i=j(e),n={width:i.width,height:i.height},r=-1!==['right','left'].indexOf(o),s=r?'top':'left',a=r?'left':'top',p=r?'height':'width',l=r?'width':'height';return n[s]=t[s]+t[p]/2-i[p]/2,n[a]=o===a?t[a]-i[l]:t[V(a)],n}function G(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function X(e,t,o){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===o});var i=G(e,function(e){return e[t]===o});return e.indexOf(i)}function Q(e,t,o){var i=void 0===o?e:e.slice(0,X(e,'name',o));return i.forEach(function(e){e['function']&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');var o=e['function']||e.fn;e.enabled&&L(o)&&(t.offsets.popper=B(t.offsets.popper),t.offsets.reference=B(t.offsets.reference),t=o(t,e))}),t}function J(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=z(this.state,this.popper,this.reference),e.placement=Y(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.offsets.popper=K(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position='absolute',e=Q(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function Z(e,t){return e.some(function(e){var o=e.name,i=e.enabled;return i&&o===t})}function $(e){for(var t=[!1,'ms','Webkit','Moz','O'],o=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<t.length-1;n++){var i=t[n],r=i?''+i+o:e;if('undefined'!=typeof document.body.style[r])return r}return null}function ee(){return this.state.isDestroyed=!0,Z(this.modifiers,'applyStyle')&&(this.popper.removeAttribute('x-placement'),this.popper.style.left='',this.popper.style.position='',this.popper.style.top='',this.popper.style[$('transform')]=''),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function te(e){var t=e.ownerDocument;return t?t.defaultView:window}function oe(e,t,o,i){var n='BODY'===e.nodeName,r=n?e.ownerDocument.defaultView:e;r.addEventListener(t,o,{passive:!0}),n||oe(k(r.parentNode),t,o,i),i.push(r)}function ie(e,t,o,i){o.updateBound=i,te(e).addEventListener('resize',o.updateBound,{passive:!0});var n=k(e);return oe(n,'scroll',o.updateBound,o.scrollParents),o.scrollElement=n,o.eventsEnabled=!0,o}function ne(){this.state.eventsEnabled||(this.state=ie(this.reference,this.options,this.state,this.scheduleUpdate))}function re(e,t){return te(e).removeEventListener('resize',t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener('scroll',t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t}function se(){this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=re(this.reference,this.state))}function ae(e){return''!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function pe(e,t){Object.keys(t).forEach(function(o){var i='';-1!==['width','height','top','right','bottom','left'].indexOf(o)&&ae(t[o])&&(i='px'),e.style[o]=t[o]+i})}function le(e,t){Object.keys(t).forEach(function(o){var i=t[o];!1===i?e.removeAttribute(o):e.setAttribute(o,t[o])})}function de(e,t,o){var i=G(e,function(e){var o=e.name;return o===t}),n=!!i&&e.some(function(e){return e.name===o&&e.enabled&&e.order<i.order});if(!n){var r='`'+t+'`';console.warn('`'+o+'`'+' modifier is required by '+r+' modifier in order to work, be sure to include it before '+r+'!')}return n}function ce(e){return'end'===e?'start':'start'===e?'end':e}function fe(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],o=Qe.indexOf(e),i=Qe.slice(o+1).concat(Qe.slice(0,o));return t?i.reverse():i}function me(e,t,o,i){var n=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),r=+n[1],s=n[2];if(!r)return e;if(0===s.indexOf('%')){var a;switch(s){case'%p':a=o;break;case'%':case'%r':default:a=i;}var p=B(a);return p[t]/100*r}if('vh'===s||'vw'===s){var l;return l='vh'===s?Pe(document.documentElement.clientHeight,window.innerHeight||0):Pe(document.documentElement.clientWidth,window.innerWidth||0),l/100*r}return r}function he(e,t,o,i){var n=[0,0],r=-1!==['right','left'].indexOf(i),s=e.split(/(\+|\-)/).map(function(e){return e.trim()}),a=s.indexOf(G(s,function(e){return-1!==e.search(/,|\s/)}));s[a]&&-1===s[a].indexOf(',')&&console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');var p=/\s*,\s*|\s+/,l=-1===a?[s]:[s.slice(0,a).concat([s[a].split(p)[0]]),[s[a].split(p)[1],s.slice(a+1)]];return l=l.map(function(e,i){var n=(1===i?!r:r)?'height':'width',s=!1;return e.reduce(function(e,t){return''===e[e.length-1]&&-1!==['+','-'].indexOf(t)?(e[e.length-1]=t,s=!0,e):s?(e[e.length-1]+=t,s=!1,e):e.concat(t)},[]).map(function(e){return me(e,n,t,o)})}),l.forEach(function(e,t){e.forEach(function(o,i){ae(o)&&(n[t]+=o*('-'===e[i-1]?-1:1))})}),n}function ue(e){return-(e-Re.distance)+'px'}function ge(e){var t=e.el,o=e.popper,i=e.settings,n=i.position,r=i.popperOptions,s=i.offset,l=i.distance,d=i.flipDuration,c=h(o),f=c.tooltip,m=tt({placement:n},r||{},{modifiers:tt({},r?r.modifiers:{},{flip:tt({padding:l+5},r&&r.modifiers?r.modifiers.flip:{}),offset:tt({offset:s},r&&r.modifiers?r.modifiers.offset:{})}),onUpdate:function(){var e=f.style;e.top='',e.bottom='',e.left='',e.right='',e[v(o.getAttribute('x-placement'))]=ue(l)}});if(window.MutationObserver){var u=o.style,g=new MutationObserver(function(){u[p('transitionDuration')]='0ms',e.popperInstance.update(),a(function(){u[p('transitionDuration')]=d+'ms'})});g.observe(o,{childList:!0,subtree:!0,characterData:!0}),e._mutationObservers.push(g)}return new Ze(t,o,m)}function be(e){var t=e.el,o=e.popper,i=e.settings,n=i.appendTo,r=i.followCursor;n.contains(o)||(n.appendChild(o),e.popperInstance?(e.popperInstance.update(),(!r||De.touch)&&e.popperInstance.enableEventListeners()):e.popperInstance=ge(e),r&&!De.touch&&(t.addEventListener('mousemove',E),e.popperInstance.disableEventListeners()))}function ye(e){var t=e.popper,o=e.popperInstance,i=e.settings.stickyDuration,n=function(){return t.style[p('transitionDuration')]=i+'ms'},r=function(){return t.style[p('transitionDuration')]=''};a(function e(){o&&o.scheduleUpdate(),n(),g(t)?window.requestAnimationFrame(e):r()})}function ve(e,t){var o=Ne.reduce(function(o,i){var n=e.getAttribute('data-'+i.toLowerCase())||t[i];return'false'===n&&(n=!1),'true'===n&&(n=!0),isFinite(n)&&!isNaN(parseFloat(n))&&(n=parseFloat(n)),'string'==typeof n&&'['===n.trim().charAt(0)&&(n=JSON.parse(n)),o[i]=n,o},{});return tt({},t,o)}function Ee(e,t,o){var i=o.position,n=o.distance,r=o.arrow,s=o.animateFill,a=o.inertia,p=o.animation,l=o.arrowSize,d=o.size,c=o.theme,f=o.html,m=o.zIndex,h=o.interactive,u=document.createElement('div');u.setAttribute('class','tippy-popper'),u.setAttribute('role','tooltip'),u.setAttribute('aria-hidden','true'),u.setAttribute('id','tippy-tooltip-'+e),u.style.zIndex=m;var g=document.createElement('div');if(g.setAttribute('class','tippy-tooltip tippy-tooltip--'+d+' leave'),g.setAttribute('data-animation',p),c.split(' ').forEach(function(e){g.classList.add(e+'-theme')}),r){var b=document.createElement('div');b.setAttribute('class','arrow-'+l),b.setAttribute('x-arrow',''),g.appendChild(b)}if(s){g.setAttribute('data-animatefill','');var y=document.createElement('div');y.setAttribute('class','leave'),y.setAttribute('x-circle',''),g.appendChild(y)}a&&g.setAttribute('data-inertia',''),h&&g.setAttribute('data-interactive','');var E=document.createElement('div');if(E.setAttribute('class','tippy-tooltip-content'),f){var w;f instanceof Element?(E.appendChild(f),w='#'+f.id||'tippy-html-template'):(E.innerHTML=document.getElementById(f.replace('#','')).innerHTML,w=f),u.classList.add('html-template'),h&&u.setAttribute('tabindex','-1'),g.setAttribute('data-template-id',w)}else E.innerHTML=t;return g.style[v(i)]=ue(n),g.appendChild(E),u.appendChild(g),u}function we(e,t,o,i){var n=[];return'manual'===e?n:(t.addEventListener(e,o.handleTrigger),n.push({event:e,handler:o.handleTrigger}),'mouseenter'===e&&(De.SUPPORTS_TOUCH&&i&&(t.addEventListener('touchstart',o.handleTrigger),n.push({event:'touchstart',handler:o.handleTrigger}),t.addEventListener('touchend',o.handleMouseleave),n.push({event:'touchend',handler:o.handleMouseleave})),t.addEventListener('mouseleave',o.handleMouseleave),n.push({event:'mouseleave',handler:o.handleMouseleave})),'focus'===e&&(t.addEventListener('blur',o.handleBlur),n.push({event:'blur',handler:o.handleBlur})),n)}function Oe(e,t,o){if(!t.getAttribute('x-placement'))return!0;var i=e.clientX,n=e.clientY,r=o.interactiveBorder,s=o.distance,a=t.getBoundingClientRect(),p=v(t.getAttribute('x-placement')),l=r+s,d={top:a.top-n>r,bottom:n-a.bottom>r,left:a.left-i>r,right:i-a.right>r};return'top'===p?d.top=a.top-n>l:'bottom'===p?d.bottom=n-a.bottom>l:'left'===p?d.left=a.left-i>l:'right'===p?d.right=i-a.right>l:void 0,d.top||d.bottom||d.left||d.right}function Le(e,t,i){var n,r,s=this,a=i.position,p=i.delay,l=i.duration,d=i.interactive,c=i.interactiveBorder,f=i.distance,m=i.hideOnClick,h=i.trigger,u=i.touchHold,b=i.touchWait,y=function(){clearTimeout(n),clearTimeout(r)},v=function(){if(y(),!g(t)){var e=Array.isArray(p)?p[0]:p;p?n=setTimeout(function(){return s.show(t)},e):s.show(t)}},E=function(e){return s.callbacks.wait?s.callbacks.wait.call(t,v,e):v()},w=function(){y();var e=Array.isArray(p)?p[1]:p;p?r=setTimeout(function(){return s.hide(t)},e):s.hide(t)};return{handleTrigger:function(o){var i='mouseenter'===o.type&&De.SUPPORTS_TOUCH&&De.touch;if(!(i&&u)){var n='click'===o.type;n&&g(t)&&'persistent'!==m?w():E(o),i&&De.iOS()&&e.click&&e.click()}},handleMouseleave:function(n){if(!('mouseleave'===n.type&&De.SUPPORTS_TOUCH&&De.touch&&u)){if(d){var r=function n(r){var s=function(){document.body.removeEventListener('mouseleave',w),document.removeEventListener('mousemove',n),w()},a=o(r.target,He.TOOLTIPPED_EL),p=o(r.target,He.POPPER)===t,l=-1!==h.indexOf('click');return a&&a!==e?s():void(p||a===e||l||Oe(r,t,i)&&s())};return document.body.addEventListener('mouseleave',w),void document.addEventListener('mousemove',r)}w()}},handleBlur:function(e){!e.relatedTarget||De.touch||o(e.relatedTarget,He.POPPER)||w()}}}function Te(e){return e.arrow&&(e.animateFill=!1),e.appendTo&&'function'==typeof e.appendTo&&(e.appendTo=e.appendTo()),e}function Se(e){var t=this;return e.reduce(function(e,o){var i=ot,n=tt({},Te(t.settings.performance?t.settings:ve(o,t.settings)));'function'==typeof n.html&&(n.html=n.html(o));var r=n.html,s=n.trigger,a=n.touchHold,p=n.dynamicTitle,l=o.getAttribute('title');if(!l&&!r)return e;o.setAttribute('data-tooltipped',''),o.setAttribute('aria-describedby','tippy-tooltip-'+i),d(o);var c=Ee(i,l,n),f=Le.call(t,o,c,n),m=[];s.trim().split(' ').forEach(function(e){return m=m.concat(we(e,o,f,a))});var u;if(p&&window.MutationObserver){var g=h(c),b=g.content;u=new MutationObserver(function(){var e=o.getAttribute('title');e&&(b.innerHTML=e,d(o))}),u.observe(o,{attributes:!0})}return e.push({id:i,el:o,popper:c,settings:n,listeners:m,tippyInstance:t,_mutationObservers:[u]}),ot++,e},[])}function ke(e,t){return y(e)&&(e={refObj:!0,attributes:e.attributes||{},getBoundingClientRect:e.getBoundingClientRect,clientWidth:e.clientWidth,clientHeight:e.clientHeight,setAttribute:function(t,o){e.attributes[t]=o},getAttribute:function(t){return e.attributes[t]},removeAttribute:function(t){delete e.attributes[t]},addEventListener:function(){},removeEventListener:function(){},classList:{classNames:{},add:function(t){e.classList.classNames[t]=!0},remove:function(t){return e.classList.classNames[t]=!1,!0},contains:function(t){return!!e.classList.classNames[t]}}}),new it(e,t)}var Ae=Math.min,xe=Math.floor,Pe=Math.max,Ce=Math.round,De={};'undefined'!=typeof window&&(De.SUPPORTED='requestAnimationFrame'in window,De.SUPPORTS_TOUCH='ontouchstart'in window,De.touch=!1,De.dynamicInputDetection=!0,De.iOS=function(){return /iPhone|iPad|iPod/.test(navigator.userAgent)&&!window.MSStream});var Ie=[],He={POPPER:'.tippy-popper',TOOLTIP:'.tippy-tooltip',CONTENT:'.tippy-tooltip-content',CIRCLE:'[x-circle]',ARROW:'[x-arrow]',TOOLTIPPED_EL:'[data-tooltipped]',CONTROLLER:'[data-tippy-controller]'},Re={html:!1,position:'top',animation:'shift',animateFill:!0,arrow:!1,arrowSize:'regular',delay:0,trigger:'mouseenter focus',duration:350,interactive:!1,interactiveBorder:2,theme:'dark',size:'regular',distance:10,offset:0,hideOnClick:!0,multiple:!1,followCursor:!1,inertia:!1,flipDuration:350,sticky:!1,stickyDuration:200,appendTo:function(){return document.body},zIndex:9999,touchHold:!1,performance:!1,dynamicTitle:!1,popperOptions:{}},Ne=De.SUPPORTED&&Object.keys(Re),Be={};if('undefined'!=typeof Element){var We=Element.prototype;Be=We.matches||We.matchesSelector||We.webkitMatchesSelector||We.mozMatchesSelector||We.msMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),o=t.length;0<=--o&&t.item(o)!==this;);return-1<o}}for(var e=Be,Me='undefined'!=typeof window&&'undefined'!=typeof document,Ue=['Edge','Trident','Firefox'],_e=0,Fe=0;Fe<Ue.length;Fe+=1)if(Me&&0<=navigator.userAgent.indexOf(Ue[Fe])){_e=1;break}var i,qe=Me&&window.Promise,Ye=qe?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},_e))}},ze=function(){return void 0==i&&(i=-1!==navigator.appVersion.indexOf('MSIE 10')),i},je=function(e,t){if(!(e instanceof t))throw new TypeError('Cannot call a class as a function')},Ve=function(){function e(e,t){for(var o,n=0;n<t.length;n++)o=t[n],o.enumerable=o.enumerable||!1,o.configurable=!0,'value'in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}return function(t,o,i){return o&&e(t.prototype,o),i&&e(t,i),t}}(),Ke=function(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e},Ge=Object.assign||function(e){for(var t,o=1;o<arguments.length;o++)for(var i in t=arguments[o],t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},Xe=['auto-start','auto','auto-end','top-start','top','top-end','right-start','right','right-end','bottom-end','bottom','bottom-start','left-end','left','left-start'],Qe=Xe.slice(3),Je={FLIP:'flip',CLOCKWISE:'clockwise',COUNTERCLOCKWISE:'counterclockwise'},Ze=function(){function e(t,o){var i=this,n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};je(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=Ye(this.update.bind(this)),this.options=Ge({},e.Defaults,n),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=o&&o.jquery?o[0]:o,this.options.modifiers={},Object.keys(Ge({},e.Defaults.modifiers,n.modifiers)).forEach(function(t){i.options.modifiers[t]=Ge({},e.Defaults.modifiers[t]||{},n.modifiers?n.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return Ge({name:e},i.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&L(e.onLoad)&&e.onLoad(i.reference,i.popper,i.options,e,i.state)}),this.update();var r=this.options.eventsEnabled;r&&this.enableEventListeners(),this.state.eventsEnabled=r}return Ve(e,[{key:'update',value:function(){return J.call(this)}},{key:'destroy',value:function(){return ee.call(this)}},{key:'enableEventListeners',value:function(){return ne.call(this)}},{key:'disableEventListeners',value:function(){return se.call(this)}}]),e}();Ze.Utils=('undefined'==typeof window?global:window).PopperUtils,Ze.placements=Xe,Ze.Defaults={placement:'bottom',eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t=e.placement,o=t.split('-')[0],i=t.split('-')[1];if(i){var n=e.offsets,r=n.reference,s=n.popper,a=-1!==['bottom','top'].indexOf(o),p=a?'left':'top',l=a?'width':'height',d={start:Ke({},p,r[p]),end:Ke({},p,r[p]+r[l]-s[l])};e.offsets.popper=Ge({},s,d[i])}return e}},offset:{order:200,enabled:!0,fn:function(e,t){var o,i=t.offset,n=e.placement,r=e.offsets,s=r.popper,a=r.reference,p=n.split('-')[0];return o=ae(+i)?[+i,0]:he(i,s,a,p),'left'===p?(s.top+=o[0],s.left-=o[1]):'right'===p?(s.top+=o[0],s.left+=o[1]):'top'===p?(s.left+=o[0],s.top-=o[1]):'bottom'===p&&(s.left+=o[0],s.top+=o[1]),e.popper=s,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,t){var o=t.boundariesElement||A(e.instance.popper);e.instance.reference===o&&(o=A(o));var i=F(e.instance.popper,e.instance.reference,t.padding,o);t.boundaries=i;var n=t.priority,r=e.offsets.popper,s={primary:function(e){var o=r[e];return r[e]<i[e]&&!t.escapeWithReference&&(o=Pe(r[e],i[e])),Ke({},e,o)},secondary:function(e){var o='right'===e?'left':'top',n=r[o];return r[e]>i[e]&&!t.escapeWithReference&&(n=Ae(r[o],i[e]-('right'===e?r.width:r.height))),Ke({},o,n)}};return n.forEach(function(e){var t=-1===['left','top'].indexOf(e)?'secondary':'primary';r=Ge({},r,s[t](e))}),e.offsets.popper=r,e},priority:['left','right','top','bottom'],padding:5,boundariesElement:'scrollParent'},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,o=t.popper,i=t.reference,n=e.placement.split('-')[0],r=xe,s=-1!==['top','bottom'].indexOf(n),a=s?'right':'bottom',p=s?'left':'top',l=s?'width':'height';return o[a]<r(i[p])&&(e.offsets.popper[p]=r(i[p])-o[l]),o[p]>r(i[a])&&(e.offsets.popper[p]=r(i[a])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){var o;if(!de(e.instance.modifiers,'arrow','keepTogether'))return e;var i=t.element;if('string'==typeof i){if(i=e.instance.popper.querySelector(i),!i)return e;}else if(!e.instance.popper.contains(i))return console.warn('WARNING: `arrow.element` must be child of its popper element!'),e;var n=e.placement.split('-')[0],r=e.offsets,s=r.popper,a=r.reference,p=-1!==['left','right'].indexOf(n),l=p?'height':'width',d=p?'Top':'Left',c=d.toLowerCase(),f=p?'left':'top',m=p?'bottom':'right',h=j(i)[l];a[m]-h<s[c]&&(e.offsets.popper[c]-=s[c]-(a[m]-h)),a[c]+h>s[m]&&(e.offsets.popper[c]+=a[c]+h-s[m]),e.offsets.popper=B(e.offsets.popper);var u=a[c]+a[l]/2-h/2,g=T(e.instance.popper),b=parseFloat(g['margin'+d],10),y=parseFloat(g['border'+d+'Width'],10),v=u-e.offsets.popper[c]-b-y;return v=Pe(Ae(s[l]-h,v),0),e.arrowElement=i,e.offsets.arrow=(o={},Ke(o,c,Ce(v)),Ke(o,f,''),o),e},element:'[x-arrow]'},flip:{order:600,enabled:!0,fn:function(e,t){if(Z(e.instance.modifiers,'inner'))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;var o=F(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement),i=e.placement.split('-')[0],n=V(i),r=e.placement.split('-')[1]||'',s=[];switch(t.behavior){case Je.FLIP:s=[i,n];break;case Je.CLOCKWISE:s=fe(i);break;case Je.COUNTERCLOCKWISE:s=fe(i,!0);break;default:s=t.behavior;}return s.forEach(function(a,p){if(i!==a||s.length===p+1)return e;i=e.placement.split('-')[0],n=V(i);var l=e.offsets.popper,d=e.offsets.reference,c=xe,f='left'===i&&c(l.right)>c(d.left)||'right'===i&&c(l.left)<c(d.right)||'top'===i&&c(l.bottom)>c(d.top)||'bottom'===i&&c(l.top)<c(d.bottom),m=c(l.left)<c(o.left),h=c(l.right)>c(o.right),u=c(l.top)<c(o.top),g=c(l.bottom)>c(o.bottom),b='left'===i&&m||'right'===i&&h||'top'===i&&u||'bottom'===i&&g,y=-1!==['top','bottom'].indexOf(i),v=!!t.flipVariations&&(y&&'start'===r&&m||y&&'end'===r&&h||!y&&'start'===r&&u||!y&&'end'===r&&g);(f||b||v)&&(e.flipped=!0,(f||b)&&(i=s[p+1]),v&&(r=ce(r)),e.placement=i+(r?'-'+r:''),e.offsets.popper=Ge({},e.offsets.popper,K(e.instance.popper,e.offsets.reference,e.placement)),e=Q(e.instance.modifiers,e,'flip'))}),e},behavior:'flip',padding:5,boundariesElement:'viewport'},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,o=t.split('-')[0],i=e.offsets,n=i.popper,r=i.reference,s=-1!==['left','right'].indexOf(o),a=-1===['top','left'].indexOf(o);return n[s?'left':'top']=r[o]-(a?n[s?'width':'height']:0),e.placement=V(t),e.offsets.popper=B(n),e}},hide:{order:800,enabled:!0,fn:function(e){if(!de(e.instance.modifiers,'hide','preventOverflow'))return e;var t=e.offsets.reference,o=G(e.instance.modifiers,function(e){return'preventOverflow'===e.name}).boundaries;if(t.bottom<o.top||t.left>o.right||t.top>o.bottom||t.right<o.left){if(!0===e.hide)return e;e.hide=!0,e.attributes['x-out-of-boundaries']=''}else{if(!1===e.hide)return e;e.hide=!1,e.attributes['x-out-of-boundaries']=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var o=t.x,i=t.y,n=e.offsets.popper,r=G(e.instance.modifiers,function(e){return'applyStyle'===e.name}).gpuAcceleration;void 0!==r&&console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');var s,a,p=void 0===r?t.gpuAcceleration:r,l=A(e.instance.popper),d=W(l),c={position:n.position},f={left:xe(n.left),top:xe(n.top),bottom:xe(n.bottom),right:xe(n.right)},m='bottom'===o?'top':'bottom',h='right'===i?'left':'right',u=$('transform');if(a='bottom'==m?-d.height+f.bottom:f.top,s='right'==h?-d.width+f.right:f.left,p&&u)c[u]='translate3d('+s+'px, '+a+'px, 0)',c[m]=0,c[h]=0,c.willChange='transform';else{var g='bottom'==m?-1:1,b='right'==h?-1:1;c[m]=a*g,c[h]=s*b,c.willChange=m+', '+h}var y={"x-placement":e.placement};return e.attributes=Ge({},y,e.attributes),e.styles=Ge({},c,e.styles),e.arrowStyles=Ge({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:'bottom',y:'right'},applyStyle:{order:900,enabled:!0,fn:function(e){return pe(e.instance.popper,e.styles),le(e.instance.popper,e.attributes),e.arrowElement&&Object.keys(e.arrowStyles).length&&pe(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,o,i,n){var r=z(n,t,e),s=Y(o.placement,r,t,e,o.modifiers.flip.boundariesElement,o.modifiers.flip.padding);return t.setAttribute('x-placement',s),pe(t,{position:'absolute'}),o},gpuAcceleration:void 0}}};var $e=function(e,t){if(!(e instanceof t))throw new TypeError('Cannot call a class as a function')},et=function(){function e(e,t){for(var o,n=0;n<t.length;n++)o=t[n],o.enumerable=o.enumerable||!1,o.configurable=!0,'value'in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}return function(t,o,i){return o&&e(t.prototype,o),i&&e(t,i),t}}(),tt=Object.assign||function(e){for(var t,o=1;o<arguments.length;o++)for(var i in t=arguments[o],t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},ot=1,it=function(){function e(t){var o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};$e(this,e),De.SUPPORTED&&(s(),this.state={destroyed:!1},this.selector=t,this.settings=tt({},Re,o),(o.show||o.shown||o.hide||o.hidden)&&console.warn('Callbacks without the `on` prefix are deprecated (with the exception of `wait`). Use onShow, onShown, onHide, and onHidden instead.'),this.callbacks={wait:o.wait,show:o.onShow||o.show||b,shown:o.onShown||o.shown||b,hide:o.onHide||o.hide||b,hidden:o.onHidden||o.hidden||b},this.store=Se.call(this,w(t)),Ie.push.apply(Ie,this.store))}return et(e,[{key:'getPopperElement',value:function(e){try{return n(this.store,function(t){return t.el===e}).popper}catch(t){console.error('[getPopperElement]: Element passed as the argument does not exist in the instance')}}},{key:'getReferenceElement',value:function(e){try{return n(this.store,function(t){return t.popper===e}).el}catch(t){console.error('[getReferenceElement]: Popper passed as the argument does not exist in the instance')}}},{key:'getReferenceData',value:function(e){return n(this.store,function(t){return t.el===e||t.popper===e})}},{key:'show',value:function(e,t){var o=this;if(!this.state.destroyed){var i=n(this.store,function(t){return t.popper===e}),r=h(e),s=r.tooltip,p=r.circle,l=r.content;if(!this.selector.refObj&&!document.body.contains(i.el))return void this.destroy(e);this.callbacks.show.call(e);var d=i.el,c=i.settings,b=c.appendTo,y=c.sticky,v=c.interactive,E=c.followCursor,w=c.flipDuration,L=c.duration,T=void 0===t?Array.isArray(L)?L[0]:L:t;u([e,s,p],0),be(i),e.style.visibility='visible',e.setAttribute('aria-hidden','false'),a(function(){g(e)&&((!E||De.touch)&&(i.popperInstance.update(),u([e],w)),u([s,p],T),p&&(l.style.opacity=1),v&&d.classList.add('active'),y&&ye(i),f(s,p),m([s,p],function(e){e.contains('tippy-notransition')&&e.remove('tippy-notransition'),e.remove('leave'),e.add('enter')}),O(i,T,function(){!g(e)||i._onShownFired||(v&&e.focus(),s.classList.add('tippy-notransition'),i._onShownFired=!0,o.callbacks.shown.call(e))}))})}}},{key:'hide',value:function(e,t){var o=this;if(!this.state.destroyed){this.callbacks.hide.call(e);var i=n(this.store,function(t){return t.popper===e}),r=h(e),s=r.tooltip,a=r.circle,p=r.content,l=i.el,d=i.settings,f=d.appendTo,b=d.sticky,y=d.interactive,v=d.followCursor,w=d.html,L=d.trigger,T=d.duration,S=void 0===t?Array.isArray(T)?T[1]:T:t;i._onShownFired=!1,y&&l.classList.remove('active'),e.style.visibility='hidden',e.setAttribute('aria-hidden','true'),u([s,a,a?p:null],S),a&&(p.style.opacity=0),m([s,a],function(e){e.contains('tippy-tooltip')&&e.remove('tippy-notransition'),e.remove('enter'),e.add('leave')}),w&&-1!==L.indexOf('click')&&c(l)&&l.focus(),O(i,S,function(){g(e)||!f.contains(e)||'1'===getComputedStyle(s).opacity||(l.removeEventListener('mousemove',E),i.popperInstance.disableEventListeners(),f.removeChild(e),o.callbacks.hidden.call(e))})}}},{key:'update',value:function(e){if(!this.state.destroyed){var t=n(this.store,function(t){return t.popper===e}),o=h(e),i=o.content,r=t.el,s=t.settings.html;return s instanceof Element?void console.warn('Aborted: update() should not be used if `html` is a DOM element'):void(i.innerHTML=s?document.getElementById(s.replace('#','')).innerHTML:r.getAttribute('title')||r.getAttribute('data-original-title'),!s&&d(r))}}},{key:'destroy',value:function(e,t){var o=this;if(!this.state.destroyed){var i=n(this.store,function(t){return t.popper===e}),r=i.el,s=i.popperInstance,a=i.listeners,p=i._mutationObservers;g(e)&&this.hide(e,0),a.forEach(function(e){return r.removeEventListener(e.event,e.handler)}),r.setAttribute('title',r.getAttribute('data-original-title')),r.removeAttribute('data-original-title'),r.removeAttribute('data-tooltipped'),r.removeAttribute('aria-describedby'),s&&s.destroy(),p.forEach(function(e){e&&e.disconnect()}),Ie.splice(l(Ie,function(t){return t.popper===e}),1),(void 0===t||t)&&(this.store=Ie.filter(function(e){return e.tippyInstance===o}))}}},{key:'destroyAll',value:function(){var e=this;if(!this.state.destroyed){var t=this.store.length;this.store.forEach(function(o,i){var n=o.popper;e.destroy(n,i===t-1)}),this.store=null,this.state.destroyed=!0}}}]),e}();return ke.Browser=De,ke.Defaults=Re,ke.disableDynamicInputDetection=function(){return De.dynamicInputDetection=!1},ke.enableDynamicInputDetection=function(){return De.dynamicInputDetection=!0},ke});
