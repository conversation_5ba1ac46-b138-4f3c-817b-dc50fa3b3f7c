<?php

namespace App\Validators\Frontend;

use App\Validators\Base\BaseValidator;

/**
 * Class AdminUserInfo
 * @package App\Validator
 */
class ShopInfo extends BaseValidator
{
    /**
     * @param $data
     * @return bool
     */
    public function validateLogin($data)
    {
        $rules = array(
            'email' => 'required|email', // make sure the email is an actual email
            'password' => 'required' // password can only be alphanumeric and has to be greater than 3 characters
        );
        $messages = [
            'email.required' => trans('auth.id_required'),
            'email.email' => trans('auth.email_password_invalid'),
            'password.required' => trans('auth.password_required'),
        ];
        return $this->_addRules($rules, $messages)->with($data)->passes();
    }

    /**
     * @return array
     */
    protected function _getRulesDefault()
    {
        $fileRule = getConfig('file.admin.avatar');
        $imageType = implode(',', $fileRule['ext']);
        $groups = getKeysConfig('user.groups');
        $rules = [
            'email' => 'required|email|max:256' . $this->_getUniqueInDbRule(),
            'login_password' => 'nullable|same:password_confirmation|min:6|max:8',
            'groups' => 'required',
        ];
        foreach ($this->getData('groups', []) as $idx => $datum) {
            $rules += [
                'groups.' . $idx . $this->_getInArrayRule($groups)
            ];
        }
        if ($this->_hasFileUpload('avatar')) {
            $rules += [
                'avatar' => 'mimes:' . $imageType . '|max:' . $fileRule['size']['max'] * 1024,
            ];
        }
        return $rules;
    }

    /**
     * @return array
     */
    protected function _buildCreateRules()
    {
        return [
            'rules' => $this->_buildRules([
                'login_password' => 'required|same:password_confirmation|min:6|max:8',
                'password_confirmation' => 'required|same:password_confirmation|min:6|max:8',
            ])
        ];
    }

    /**
     * @return array
     */
    protected function _buildUpdateRules()
    {
        return parent::_buildUpdateRules(); // TODO: Change the autogenerated stub
    }

    /**
     * @return array
     */
    protected function _buildSearchRules()
    {
        return parent::_buildSearchRules(); // TODO: Change the autogenerated stub
    }

    /**
     * @return array
     */
    protected function _buildDestroyRules()
    {
        return parent::_buildDestroyRules(); // TODO: Change the autogenerated stub
    }


    /**
     * @param $data
     * @return bool
     */
    public function validateResetPassword($data)
    {
        $rules = array(
            'email' => 'required|email', // make sure the email is an actual email
        );
        return $this->_addRules($rules)->with($data)->passes();
    }

    /**
     * @param $data
     * @return bool
     */
    public function validateConfirmReset($data)
    {
        $rules = array(
            'password' => 'required|min:5|max:12|same:password_confirmation',
            'password_confirmation' => 'required',
        );
        $message = [];
        return $this->_addRules($rules, $message)->with($data)->passes();
    }

}
