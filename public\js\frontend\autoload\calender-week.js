var jsonDate = [
    {
        "day": "2022-03-21",
        "count": 3,
        "tel": true
    },
    {
        "day": "2022-03-22",
        "count": 5,
        "tel": false
    },
    {
        "day": "2022-03-23",
        "count": 10,
        "tel": true
    },
    {
        "day": "2022-03-24",
        "count": 22,
        "tel": false
    },
    {
        "day": "2022-03-25",
        "count": 16,
        "tel": true
    },
    {
        "day": "2022-03-26",
        "count": 0,
        "tel": false
    },
    {
        "day": "2022-03-27",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-03-28",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-03-29",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-03-30",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-03-31",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-01",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-02",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-03",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-04",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-05",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-06",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-07",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-08",
        "count": 3,
        "tel": false
    },
    {
        "day": "2022-04-09",
        "count": 1,
        "tel": false
    },
    {
        "day": "2022-04-10",
        "count": 2,
        "tel": false
    },
];

var jsonDateTime = [
    {
        "day": "2022-03-22",
        "timeData": {
            0: 3,
            1: 10,
            2: 0,
            3: 2,
            4: 1,
            5: 1
        }
    },
    {
        "day": "2022-03-24",
        "timeData": {
            0: 10,
            1: 15,
            2: 10,
            3: 1,
            4: 0,
            5: 0
        }
    },
    {
        "day": "2022-03-27",
        "timeData": {
            0: 10,
            1: 15,
            2: 10,
            3: 10,
            4: 10,
            5: 10
        }
    },
    {
        "day": "2022-03-28",
        "timeData": {
            0: 2,
            1: 1,
            2: 2,
            3: 1,
            4: 1,
            5: 1
        }
    },
    {
        "day": "2022-03-29",
        "timeData": {
            0: 10,
            1: 15,
            2: 10,
            3: 1,
            4: 0,
            5: 0
        }
    },
    {
        "day": "2022-03-30",
        "timeData": {
            0: 10,
            1: 15,
            2: 10,
            3: 1,
            4: 0,
            5: 0
        }
    },
    {
        "day": "2022-03-31",
        "timeData": {
            0: 10,
            1: 15,
            2: 10,
            3: 1,
            4: 0,
            5: 0
        }
    },
    {
        "day": "2022-04-01",
        "timeData": {
            0: 2,
            1: 1,
            2: 4,
            3: 5,
            4: 0,
            5: 0
        }
    },
    {
        "day": "2022-04-02",
        "timeData": {
            0: 2,
            1: 1,
            2: 4,
            3: 5,
            4: 0,
            5: 0
        }
    },
    {
        "day": "2022-04-03",
        "timeData": {
            0: 2,
            1: 1,
            2: 4,
            3: 5,
            4: 0,
            5: 0
        }
    },
    {
        "day": "2022-04-04",
        "timeData": {
            0: 2,
            1: 1,
            2: 4,
            3: 5,
            4: 0,
            5: 0
        }
    },
    {
        "day": "2022-04-05",
        "timeData": {
            0: 2,
            1: 1,
            2: 4,
            3: 5,
            4: 0,
            5: 0
        }
    },
];

// 時間帯
const timeRange = {
    0: '10:00〜11:30',
    1: '11:30〜13:00',
    2: '13:00〜14:30',
    3: '14:30〜16:00',
    4: '16:00〜18:30',
    5: '18:30〜20:00',
}

// パラメーター取得
var url = new URL(window.location.href);
var params = url.searchParams;

// カレンダーのセルにアイコンを表示するためのclass命名
function setCellClassName(count, tel) {
    var className = 'reserve-status-none';
    if (tel) {
        className = 'reserve-status-tel';
    } else {
        if (count > 4) {
            className = 'reserve-status-1';
        } else if(count > 2) {
            className = 'reserve-status-2';
        } else if(count > 0) {
            className = 'reserve-status-3';
        } else if(count == 'tel') {
            className = 'reserve-status-tel';
        }
    }
    return className;
}

// 時間選択エリアの生成
function createReserveTime(date) {
    var cell_date = new Date(date);
    var format_date = cell_date.getFullYear()+'-'+("0" + (cell_date.getMonth()+1)).slice(-2)+'-'+("0" + cell_date.getDate()).slice(-2);
    const isInvalidDate = (date) => Number.isNaN(date.getTime());
    // タイトル
    if (!isInvalidDate(date)) {
        var title_date = (date.getMonth()+1)+'/'+(date.getDate());
        document.getElementById('time-select-title').textContent = title_date+'の時間選択';
    }
    // 時間選択リスト
    var indexDate = jsonDateTime.filter(function(item, index){
        if (item.day == format_date) return item;
    });
    var targetElm = document.getElementById('time-select-list');
    targetElm.innerHTML = '';
    if (indexDate.length > 0) {
        var timeData = indexDate[0].timeData;
        for (let key in timeData) {
            var val = timeData[key];
            var className = setCellClassName(val);
            var li = '<li><a href="" class="'+className+'"><span class="time-range">'+timeRange[key]+'</span></a></li>';
            $(li).appendTo(targetElm);
        }
    } else {
        if (isInvalidDate(date)) {
            $('<p>日付を選択してください。</p>').appendTo(targetElm);
        } else {
            $('<p>選択できません</p>').appendTo(targetElm);
        }
    }
}

// ページ読み込み時
createReserveTime(new Date(params));


/* calender
* --------------------------------------------------------- */
var calendar = document.getElementById('calendar');
if (calendar != null) {
    var calendar = new FullCalendar.Calendar(calendar, {
        initialView: 'dayGridWeek',
        locale: 'ja',
        height: 175,
        initialDate: params.get('date'),
        dayHeaderFormat: {
            weekday: 'short'
        },
        firstDay: 1,
        headerToolbar: {
            left: 'prev',
            center: 'title',
            right: 'next'
        },
        titleFormat: {
            year: 'numeric'
        },
        buttonText: {
            prev: '前の1週間',
            next: '次の1週間',
        },
        dayMaxEvents: true,
        dayCellClassNames: function(info) {
            var cell_date = new Date(info.date);
            var format_date = cell_date.getFullYear()+'-'+("0" + (cell_date.getMonth()+1)).slice(-2)+'-'+("0" + cell_date.getDate()).slice(-2);
            var indexDate = jsonDate.filter(function(item, index){
                if (item.day == format_date) return item;
            });
            if (indexDate.length > 0) {
                var className = setCellClassName(indexDate[0].count, indexDate[0].tel);
            } else {
                var className = 'reserve-status-none';
            }
            if (params.get('date') == format_date) {
                return [ className, 'reserve-active-day' ]
            } else {
                return [ className ]
            }

        },
        dayCellContent: function (e) {
            var cell_month = e.date.getMonth() + 1;
            var cell_day = e.date.getDate();
            e.dayNumberText = cell_month+'/'+cell_day;
        },
        dateClick: function (info) {
            if ($(info.dayEl).hasClass('fc-day-past')) return;
            if ($(info.dayEl).hasClass('reserve-status-tel')) {
                alert('お電話にてご確認ください。'); return;
            }
            $('.fc .fc-daygrid-day').removeClass('reserve-active-day');
            $(info.dayEl).addClass('reserve-active-day');
            createReserveTime(info.date);
        },
        fixedWeekCount: false,
        datesSet: function (dateInfo) {
            var today = new Date();
            today.setHours(0);
            today.setMinutes(0);
            today.setSeconds(0);
            today.setMilliseconds(0);
            var viewDate = calendar.view.currentStart;
            // Past
            var minDate = today;
            if (minDate >= viewDate) {
                $(".fc-prev-button").prop('disabled', true);
                $(".fc-prev-button").addClass('fc-state-disabled');
            }
            else {
                $(".fc-prev-button").removeClass('fc-state-disabled');
                $(".fc-prev-button").prop('disabled', false);
            }
            var maxDate = today.setMonth(today.getMonth() + 2), //最大で表示する未来の月を設定
                maxDate = today.setDate(0);
            var maxDate = new Date(maxDate);

            // Future
            if (maxDate <= viewDate) {
                $(".fc-next-button").prop('disabled', true);
                $(".fc-next-button").addClass('fc-state-disabled');
            } else {
                $(".fc-next-button").removeClass('fc-state-disabled');
                $(".fc-next-button").prop('disabled', false);
            }
        }
    });
    calendar.render();
}
