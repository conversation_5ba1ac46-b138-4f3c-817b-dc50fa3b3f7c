<!-- start::Header -->
<header role="header" class="header">
    <div class="container flex align-items-center">
        <div class="flex align-items-center mr-auto">
            <div class="header-title flex align-items-center">
                <a href="
                @if(shopGuard()->check())
                    {{ route('shop.edit', ['shop' => shopGuard()->user()->id]) }}
                @elseif(backendGuard()->check())
                    {{ route('shop.index') }}
                @endif">
                <img src="{{ public_url('images/backend/logo.svg') }}" alt="RESERVE ONE Based on DIGITALEYES"
                     width="180" height="35"></a>
            </div>
            <div class="header-user-name flex align-items-center">
                @if(shopGuard()->check())
                    <span class="text">ようこそ、{{shortenString(shopGuard()->user()->shop_name)}}さま</span>
                @elseif(backendGuard()->check())
                    <span class="text">ようこそ、管理者さま</span>
                @endif
            </div>
        </div>
        <div class="flex align-items-center ml-auto">
            <nav role="navigation" class="header-navigation">
                <ul class="flex align-items-center">
                    @if(Route::currentRouteName() !== getConstant('ROUTE_SHOP_INDEX'))
                        @if(shopGuard()->check())
                            <li><a href="{{ route('shop.edit', ['shop' => (int)request()->shop_id]) }}" class="{{ getNavActiveClass('shop', 'edit') ? 'is-current' : '' }}">店舗情報</a></li>
                            <li><a href="{{ route('calendar.setting', ['shop_id' => (int)request()->shop]) }}" class="{{ getNavActiveClass('calendar', 'setting') ? 'is-current' : '' }}">カレンダー設定</a></li>
                        @elseif(backendGuard()->check() && Route::currentRouteName() !== 'shop.create')
                            @php $idShop = !empty(request('shop')) ? request('shop') : request('shop_id'); @endphp
                            <li><a href="{{ route('shop.edit', ['shop' => $idShop]) }}" class="{{ getNavActiveClass('shop', 'edit') ? 'is-current' : '' }}">店舗情報</a></li>
                            <li><a href="{{ route('calendar.setting', ['shop_id' => $idShop]) }}" class="{{ getNavActiveClass('calendar', 'setting') ? 'is-current' : '' }}">カレンダー設定</a></li>
                            <li><a href="{{ route('shop.index') }}" class="{{ getNavActiveClass('shop', 'index') ? 'is-current' : '' }}">クライアント一覧</a></li>
                        @endif
                    @endif
                    <li><a href="javascript:void(0);" onclick="document.getElementById('form-logout').submit();">
                            <span class="logout">ログアウト</span>
                        </a></li>
                </ul>
            </nav>
            <div class="header-nav-btn">
                <button type="button" id="header-toggle-btn">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </div>
</header>
{{MyForm::open(['route'=>'backend.logout','class'=>'form-logout', 'id' => 'form-logout'])}}
{!! MyForm::close() !!}
<!-- end::Header -->
