<?php

use App\Database\Migration\CustomBlueprint as Blueprint;
use App\Database\Migration\Schema;

class AlterInsDateAndUpdDateType extends \App\Database\Migration\Create
{
    protected $allTable = ['admin_users', 'shops', 'reserves', 'reserve_denials', 'reserve_tel'];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        foreach ($this->allTable as $value) {
            Schema::table($value, function (Blueprint $table) {
                $table->dateTime(getCreatedAtColumn())->comment(getCreatedAtColumn('comment'))->useCurrent()->change();
                $table->dateTime(getUpdatedAtColumn())->nullable()->comment(getUpdatedAtColumn('comment'))->change();
            });
        }
    }
}
