<?php

use App\Database\Migration\CustomBlueprint as Blueprint;
use App\Database\Migration\Schema;

class CreateAdminUsersTable extends \App\Database\Migration\Create
{
    protected $_table = 'admin_users';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable($this->getTable())) {
            Schema::create($this->getTable(), function (Blueprint $table) {
                $table->increments('id');
                $table->string('email', 500)->comment('メールアドレス');
                $table->string('password', 500)->comment('パスワード');
                $table->actionBy();
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }
}
