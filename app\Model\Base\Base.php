<?php

namespace App\Model\Base;

use App\Services\TraitCacheService;
use Awobaz\Compoships\Database\Eloquent\Model;
use Carbon\Carbon;
use App\Model\Base\CustomBuilder as Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
/**
 * Class Base
 * @package App\Model\Base
 */
class Base extends Model
{
    protected $urlAttributes = [];

    protected $_allowBlankField = false;

    protected $_exceptAllowBlankField = [];

    protected static $_fillabelFromSchema = [];

    /**
     * @var array
     */
    protected static $_destroyRelations = [];

    protected static $_updateRelations = []; // set byDestroy for extend destroyRelations

    protected static $_updateTime = null;
    /**
     * @var bool
     */
    protected $_hasActionBy = true;
    protected $_delRelationsTmp = [];
    use \App\Model\Scopes\Base\Base;
    use \App\Model\Presenters\Base;
    use AutoFillInsIdUpId;
    use TraitCacheService;
    /**
     * @var bool`
     */
    public $timestamps = true;
    /**
     * @var string
     */
    protected $_alias = '';

    protected $primaryKeyAutoIncrement = 'id';
    protected $sequence = '';

    /**
     * @return array
     */
    public function getExceptAllowBlankField()
    {
        return $this->_exceptAllowBlankField;
    }

    /**
     * @param array $exceptAllowBlankField
     */
    public function setExceptAllowBlankField($exceptAllowBlankField)
    {
        $this->_exceptAllowBlankField = $exceptAllowBlankField;
    }

    /**
     * @return string
     */
    public function getPrimaryKeyAutoIncrement()
    {
        return $this->primaryKeyAutoIncrement;
    }

    /**
     * @param string $primaryKeyAutoIncrement
     */
    public function setPrimaryKeyAutoIncrement($primaryKeyAutoIncrement)
    {
        $this->primaryKeyAutoIncrement = $primaryKeyAutoIncrement;
    }


    /**
     * @return string
     */
    public function getSequence()
    {
        return $this->sequence;
    }

    /**
     * @param string $sequence
     */
    public function setSequence($sequence)
    {
        $this->sequence = $sequence;
    }

    /**
     * @return array
     */
    public function getDelRelationsTmp()
    {
        return $this->_delRelationsTmp;
    }

    /**
     * @param array $delRelationsTmp
     */
    public function setDelRelationsTmp($delRelationsTmp)
    {
        $this->_delRelationsTmp = $delRelationsTmp;
    }


    /**
     * @return array
     */
    public static function getDestroyRelations()
    {
        return static::$_destroyRelations;
    }

    /**
     * @param array $destroyRelations
     */
    public static function setDestroyRelations($destroyRelations)
    {
        static::$_destroyRelations = $destroyRelations;
    }

    /**
     * @return array
     */
    public static function getUpdateRelations()
    {
        return Arr::get(static::$_updateRelations, 0, '') == 'byDestroy' ? self::getDestroyRelations() : static::$_updateRelations;
    }

    /**
     * @param $updateRelations
     */
    public static function setUpdateRelations($updateRelations)
    {
        static::$_updateRelations = $updateRelations;
    }

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    public static function getTableName()
    {
        return with(new static)->getTable();
    }

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    public static function newEntity()
    {
        return with(new static);
    }

    /**
     * @param $field
     * @return string
     */
    public function getField($field)
    {
        return $this->getTableName() . '.' . $field;
    }

    /**
     * @return string
     */
    public function getAlias()
    {
        return $this->_alias ? $this->_alias : $this->table;
    }

    /**
     * @param string $alias
     */
    public function setAlias($alias)
    {
        $this->_alias = $alias;
    }

    /**
     * @param $key
     * @return array|\Illuminate\Contracts\Translation\Translator|null|string
     */
    public function getAttributeName($key)
    {
        return transa($this->getAlias(), $key);
    }


    /**
     * @param $key
     * @return array|\Illuminate\Contracts\Translation\Translator|null|string
     */
    public function getFormAttributesName($key = 'form_attributes')
    {
        $formAttrs = $this->getAttributesName($key);
        if(!is_array($formAttrs) || empty($formAttrs)){
            return (array)$this->getAttributesName();
        }
        return (array)$formAttrs;
    }
    /**
     * @param $key
     * @return array|\Illuminate\Contracts\Translation\Translator|null|string
     */
    public function getAttributesName($key = 'attributes')
    {
        return transm($this->getAlias() . '.' . $key);
    }


    /**
     * @param $key
     * @return array|\Illuminate\Contracts\Translation\Translator|null|string
     */
    public function tA($key)
    {
        return transa($this->getAlias(), $key);
    }

    /**
     * @param $key
     * @return array|\Illuminate\Contracts\Translation\Translator|null|string
     */
    public function getModelName($key = 'name')
    {
        return transm($this->getAlias() . '.' . $key);
    }

    /**
     * @return mixed
     */
    public function getCreatedAtColumn()
    {
        return getCreatedAtColumn();
    }

    /**
     * Get the name of the "updated at" column.
     *
     * @return string
     */
    public function getUpdatedAtColumn()
    {
        if (!$this->_allowFillActionAt()) {
            return null;
        }
        return getUpdatedAtColumn();
    }

    /**
     * @param $key
     * @return bool
     */
    public function hasAttribute($key)
    {
        return array_key_exists($key, $this->getAttributes());
    }

    public function bootIfNotBooted()
    {
        if (!isset(static::$_fillabelFromSchema[static::class])) {
            static::$_fillabelFromSchema[static::class] = $this->_getFillableFromSchema();
        }
        parent::bootIfNotBooted(); // TODO: Change the autogenerated stub
    }

    /**
     * @param array $options
     * @return bool
     */
    public function save(array $options = [])
    {
        // unset field not exist in db
        $attrs = $this->getAttributes();
        $ids = (array)$this->getKeyName();
        $update = true;
        foreach ($ids as $id) {
            $id = $id ? $id : 'id';
            if (!isset($attrs[$id])) {
                $update = false;
                unset($attrs[$id]);
            }
        }
        // check allow blank value
        if ($this->_allowBlankField) {
            foreach ($this->getFillable() as $field) {
                if (isset($attrs[$field]) || in_array($field, $this->getExceptAllowBlankField())) {
                    continue;
                }
                $attrs[$field] = '';
            }
        }

        $attrs[getSystemConfig('updated_at_column.field')] = $this->getKey() != 0 && $this->_allowFillActionAt() ? date('Y-m-d H:i:s') : null;
        $this->setRawAttributes([])->fill($attrs);
        return parent::save($options); // TODO: Change the autogenerated stub
    }

    public function getTableColumnAndTypeList()
    {
        $tableName = $this->getTable();

        $cacheKey = $this->getConnection()->getDatabaseName() . $this->getConnection()->getDriverName() . $tableName;
        if (cache()->has($cacheKey)) {
            $r = cache()->get($cacheKey);
            if (empty($r)) {
                $r = $this->_getTableColumnWithCache();
                cache()->forever($cacheKey, $r);
            }
            return $r;
        }

        $r = $this->_getTableColumnWithCache();
        cache()->forever($cacheKey, $r);
        return $r;
    }

    protected function _getTableColumnWithCache($fullType = false)
    {
        $tableName = $this->getTable();
        $cacheKey = $this->getConnection()->getDatabaseName() . $this->getConnection()->getDriverName() . $tableName;
        return $this->cache($cacheKey, function () use ($tableName, $fullType) {
            $fieldAndTypeList = [];
            switch ($this->getConnection()->getDriverName()) {
                case 'mysql':
                    foreach ($this->getConnection()->select("describe $tableName") as $field) {
                        $type = ($fullType || !str_contains($field->Type, '(')) ? $field->Type : substr($field->Type, 0, strpos($field->Type, '('));
                        $fieldAndTypeList[$field->Field] = $type;
                    }
                    break;
                case 'pgsql':
                    foreach ($this->getConnection()->select(DB::raw("SELECT COLUMN_NAME AS field, data_type AS type FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME =  '{$tableName}' AND table_catalog = '{$this->getConnection()->getDatabaseName()}'")) as $field) {
                        $type = ($fullType || !str_contains($field->type, ' ')) ? $field->type : substr($field->type, 0, strpos($field->type, ' '));
                        $fieldAndTypeList[$field->field] = $type;
                    }
                    break;
                case 'sqlite':
                    //@todo fix for sqlite
                    break;
                case 'sqlsrv':
                    //@todo fix for sqlsrv
                    break;
            }
            return $fieldAndTypeList;
        });
    }

    /**
     * @return array
     */
    public function getFillable()
    {
        if (isset(static::$_fillabelFromSchema[static::class])) {
            return static::$_fillabelFromSchema[static::class];
        }
        return parent::getFillable();
    }

    /**
     * @return array
     */
    protected function _getFillableFromSchema()
    {
        $fields = parent::getFillable();
        if(empty($fields)){
            return array_keys($this->getTableColumnAndTypeList());
        }
        getSystemConfig('updated_at_column.field') ? $fields[] = getSystemConfig('updated_at_column.field') : '';
        return $fields;
    }


    public function removeTrashAttributes()
    {
        $attrs = $this->getAttributes();
        $this->setRawAttributes([])->fill($attrs);
        return $this;
    }

    /**
     * @param array $attributes
     * @return $this
     */
    public function fill(array $attributes)
    {
        $keys = (array)$this->getKeyName();
        foreach ($keys as $key) {
            $key = $key ? $key : 'id';
            isset($attributes[$key]) ? $this->$key = $attributes[$key] : null;
        }
        return parent::fill($attributes); // TODO: Change the autogenerated stub
    }

    /**
     * @param $key
     * @return $this
     */
    public function removeAttribute($key)
    {
        unset($this->attributes[$key]);
        return $this;
    }

    /**
     * @param $data
     * @return $this
     */
    public function mergeAttributes($data)
    {
        $this->attributes = array_merge($this->attributes, $data);
        return $this;
    }

    /**
     * @param $column
     * @return string
     */
    public function getQualifiedColumn($column)
    {
        return $this->getTable() . '.' . $column;
    }

    /**
     * @param $column
     * @return string
     */
    public static function getQuaColumn($column)
    {
        return with(new static)->getQualifiedColumn($column);
    }

    /**
     * @param $key
     * @param int $offset
     * @param array $attr
     * @param bool $resetOffset
     * @return mixed
     */
    public function getRelationOrNew($key, $offset = 0, $attr = [], $resetOffset = false)
    {
        if (!$this->getKey() && empty($this->relationsToArray())) {
            return $this->{$key}()->getRelated()->setRawAttributes($attr);
        }
        try {
            $r = $this->getRelationValue($key);
            if (isCollection($r) && $resetOffset) {
                $r = $r->values();
            }
            $entity = isCollection($r) ? $r->offsetGet($offset) : $r;
            if ($entity) {
                return $entity;
            }
        } catch (\Exception $exception) {

        }
        return $this->{$key}()->getRelated()->setRawAttributes($attr);
    }

    public function removeRelation($key)
    {
        $relations = $this->getRelations();
        unset($relations[$key]);
        $this->setRelations($relations);
        return $this;
    }

    public function removeRelations()
    {
        $this->setRelations([]);
    }

    /**
     * @param $key
     * @param int $offset
     * @param bool $resetOffset
     * @return mixed
     */
    public function tryGet($key, $offset = 0, $resetOffset = false)
    {
        return $this->getRelationOrNew($key, $offset, [], $resetOffset);
    }

    /**
     *
     */
    protected static function boot()
    {
        parent::boot();
        static::deleting(function ($entity) {
            try {
                $action = $entity->isForceDeleting() ? 'forceDelete' : 'delete';
            } catch (\Exception $exception) {
                $action = 'delete';
            }
            static::_destroy($action, $entity);
        });
        static::updating(function ($entity) {
            static::_update($entity);
        });
    }

    protected static function _destroy($action, $entity, $parentIds = [], $isFirst = true)
    {
        $destroyRelations = $entity::getDestroyRelations();
        if (empty($destroyRelations)) {
            if ($isFirst || empty($parentIds)) {
                return true;
            }
        }
        if ($isFirst) {
            $parentIds = $entity->getKeyWithName();
        }
        // get all child ids
        foreach ($destroyRelations as $relation) {
            $related = $entity->$relation()->getRelated();
            $relatedDestroyRelations = $related::getDestroyRelations();
            if (empty($relatedDestroyRelations)) {
                if (empty($parentIds)) {
                    continue;
                }
                static::_destroyByParentKey($action, $entity, $relation, $parentIds);
                continue;
            }
            $reParentIds = static::_getParentIds($entity, $relation, $parentIds, $relatedDestroyRelations);
            if (empty($reParentIds)) {
                continue;
            }
            static::_destroy($action, $related, $reParentIds, false);
        }
        if ($isFirst || empty($parentIds)) {
            return true;
        }
        return $entity->withKeysIn($parentIds)->$action();
    }

    protected static function _destroyByParentKey($action, $parent, $relation, $parentKeys)
    {
        $parentKeysName = static::_getParentKeyName($parent, $relation);
        $fKeysName = static::_getForeignKeyName($parent, $relation);
        $fKeysValue = static::_mapForeignKeyValue($parentKeysName, $parentKeys, $fKeysName);
        $parent->$relation()->getRelated()->withParentKeysIn($fKeysValue)->$action();
    }

    protected static function _updateByParentKey($parent, $relation, $parentKeys)
    {
        $parentKeysName = static::_getParentKeyName($parent, $relation);
        $fKeysName = static::_getForeignKeyName($parent, $relation);
        $fKeysValue = static::_mapForeignKeyValue($parentKeysName, $parentKeys, $fKeysName);
        $parent->$relation()->getRelated()->withParentKeysIn($fKeysValue)->update([getSystemConfig('updated_at_column.field') => static::$_updateTime]);
    }

    protected static function _getParentKeyName($entity, $relation)
    {
        $parent = $entity->$relation()->getParent();
        $parentKeysName = (array)$entity->$relation()->getQualifiedParentKeyName();
        $parentTableName = $parent->getTableName();
        foreach ($parentKeysName as &$key) {
            $key = str_replace($parentTableName . '.', '', $key);
        }
        return $parentKeysName;
    }

    protected static function _getForeignKeyName($entity, $relation)
    {
        $related = $entity->$relation()->getRelated();
        $relatedTableName = $related->getTableName();
        $fKeysName = (array)$entity->$relation()->getForeignKeyName();
        foreach ($fKeysName as &$fKey) {
            $fKey = str_replace($relatedTableName . '.', '', $fKey);
        }
        return $fKeysName;
    }

    protected static function _getSelectFieldForRelation($entity, $otherKeys = [], $relations)
    {
        if (empty($relations)) {
            return [];
        }
        $parentKeysName = array_merge_recursive($otherKeys, (array)$entity->getKeyName());
        foreach ($relations as $relation) {
            $parentKeysName = array_merge_recursive($parentKeysName, (array)$entity->$relation()->getQualifiedParentKeyName());
        }

        $parentKeysName = array_unique($parentKeysName);
        $parentTableName = $entity->getTableName();
        foreach ($parentKeysName as &$key) {
            $key = str_replace($parentTableName . '.', '', $key);
        }
        return array_unique($parentKeysName);
    }

    protected static function _getParentIds($entity, $relation, $keys, $relations)
    {
        $query = null;
        $r = [];
        $related = $entity->$relation()->getRelated();
        $parentKeysName = static::_getParentKeyName($entity, $relation);
        $fKeysName = static::_getForeignKeyName($entity, $relation);
        $fKeysValue = static::_mapForeignKeyValue($parentKeysName, $keys, $fKeysName);

        foreach ($fKeysValue as $fKey => $value) {
            if ($query) {
                $query = $query->whereIn($fKey, (array)$value);
            } else {
                $query = $related->whereIn($fKey, (array)$value);
            }
        }

        $selectField = static::_getSelectFieldForRelation($related, (array)$parentKeysName, $relations);
        $query->select($selectField)->get()->map(function ($x) use (&$r) {
            $r = array_merge_recursive($r, (array)$x->getAttributes());
        });
        return $r;
    }

    protected static function _mapForeignKeyValue($parentKeysName, $parentKeysValue, $foreignKeyName)
    {
        $fKeysValue = [];
        foreach ($foreignKeyName as $idx => $fKey) {
            $fKeysValue[$fKey] = Arr::get($parentKeysValue, $parentKeysName[$idx]);
        }
        return $fKeysValue;
    }

    protected static function _update($entity, $parentIds = [], $isFirst = true)
    {
        if (!static::$_updateTime) {
            static::$_updateTime = Carbon::create();
        }
        $destroyRelations = $entity::getUpdateRelations();
        if (empty($destroyRelations)) {
            if ($isFirst || empty($parentIds)) {
                return true;
            }
        }
        if ($isFirst) {
            $parentIds = $entity->getKeyWithName();
        }
        // get all child ids
        foreach ($destroyRelations as $relation) {
            $related = $entity->$relation()->getRelated();
            $relatedDestroyRelations = $related::getUpdateRelations();
            if (empty($relatedDestroyRelations)) {
                if (empty($parentIds)) {
                    continue;
                }
                static::_updateByParentKey($entity, $relation, $parentIds);
                continue;
            }
            $reParentIds = static::_getParentIds($entity, $relation, $parentIds, $relatedDestroyRelations);
            if (empty($reParentIds)) {
                continue;
            }
            static::_update($related, $reParentIds, false);
        }
        //
        if ($isFirst || empty($parentIds)) {
            return true;
        }
        return $entity->withKeysIn($parentIds)->update([getSystemConfig('updated_at_column.field') => static::$_updateTime]);
    }

    public function getNextInsertId()
    {
        $entity = $this;
        if ($entity->getKey()) {
            return $entity->getKey();
        }
        $nextId = 1;
        $table = $entity->getTable();
        switch ($this->getConnection()->getDriverName()) {
            case 'mysql':
                $statement = $this->getConnection()->select("SHOW TABLE STATUS LIKE '{$table}'");
                $nextId = $statement[0]->Auto_increment;
                break;
            case 'pgsql':
                $statement = $this->getConnection()->select("SELECT nextval('{$this->getSequence()}')");
                $nextId = $statement[0]->nextval;
                break;
            case 'sqlite':
                //@todo fix for sqlite
                break;
            case 'sqlsrv':
                //@todo fix for sqlsrv
                break;
        }
        return $nextId;
    }


    protected function fireModelEvent($event, $halt = true)
    {
        if (!isset(static::$dispatcher)) {
            return true;
        }

        // First, we will get the proper method to call on the event dispatcher, and then we
        // will attempt to fire a custom, object based event for the given event. If that
        // returns a result we can return that result, or we'll call the string events.
        $method = $halt ? 'until' : 'dispatch';

        $result = $this->filterModelEventResults(
            $this->fireCustomModelEvent($event, $method)
        );

        if ($result === false) {
            return false;
        }

        return !empty($result) ? $result : static::$dispatcher->{$method}(
            getConstant('EVENT_MODEL_TYPE') . ".{$event}: " . static::class, $this
        );
    }

    /**
     * Register a model event with the dispatcher.
     *
     * @param  string $event
     * @param  \Closure|string $callback
     * @return void
     */
    protected static function registerModelEvent($event, $callback)
    {
        if (isset(static::$dispatcher)) {
            $name = static::class;

            static::$dispatcher->listen(getConstant('EVENT_MODEL_TYPE') . ".{$event}: {$name}", $callback);
        }
    }

    public static function callRaw($sProcedure, $aParams = [], $isExecute = false)
    {
        // create database connection
        $db = DB::connection()->getPdo();

        // if any params are present, add them
        $sParamsIn = '';
        if (isset($aParams) && is_array($aParams) && count($aParams) > 0) {
            // loop through params and set
            foreach ($aParams as $sParam) {
                $sParamsIn .= '?,';
            }

            // trim the last comma from the params in string
            $sParamsIn = substr($sParamsIn, 0, strlen($sParamsIn) - 1);
        }

        // create initial stored procedure call
        $stmt = $db->prepare("CALL $sProcedure($sParamsIn)");

        // if any params are present, add them
        if (isset($aParams) && is_array($aParams) && count($aParams) > 0) {
            $iParamCount = 1;

            // loop through params and bind value to the prepare statement
            foreach ($aParams as &$value) {
                $stmt->bindParam($iParamCount, $value);
                $iParamCount++;
            }
        }

        try {

            // execute the stored procedure
            $stmt->execute();
            if ($isExecute) {
                return true;
            }
        } catch (\Exception $exception) {
            logError($exception->getMessage());
            if ($isExecute) {
                return false;
            }
            return [];
        }

        do {
            try {
                $results[] = $stmt->fetchAll(\PDO::FETCH_OBJ);
            } catch (\Exception $exception) {
                logError($exception->getMessage());
            }
        } while ($stmt->nextRowset());


        // if the resultset has only 1 record, check the name of the stored procedure
        // if the name of the procedure has sel_rec within it, just return the one record
        if (count($results) == 1 && strpos($sProcedure, 'sel_rec')) {
            $results = $results[0];
        }

        // return the data
        return $results;
    }

    public function getAttribute($field)
    {
        $r = parent::getAttribute($field);
        if ($r && $this->hasUrlAttribute($field)) {
            return $this->getFileUrl($r);
        }
        return $r;
    }

    public function setAttribute($field, $value)
    {
        if ($value && $this->hasUrlAttribute($field)) {
            $value = str_replace($this->getFileUrl(''), '', $value);
        }
        return parent::setAttribute($field, $value);
    }


    /**
     * @return array
     */
    public function getUrlAttributes()
    {
        return $this->urlAttributes;
    }

    /**
     * @param array $urlAttributes
     */
    public function setUrlAttributes($urlAttributes)
    {
        $this->urlAttributes = $urlAttributes;
    }

    public function hasUrlAttribute($field)
    {
        return in_array($field, $this->getUrlAttributes());
    }

    // override base
    protected function getArrayableItems(array $values)
    {
        if (count($this->getUrlAttributes()) <= 0) {
            return parent::getArrayableItems($values);
        }

        foreach ($this->getUrlAttributes() as $attribute) {
            if (!array_key_exists($attribute, $values)) {
                continue;
            }
            $values[$attribute] = $values[$attribute] ? $this->getFileUrl($values[$attribute]) : $values[$attribute];
        }
        return parent::getArrayableItems($values);
    }

    /**
     * Set the keys for a save update query.
     *
     * @param  \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function setKeysForSaveQuery($query)
    {
        $keys = $this->getKeyName();
        if (!is_array($keys)) {
            return parent::setKeysForSaveQuery($query);
        }

        foreach ($keys as $keyName) {
            $query->where($keyName, '=', $this->getKeyForSaveQuery($keyName));
        }

        return $query;
    }

    /**
     * Get the primary key value for a save query.
     *
     * @param mixed $keyName
     * @return mixed
     */
    protected function getKeyForSaveQuery($keyName = null)
    {
        if (is_null($keyName)) {
            $keyName = $this->getKeyName();
        }

        if (isset($this->original[$keyName])) {
            return $this->original[$keyName];
        }

        return $this->getAttribute($keyName);
    }


    /**
     * Get the value of the model's primary key.
     *
     * @return mixed
     */
    public function getKey($key = '')
    {
        if ($key === true) {
            return $this->getAttribute($this->getKeyName(true));
        }
        if ($key) {
            return $this->getAttribute($key);
        }
        $keys = $this->getKeyName();
        if (!is_array($keys)) {
            return parent::getKey();
        }
        $r = [];
        foreach ($keys as $keyName) {
            $r[$keyName] = $this->getAttribute($keyName);
        }
        return $r;
    }

    public function isEmptyKey()
    {
        $key = $this->getKey();
        if (is_array($key)) {
            return empty(array_filter_null($key));
        }
        return is_null($key) || $key === '';
    }

    public function getKeyAsString()
    {
        $keys = (array)$this->getKey();
        return implode('k_k', $keys);
    }

    public function getKeyNameAsString()
    {
        $keys = (array)$this->getKeyName();
        return implode('k_k', $keys);
    }

    public function setOriginKeyFromString($value)
    {
        $value = explode('k_k', $value);
        $keys = (array)$this->getKeyName();
        foreach ($keys as $idx => $key) {
            array_set($this->original, $key, Arr::get($value, $idx));
        }
        return $this;
    }


    public function getKeyFromString($value)
    {
        $r = [];
        $value = explode('k_k', $value);
        $keys = (array)$this->getKeyName();
        foreach ($keys as $idx => $key) {
            array_set($r, $key, Arr::get($value, $idx));
        }
        return $r;
    }

    /**
     * Get the value of the model's primary key.
     *
     * @return mixed
     */
    public function getKeyWithName($key = '')
    {
        if ($key) {
            return [$key => $this->getAttribute($key)];
        }
        $keys = (array)$this->getKeyName();
        $r = [];
        foreach ($keys as $keyName) {
            $r[$keyName] = $this->getAttribute($keyName);
        }
        return $r;
    }

    public function getKeyName($getFirst = false)
    {
        $keys = parent::getKeyName(); // TODO: Change the autogenerated stub
        if (!$getFirst) {
            return $keys;
        }
        return Arr::get((array)$keys, 0);
    }

    public function getForeignKey()
    {
        if (!is_array($this->primaryKey)) {
            return parent::getForeignKey();
        }
        $keys = [];
        foreach ($this->primaryKey as $key) {
            $keys[$key] = Str::snake(class_basename($this)) . '_' . $key;
        }
        return $keys;
    }

    public function getAllGlobalScopes()
    {
        return static::$globalScopes;
    }

    /**
     * Get the global scopes for this class instance.
     *
     * @return array
     */
    public static function getAllGlobalScope()
    {
        return static::$globalScopes;
    }

    /**
     * Create a new Eloquent query builder for the model.
     *
     * @param  \Illuminate\Database\Query\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder|static
     */
    public function newEloquentBuilder($query)
    {
        return new Builder($query);
    }

    /**
     * Create a new Eloquent query builder for the model.
     *
     * @return \Illuminate\Database\Eloquent\Builder|static
     */
    public function newBaseQueryBuilder()
    {
        $connection = $this->getConnection();

        return new CustomQueryBuilder($connection, $connection->getQueryGrammar(), $connection->getPostProcessor());
    }

    /**
     * Cast an attribute to a native PHP type.
     *
     * @param  string  $key
     * @param  mixed  $value
     * @return mixed
     */
    protected function castAttribute($key, $value)
    {
        if (is_null($value)) {
            return $value;
        }
        // for pgsql
        switch ($this->getCastType($key)) {
            case 'bigint':
                return (int) $value;
            case 'character':
                return (string) $value;
            default:
                return parent::castAttribute($key, $value);
        }
    }


    /**
     * Insert the given attributes and set the ID on the model.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  array  $attributes
     * @return void
     */
    protected function insertAndSetId(\Illuminate\Database\Eloquent\Builder $query, $attributes)
    {
        if(empty($attributes[$this->getPrimaryKeyAutoIncrement()])){
            $attributes[$this->getPrimaryKeyAutoIncrement()] = $this->getNextInsertId();
        }
        return parent::insertAndSetId($query, $attributes);
    }

    public function setUpdDateAttribute()
    {
        if (empty($this->getKey())) {
            $this->attributes['upd_date'] = getConstant('DEFAULT_UPD_DATE');
        } else {
            $this->attributes['upd_date'] = Carbon::now();
        }
    }
}
