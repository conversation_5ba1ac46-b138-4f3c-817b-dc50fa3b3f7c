[2025-08-15T13:01:12.376386+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:01:12.381372+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:01:14.568084+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:01:14.569429+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:02:16.154797+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:02:16.155831+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:03:05.835347+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:03:05.836618+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:03:47.218325+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:03:47.219625+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:18:33.228496+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:18:33.229725+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:18:35.110001+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:18:35.111422+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:18:42.354473+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:18:42.355763+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:18:45.062152+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:18:45.063356+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:18:49.841686+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:18:49.843376+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:19:31.634999+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/images/ui-icons_444444_256x240.png|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:19:31.636163+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:40:52.746541+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:40:52.747935+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:40:54.919605+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:40:54.920824+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T13:41:00.100220+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T13:41:00.101101+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T15:37:02.717800+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T15:37:02.720875+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T15:37:06.894047+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T15:37:06.895211+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T15:37:07.918704+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T15:37:07.920056+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T15:37:11.147181+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/images/ui-icons_444444_256x240.png|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T15:37:11.148117+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T15:37:49.859121+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T15:37:49.860988+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T15:37:52.872524+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/images/ui-icons_444444_256x240.png|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T15:37:52.874109+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T15:39:18.561455+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T15:39:18.562456+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T15:57:19.491193+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/images/ui-icons_444444_256x240.png|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T15:57:19.492531+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T15:58:23.392700+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/images/ui-icons_444444_256x240.png|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T15:58:23.394312+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T15:58:42.882345+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T15:58:42.883146+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:00:11.895393+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:00:11.896722+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:01:17.584380+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:01:17.585293+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:02:51.260593+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:02:51.262147+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:04:24.044697+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:04:24.045585+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:08:14.001476+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:08:14.004417+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:10:55.147164+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:10:55.148412+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:11:22.773445+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/images/ui-icons_444444_256x240.png|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:11:22.774747+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:11:55.529406+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:11:55.530348+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:17:47.758480+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:17:47.759741+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:18:01.201731+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:18:01.202942+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:20:28.795272+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:20:28.796670+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:22:37.280467+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/images/ui-icons_444444_256x240.png|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:22:37.281612+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:40:17.750495+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:40:17.751946+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
[2025-08-15T16:40:19.407491+09:00] error.ERROR: IP:127.0.0.1|Url://css/backend/vendor/bootstrap-datepicker.min.css.map|Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  
[2025-08-15T16:40:19.408743+09:00] error.ERROR: Symfony\Component\HttpKernel\Exception\NotFoundHttpException in C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\AbstractRouteCollection.php:43
Stack trace:
#0 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\RouteCollection.php(162): Illuminate\Routing\AbstractRouteCollection->handleMatchedRoute(Object(Illuminate\Http\Request), NULL)
#1 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(673): Illuminate\Routing\RouteCollection->match(Object(Illuminate\Http\Request))
#2 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->findRoute(Object(Illuminate\Http\Request))
#3 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#4 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#5 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#6 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#7 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#8 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#9 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\AutoSetLang.php(12): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#10 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AutoSetLang->handle(Object(Illuminate\Http\Request), Object(Closure))
#11 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#12 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\CustomTrimStrings.php(12): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure), Array)
#17 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\CustomTrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\xampp\htdocs\reserve_one_system\app\Http\Middleware\ForceSSL.php(28): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\ForceSSL->handle(Object(Illuminate\Http\Request), Object(Closure))
#24 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#25 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#26 C:\xampp\htdocs\reserve_one_system\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#27 C:\xampp\htdocs\reserve_one_system\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#28 {main}  
