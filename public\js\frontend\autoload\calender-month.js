var jsonDate = [
    {
        "day": "2022-03-21",
        "count": 3,
        "tel": true
    },
    {
        "day": "2022-03-22",
        "count": 5,
        "tel": false
    },
    {
        "day": "2022-03-23",
        "count": 10,
        "tel": true
    },
    {
        "day": "2022-03-24",
        "count": 22,
        "tel": false
    },
    {
        "day": "2022-03-25",
        "count": 16,
        "tel": true
    },
    {
        "day": "2022-03-26",
        "count": 0,
        "tel": false
    },
    {
        "day": "2022-03-27",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-03-28",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-03-29",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-03-30",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-03-31",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-01",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-02",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-03",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-04",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-05",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-06",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-07",
        "count": 10,
        "tel": false
    },
    {
        "day": "2022-04-08",
        "count": 3,
        "tel": false
    },
    {
        "day": "2022-04-09",
        "count": 1,
        "tel": false
    },
    {
        "day": "2022-04-10",
        "count": 2,
        "tel": false
    },
];

// カレンダーのセルにアイコンを表示するためのclass命名
function setCellClassName(count, tel) {
    var className = 'reserve-status-none';
    if (tel) {
        className = 'reserve-status-tel';
    } else {
        if (count > 4) {
            className = 'reserve-status-1';
        } else if(count > 2) {
            className = 'reserve-status-2';
        } else if(count > 0) {
            className = 'reserve-status-3';
        }
    }
    return className;
}

function submitFnc(date){
    var form = document.createElement('form');
    form.action = 'calender-week.html';
    form.method = 'GET';

    document.body.append(form);

    form.addEventListener('formdata', (e) => {
        var fd = e.formData;

        fd.set('date', date);
    });

    form.submit();
}

/* calender
* --------------------------------------------------------- */
var calendar = document.getElementById('calendar');
if (calendar != null) {
    var calendar = new FullCalendar.Calendar(calendar, {
        initialView: 'dayGridMonth',
        locale: 'ja',
        height: 470,
        firstDay: 1,
        headerToolbar: {
            left: 'prev',
            center: 'title',
            right: 'next'
        },
        titleFormat: {
            year: 'numeric'
        },
        buttonText: {
            prev: '前の月',
            next: '次の月',
        },
        dayMaxEvents: true,
        dayCellClassNames: function(info) {
            var cell_date = new Date(info.date);
            var format_date = cell_date.getFullYear()+'-'+("0" + (cell_date.getMonth()+1)).slice(-2)+'-'+("0" + cell_date.getDate()).slice(-2);
            var indexDate = jsonDate.filter(function(item, index){
                if (item.day == format_date) return item;
            });
            if (indexDate.length > 0) {
                return [ setCellClassName(indexDate[0].count, indexDate[0].tel) ];
            } else {
                return [ 'reserve-status-none' ]
            }
        },
        dayCellContent: function (e) {
            var cell_month = e.date.getMonth() + 1;
            var cell_day = e.date.getDate();
            e.dayNumberText = cell_month+'/'+cell_day;
        },
        dateClick: function (info) {
            if ($(info.dayEl).hasClass('fc-day-past')) return;
            if ($(info.dayEl).hasClass('reserve-status-tel')) {
                alert('お電話にてご確認ください。'); return;
            }
            var click_date = info.date;
            var format_date = click_date.getFullYear()+'-'+("0" + (click_date.getMonth()+1)).slice(-2)+'-'+("0" + click_date.getDate()).slice(-2);

            var form = $('#calender-form');
            submitFnc(format_date);
        },
        fixedWeekCount: false,
        datesSet: function (dateInfo) {
            var today = new Date();
            today.setHours(0);
            today.setMinutes(0);
            today.setSeconds(0);
            today.setMilliseconds(0);
            var minDate = today.setDate(1), //今の月より前の月は表示しない
                maxDate = today.setMonth(today.getMonth() + 2), //最大で表示する未来の月を設定
                maxDate = today.setDate(0);
            var viewDate = calendar.view.currentStart;
            var minDate = new Date(minDate),
                maxDate = new Date(maxDate);
            // Past
            if (minDate >= viewDate) {
                $(".fc-prev-button").prop('disabled', true);
                $(".fc-prev-button").addClass('fc-state-disabled');
            }
            else {
                $(".fc-prev-button").removeClass('fc-state-disabled');
                $(".fc-prev-button").prop('disabled', false);
            }
            // Future
            if (maxDate <= viewDate) {
                $(".fc-next-button").prop('disabled', true);
                $(".fc-next-button").addClass('fc-state-disabled');
            } else {
                $(".fc-next-button").removeClass('fc-state-disabled');
                $(".fc-next-button").prop('disabled', false);
            }
        }
    });
    calendar.render();
}
