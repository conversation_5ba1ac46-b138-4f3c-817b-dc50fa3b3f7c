let Shop = {};
Shop.showModalDelete = function (e) {
    var href = $(e).attr('data-action');
    $("#modal_shop_delete").find('#del_form').attr('action', href);

    $("#modal_shop_delete").modal('show');
};

Shop.paginate = function (e) {
    var currentPage = $(e).attr('url');
    var lastPage = Number($(e).attr('max'));
    var page = $(e).val();
    $("#url-page").attr('href', href);
    if (page < 1 || page > lastPage) {
        page = 1;
    }
    var href = currentPage + "?page=" + page;
    window.location.href = href;
};

$(document).on('change', '.form-disabled-control-field .checkbox_shop', function (e) {
    let target = $(this).closest('.form-disabled-control-field').find('select');
    if ($(this).is(':checked')) {
        $(target).attr('disabled', true);
        $(target).val('00:00');
    } else {
        $(target).attr('disabled', false);
        $(target).val('');
    }
});

$(document).ready(function () {
    $('.form-disabled-control-field').each(function () {
        let input = $(this).find('.checkbox_shop');
        let checked = $(input).attr('checked');
        let target = $(this).closest('.form-disabled-control-field').find('select');
        if ($(input).is(':checked')) {
            $(target).attr('disabled', true);
            $(target).val('00:00');
        } else {
            $(target).attr('disabled', false);
        }
    });

    let error = $('.fore-show-error');
    error.closest('.form-field').find('input[type="password"]').addClass('class-error');
    error.closest('.form-field').find('input[type="text"]:not(input[name="publish_to_dt_date"])').addClass('class-error');
    error.closest('.form-field').find('select:not(select[name="publish_to_dt_time"])').addClass('class-error');
    if ($('select[name="publish_to_dt_time"]').length > 0 && $('input[name="publish_to_dt_date_submit"]').length > 0) {
        if ($('select[name="publish_to_dt_time"]').val().trim() || $('input[name="publish_to_dt_date_submit"]').val().trim()) {
            error.closest('.form-field').find('select[name="publish_to_dt_time"]').addClass('class-error');
            error.closest('.form-field').find('input[name="publish_to_dt_date"]').addClass('class-error');
        }
    }
    if (error.length > 0) {
        $('select').css('border-radius', '6px')
    }
})

//when submit changing-password button
$('#submit-change').click(function () {

    //create hidden form
    var form = document.createElement('form');
    form.action = $('#change_password').val()
    form.method = 'post'

    //create hidden input
    var token = document.createElement('input')
    token.type = 'hidden'
    token.name = '_token'
    token.value = $("input[name='_token']").val()

    var current = document.createElement('input')
    current.type = 'hidden'
    current.name = 'shop_user_pwd_current'
    current.value = $('#shop_user_pwd_current').val()

    var pwd = document.createElement('input')
    pwd.type = 'hidden'
    pwd.name = 'shop_user_pwd'
    pwd.value = $('#shop_user_pwd').val()

    var confirm = document.createElement('input')
    confirm.type = 'hidden'
    confirm.name = 'shop_user_pwd_confirm'
    confirm.value = $('#shop_user_pwd_confirm').val()

    document.body.append(form);
    form.appendChild(token)
    form.appendChild(current)
    form.appendChild(pwd)
    form.appendChild(confirm)

    form.submit();
})
