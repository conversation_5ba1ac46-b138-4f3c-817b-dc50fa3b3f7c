<div class="flex align-items-center">
    <nav class="flex align-items-center ml-auto page-controls">
        <ul class="pagination">
            <li class="page-item"><a
                    class="page-link page-prev {{ ($entities->currentPage() == 1) ? 'disabled' : '' }}"
                    href="{{$entities->url($entities->currentPage()-1)}}">前へ</a></li>
            <li class="page-item"><span class="current-page">{{$entities->currentPage()}}</span></li>
            <li class="page-item"><span class="total-page">/</span></li>
            <li class="page-item"><span class="total-page">{{$entities->lastPage()}}</span></li>
            <li class="page-item"><a
                    class="page-link page-next {{ ($entities->currentPage() == $entities->lastPage()) ? ' disabled' : '' }}"
                    href="{{$entities->url($entities->currentPage()+1)}}">次へ</a></li>
        </ul>
    </nav>
</div>
<style>
    .disabled {
        pointer-events: none;
        cursor: default;
    }
</style>
