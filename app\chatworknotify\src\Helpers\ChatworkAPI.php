<?php

namespace App\chatworknotify\src\Helpers;

/**
 * ====================================================================
 *
 * Chatwork API Driver Core class
 *
 * Implement some API call interface methods.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * ====================================================================
 */

class ChatworkAPI {

    /**
     * API Request endpoints
     */
    const REQUEST_BASE   = 'https://api.chatwork.com/v2';
    const ME_PATH        = '/me';
    const MY_STATUS_PATH = '/my/status';
    const MY_TASKS_PATH  = '/my/tasks';
    const CONTACTS_PATH  = '/contacts';
    const ROOMS_PATH     = '/rooms';

    /**
     * API parameters endpoints format
     */
    const ROOM_DETAIL         = '/rooms/%d';
    const ROOM_MEMBERS        = '/rooms/%d/members';
    const ROOM_MESSAGES       = '/rooms/%d/messages';
    const ROOM_MESSAGE_DETAIL = '/rooms/%d/messages/%d';
    const ROOM_TASKS          = '/rooms/%d/tasks';
    const ROOM_TASK_DETAIL    = '/rooms/%d/tasks/%d';
    const ROOM_FILES          = '/rooms/%d/files';
    const ROOM_FILE_DETAIL    = '/rooms/%d/files/%d';

    protected $apiKey;
    // ---------------------------------------------------------------

    /**
     * Create request parameters syntax
     * @param array
     * @return ChatworkParams
     */
    public function createParams($args)
    {
        return new ChatworkParams($args);
    }

    // ---------------------------------------------------------------

    /**
     * Constructor
     * @param string $apiKey
     */
    public function __construct()
    {
        $this->apiKey    = config('chatwork.api_key');

        $this->connection = new ChatworkConnector($this->apiKey);
    }

    // ---------------------------------------------------------------

    /**
     * Get my information data
     *
     * @return object
     * @throws \Exception
     */
    public function getInfo()
    {
        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . self::ME_PATH
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get my status
     * @return object
     */
    public function getStatus()
    {
        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . self::MY_STATUS_PATH
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get my tasks
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function getTasks($params = null)
    {
        if ( ! ($params instanceof ChatworkParams) )
        {
            $params = new ChatworkParams();
        }
        if ( TRUE !== ($valid = $params->isValidMyTaskRequest()) )
        {
            throw new ChatworkException($valid);
        }

        $suffix = ( $params->toURIParams() !== '' ) ? '?' . $params->toURIParams() : '';
        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . self::MY_TASKS_PATH . $suffix
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get my contact list
     * @return object
     */
    public function getContacts()
    {
        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . self::CONTACTS_PATH
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get my chatrooms
     * @return object
     */
    public function getRooms()
    {
        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . self::ROOMS_PATH
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get room detail info
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function getRoomDetail(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidRoomID()) )
        {
            throw new \Exception($valid);
        }

        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . sprintf(self::ROOM_DETAIL, $params->room_id)
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Create new chatroom
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function createRoom(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidCreateRoomRequest()) )
        {
            throw new \Exception($valid);
        }

        $response = $this->connection->request(
            'POST',
            self::REQUEST_BASE . self::ROOMS_PATH,
            array(),
            $params->toURIParams()
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Upadte room info
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function updateRoom(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidUpdateRoomRequest()) )
        {
            throw new \Exception($valid);
        }

        $response = $this->connection->request(
            'PUT',
            self::REQUEST_BASE . sprintf(self::ROOM_DETAIL, $params->room_id),
            array(),
            $params->toURIParams(array('room_id'))
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Leave the room
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function leaveRoom(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidRoomID()) )
        {
            throw new \Exception($valid);
        }

        $params->action_type = 'leave';

        $response = $this->connection->request(
            'DELETE',
            self::REQUEST_BASE . sprintf(self::ROOM_DETAIL, $params->room_id),
            array(),
            $params->toURIParams()
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Delete room
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function deleteRoom(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidRoomID()) )
        {
            throw new \Exception($valid);
        }

        $params->action_type = 'delete';

        $response = $this->connection->request(
            'DELETE',
            self::REQUEST_BASE . sprintf(self::ROOM_DETAIL, $params->room_id),
            array(),
            $params->toURIParams()
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get room members
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function getRoomMembers(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidRoomID()) )
        {
            throw new \Exception($valid);
        }

        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . sprintf(self::ROOM_MEMBERS, $params->room_id)
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Update room members
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function updateRoomMembers(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidUpdateRoomMembers()) )
        {
            throw new \Exception($valid);
        }

        $response = $this->connection->request(
            'PUT',
            self::REQUEST_BASE . sprintf(self::ROOM_MEMBERS, $params->room_id),
            array(),
            $params->toURIParams(array('room_id'))
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get room message posts
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     * @TODO implement
     */
    public function getRoomMessages(ChatworkParams $params)
    {
        // not implemented at 2013/12/03
        throw new \Exception('Sorry, this API has not implemented.');
        /*
        if ( TRUE !== ($valid = $params->isValidRoomID()) )
        {
            throw new ChatworkException($valid);
        }

        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . sprintf(self::ROOM_MESSAGES, $params->room_id)
        );

        return $this->makeResponse($response);
        */
    }

    // ---------------------------------------------------------------

    /**
     * Post mesage to room
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function postRoomMessage(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidPostRoomMessage()) )
        {
            throw new \Exception($valid);
        }

        $response = $this->connection->request(
            'POST',
            self::REQUEST_BASE . sprintf(self::ROOM_MESSAGES, $params->room_id),
            array(),
            $params->toURIParams(array('room_id'))
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get room message detail
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function getRoomMessageDetail(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidGetRoomMessage()) )
        {
            throw new \Exception($valid);
        }

        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . sprintf(self::ROOM_MESSAGE_DETAIL, $params->room_id, $params->message_id)
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get room task list
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function getRoomTasks(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidRoomID()) )
        {
            throw new \Exception($valid);
        }

        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . sprintf(self::ROOM_TASKS, $params->room_id)
                               . '?' . $params->toURIParams(array('room_id'))
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Add room task
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function addRoomTask(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidAddRoomTask()) )
        {
            throw new \Exception($valid);
        }

        $response = $this->connection->request(
            'POST',
            self::REQUEST_BASE . sprintf(self::ROOM_TASKS, $params->room_id),
            array(),
            $params->toURIParams(array('room_id'))
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get room task detail
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function getRoomTaskDetail(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidRoomID())
            || TRUE !== ($valid = $params->isValidTaskID()) )
        {
            throw new \Exception($valid);
        }

        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . sprintf(self::ROOM_TASK_DETAIL, $params->room_id, $params->task_id)
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get room uploaded files info
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function getRoomFiles(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidGetRoomFiles()) )
        {
            throw new \Exception($valid);
        }

        $suffix = ( $params->toURIParams() !== '' ) ? '?' . $params->toURIParams() : '';
        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . sprintf(self::ROOM_FILES, $params->room_id) . $suffix
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Get room uploaded file detail
     * @param  ChatworkParams $params
     * @return object
     * @throws \Exception
     */
    public function getRoomFileDetail(ChatworkParams $params)
    {
        if ( TRUE !== ($valid = $params->isValidGetRoomFileDetail()) )
        {
            throw new \Exception($valid);
        }

        $response = $this->connection->request(
            'GET',
            self::REQUEST_BASE . sprintf(self::ROOM_FILES, $params->room_id)
        );

        return $this->makeResponse($response);
    }

    // ---------------------------------------------------------------

    /**
     * Make/format API response
     * @access protected
     * @param  object $response
     * @return object
     * @throws \Exception
     */
    protected function makeResponse($response)
    {
        $body = json_decode($response->body);

        if ( preg_match('/^2[0-9]{2}$/', (string)$response->status) )
        {
            return $body;
        }
        else
        {
           throw new \Exception($body->errors[0]);
        }
    }
}
