<?php

namespace App\chatworknotify\src\Exceptions;

use App\chatworknotify\src\Chatwork;
use Exception;
use Illuminate\Support\Facades\App;

class Handler extends \App\Exceptions\Handler
{

    /**
     * @param Exception $exception
     * @return mixed|void
     * @throws \Throwable
     */
    public function report(\Throwable $exception)
    {
        if ($this->shouldntReport($exception)) {
            return;
        }
        try {
            if (App::environment( 'production', 'staging', 'development')) {

                (new Chatwork())->writeException(request(), $exception);
            }
        } catch (\Throwable $ex) {
            throw $exception; // throw the original exception
        }

         return parent::report($exception);
    }
}
