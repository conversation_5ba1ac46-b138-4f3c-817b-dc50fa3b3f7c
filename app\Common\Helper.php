<?php
function buildDashBoardUrl()
{
    return route('shop.index', buildDashBoardParamsDefault());
}

function buildDashBoardParamsDefault()
{
    return [];
}

function buildShopInfoUrl()
{
    return route('shop.edit', buildShopInfoParamsDefault());
}

function buildShopInfoParamsDefault()
{
    return ['shop' => shopGuard()->user()->id];
}

function buildListShopUrl()
{
    return route('shop.index', buildDashBoardParamsDefault());
}

function buildAccountUrl()
{
    return route('account.index', buildAccountParamsDefault());
}

function buildAccountParamsDefault()
{
    return [];
}

function checkActiveMenu($listController = [])
{
    $currentController = getCurrentControllerName();
    if (empty($currentController) || !is_array($listController) || !in_array($currentController, $listController)) {
        return '';
    }
    return 'active';
}

function checkActiveMailSetting($code)
{
    if (empty($code) || $code != request('settingCode')) {
        return '';
    }

    return 'active-tab';
}

function checkActiveOptionPlan($controllerName) {
    if (empty($controllerName) || !is_string($controllerName) || strtolower($controllerName) != strtolower(getCurrentControllerName())) {
        return '';
    }

    return 'active-tab';
}

function getPageTitle()
{
    $controller = getCurrentControllerName();
    $action = getCurrentAction();

    return transb("{$controller}.{$action}") . '画面';
}

function convertTime($time)
{
    if (empty($time)) {
        return '';
    }

    return \Carbon\Carbon::parse($time)->format('H:i');
}

function shortenString($str)
{
    $length = mb_strlen($str);
    if ($length > 20) {
        $str = mb_substr($str, 0, 20) . '...';
    }
    return $str;
}
