<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Base\BackendController;
use App\Repositories\ReserveDenialsRepository;
use App\Repositories\ReserveRepository;
use App\Repositories\ReserveTelRepository;
use App\Repositories\ShopInfoRepository;
use App\Services\Backend\CalendarService;
use Illuminate\Http\Request;


/**
 * Class CalendarController
 * @package App\Http\Controllers\Backend
 */
class CalendarController extends BackendController
{
    private $shopInfoRepository;
    private $calendarService;
    private $reserveDenialsRepository;
    private $reserveTelRepository;
    private $reserveRepository;

    public function __construct(ShopInfoRepository       $shopInfoRepository,
                                CalendarService          $calendarService,
                                ReserveDenialsRepository $reserveDenialsRepository,
                                ReserveTelRepository     $reserveTelRepository,
                                ReserveRepository        $reserveRepository
    )
    {
        $this->shopInfoRepository = $shopInfoRepository;
        $this->calendarService = $calendarService;
        $this->reserveDenialsRepository = $reserveDenialsRepository;
        $this->reserveTelRepository = $reserveTelRepository;
        $this->reserveRepository = $reserveRepository;
        $this->setBackUrlDefault('shop.index');
        $this->setTitle(getConfig('page_title.backend.calendar-setting'));
    }

    public function setting(Request $request)
    {
        $shop = $this->shopInfoRepository->findById((int)$request->shop_id);

        // shop is not exists
        if (empty($shop)) {
            return $this->_backToStart()->withErrors(trans('messages.no_data'));
        }

        $numberMonthsNext = $this->calendarService->getNumberMonthsNext($shop);

        return $this->render('backend.calendar.setting', compact('numberMonthsNext', 'shop'));
    }

    public function getReserveDenialsByShop($shopId)
    {
        $data = $this->reserveDenialsRepository->findWhere(['shop_id' => $shopId]);
        $html = view('backend.calendar.dialog._list_reserve_denials', compact('data'))->render();

        return successResponse(['html' => $html], '');
    }

    public function saveReserveDenials(Request $request)
    {
        $data = $request->data;
        $shopId = (int)$request->shop_id;

        // validator
        $dataValidator = ['shop_id' => $shopId, 'data' => $data];
        $valid = $this->reserveDenialsRepository->getValidator()->validate($dataValidator);
        if (!$valid) {
            $errors = $this->reserveDenialsRepository->getValidator()->errorsBag()->getMessages();
            return response(['status' => getConstant('RESPONSE.INVALID'), 'errors' => $errors]);
        }

        // create, update, remove
        $result = $this->calendarService->saveReserveDenials($shopId, $data);
        if (!$result) {
            return errorResponse([], 'failed!');
        }

        return successResponse([], 'saved!');
    }

    public function getDataCalendar(Request $request)
    {
        $dataRequest = $request->only('date', 'shop_id');
        $data = $this->calendarService->handleDataCalendar($dataRequest);

        return successResponse($data, 'get data success!');
    }

    public function createOrUpdateFlgTel(Request $request)
    {
        $data = $request->only('reserve_date', 'shop_id', 'tel_flg');
        $data['tel_flg'] = (int)$data['tel_flg'];

        // validator
        $valid = $this->reserveTelRepository->getValidator()->validate($data);
        if (!$valid) {
            $errors = $this->reserveTelRepository->getValidator()->errorsBag()->getMessages();
            return errorResponse(['errors' => $errors], 'is holiday');
        }

        // check holiday
        $isHoliday = $this->calendarService->isHoliday($data['shop_id'], $data['reserve_date']);
        if ($isHoliday) {
            return errorResponse([], 'is holiday');
        }

        $query['reserve_date'] = $data['reserve_date'];
        $query['shop_id'] = $data['shop_id'];
        $entity = $this->reserveTelRepository->findWhere($query)->first();

        $reserveTel = $this->calendarService->createOrUpdateFlgTel($entity, $data);
        if (empty($reserveTel)) {
            return errorResponse([], 'error');
        }

        return successResponse(['tel_flg' => $reserveTel->tel_flg], 'success');
    }

    public function getOperationTime(Request $request)
    {
        $dataRequest = $request->only('shop_id', 'reserve_date');

        $dataShop = $this->shopInfoRepository->findById((int)$dataRequest['shop_id']);
        if (empty($dataShop)) {
            return errorResponse([], "shop doesn't exist");
        }

        $html = $this->calendarService->getHtmlOperationTime($dataShop, $dataRequest);

        return successResponse(['html' => $html], 'success');
    }

    public function saveOperationTime(Request $request)
    {
        $data = $request->only('shop_id', 'data', 'reserve_date');
        $dataRoot = $data;

        // handle data reserve by time deny to validate
        $this->calendarService->handleDataByTimeDenial($data);

        // validate request
        $valid = $this->reserveRepository->getValidator()->validate($data);
        if (!$valid) {
            $errors = $this->reserveRepository->getValidator()->errorsBag()->getMessages();
            logError('data invalid ' . json_encode($errors));
            return response(['status' => getConstant('RESPONSE.INVALID'), 'errors' => $errors]);
        }

        // insert or update
        $result = $this->calendarService->insertOrUpdateReserve($dataRoot);

        if (!$result) {
            return errorResponse([], 'error');
        }

        $stock = $this->calendarService->handleStock($data['shop_id'], $data['reserve_date']);

        return successResponse(['stock' => $stock], 'success');
    }
}

