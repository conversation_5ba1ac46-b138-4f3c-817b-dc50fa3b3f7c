<?php

namespace App\Providers;

use App\Helpers\Facades\ExtendedRoute as Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        //
        parent::boot();
        getSystemConfig('use_ssl') ? \Illuminate\Support\Facades\URL::forceScheme('https') : null;

        Route::get('/', function () {
            echo("(PARA) Reserve One System");
        });
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiRoutes();
        $this->mapBackendRoutes();
        $this->mapFrontendRoutes();
    }

    protected function mapFrontendRoutes()
    {
        Route::middleware('frontend')
            ->prefix(getFrontendAlias())
            ->namespace($this->namespace . '\Frontend')->domain(getFrontendDomain())
            ->group(base_path('routes/frontend.php'));
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapBackendRoutes()
    {
        Route::middleware('backend')
            ->prefix(getBackendAlias())
            ->namespace($this->namespace . '\Backend')->domain(getBackendDomain())
            ->group(base_path('routes/backend.php'));
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        Route::prefix(getApiAlias())
            ->middleware('api')
            ->namespace($this->namespace . '\Api')->domain(getApiDomain())
            ->extension(config('app.api_extension'))
            ->group(base_path('routes/api.php'));
    }
}
