@extends('layouts.backend.layouts.main')
@section('content')
    <div id="success_msg_custom" style="display: none">
        <div class="row">
            <div class="col-md-12">
                <ul class="col-md-12 alert alert-success">
                    <li>
                        <i class="fa fa-check"></i>
                        <strong></strong>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div id="error_msg_custom" style="display: none">
        <div class="alert alert-danger">
            <ul>
                <li><strong></strong></li>
            </ul>
        </div>
    </div>

    <!-- start::Post -->
    <div class="post">
        <!-- start::Form -->
        <form class="form" action="calender-setting.html" method="post">
            <!-- start::Page title -->
            <div class="page-title flex align-items-center">
                <div class="mr-auto flex align-items-center">
                    <h1 class="heading-1">カレンダー設定</h1>
                </div>
                <div class="ml-auto">
                    <a data-click="openDialogDenials"
                       data-route="{{ route('calendar.getReserveDenialsByShop', ['shop_id' => request('shop_id')]) }}"
                       href="javascript:void(0)"
                       class="open-dialog-denials btn btn-primary">受入拒否時間帯一括設定</a>
                </div>
            </div>
            <!-- end::Page title -->
            <!-- start::Card -->
            <div class="card">
                <!-- start::Card body -->
                <div class="card-body">
                    <div class="scroll-area">
                        <div id="js-calender">
                            <div id="calender-header">
                                <a href="javascript:void(0);" id="calendar-prev-button" data-click="prevMonth"></a>
                                <div id="calender-month"></div>
                                <a href="javascript:void(0);" id="calendar-next-button" data-click="nextMonth"></a>
                            </div>
                            <div id="calendar"></div>
                        </div>
                    </div>
                </div>
                <!-- end::Card body -->
            </div>
            <!-- end::Card -->
        </form>
        <!-- end::Form -->
    </div>
    <!-- end::Post -->

    <!-- begin: dialog -->
    @include('backend.calendar.dialog._dialog_operation_time')
    @include('backend.calendar.dialog._dialog_reserve_denials')
    <!-- end: dialog -->

    <input id="current-shop-id" type="hidden" value="{{ request('shop_id') }}">
    <input id="route-get-data-calendar" type="hidden" value="{{ route('calendar.getDataCalendar') }}">
    <input id="route-change-flag-reserve-tel" type="hidden" value="{{ route('calendar.CreateOrUpdateFlgTel') }}">
    <input id="route-get-reserve-operation-time" type="hidden" value="{{ route('calendar.getOperationTime') }}">
    <input id="route-save-operation-time" type="hidden" value="{{ route('calendar.saveOperationTime') }}">
    <input id="number_months_next" type="hidden" value="{{ $numberMonthsNext }}">
    <input id="tel-shop" type="hidden" value="{{ $shop->tel }}">
    <input id="publish_from_dt" type="hidden" value="{{ !empty($shop->publish_from_dt) ? $shop->publish_from_dt : "" }}">
    <input id="publish_to_dt" type="hidden" value="{{ !empty($shop->publish_to_dt) ? $shop->publish_to_dt : "" }}">
@endsection

@push('scripts')

@endpush
