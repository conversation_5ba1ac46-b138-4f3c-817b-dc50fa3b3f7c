<?php

namespace App\Http\Controllers\Base;

use App\Helpers\Url;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\DB;

/**
 * Class FrontendController
 * @package App\Http\Controllers\Base
 */
class FrontendController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->setTitle(getConstant('FRONTEND_TITLE'));
    }

    /**
     * @var string
     */
    protected $_area = 'frontend';

    public function index()
    {
        return $this->render();
    }

    public function getCurrentUser()
    {
        return shopGuard()->user();
    }
}
