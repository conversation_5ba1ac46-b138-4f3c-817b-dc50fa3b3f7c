<?php

use App\Database\Migration\CustomBlueprint as Blueprint;
use App\Database\Migration\Schema;

class CreateShopsTable extends \App\Database\Migration\Create
{
    protected $_table = 'shops';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable($this->getTable())) {
            Schema::create($this->getTable(), function (Blueprint $table) {
                $table->increments('id');
                $table->text('shop_name')->comment('店舗名');
                $table->string('reserve_form_url', 500)->comment('来店予約URL');
                $table->text('address')->comment('住所');
                $table->string('shop_user_email', 500)->comment('管理者メールアドレス');
                $table->string('shop_user_pwd', 500)->comment('パスワード');
                $table->time('mon_operation_from')->comment('月曜日受付開始時間');
                $table->time('mon_operation_to')->comment('月曜日受付終了時間');
                $table->char('mon_closing_flg', 1)->comment('月曜日定休日フラグ');
                $table->time('tue_operation_from')->comment('火曜日受付開始時間');
                $table->time('tue_operation_to')->comment('火曜日受付終了時間');
                $table->char('tue_closing_flg', 1)->comment('火曜日定休日フラグ');
                $table->time('wed_operation_from')->comment('水曜日受付開始時間');
                $table->time('wed_operation_to')->comment('水曜日受付終了時間');
                $table->char('wed_closing_flg', 1)->comment('水曜日定休日フラグ');
                $table->time('thu_operation_from')->comment('木曜日受付開始時間');
                $table->time('thu_operation_to')->comment('木曜日受付終了時間');
                $table->char('thu_closing_flg', 1)->comment('木曜日定休日フラグ');
                $table->time('fri_operation_from')->comment('金曜日受付開始時間');
                $table->time('fri_operation_to')->comment('金曜日受付終了時間');
                $table->char('fri_closing_flg', 1)->comment('金曜日定休日フラグ');
                $table->time('sat_operation_from')->comment('土曜日受付開始時間');
                $table->time('sat_operation_to')->comment('土曜日受付終了時間');
                $table->char('sat_closing_flg', 1)->comment('土曜日定休日フラグ');
                $table->time('sun_operation_from')->comment('日曜日受付開始時間');
                $table->time('sun_operation_to')->comment('日曜日受付終了時間');
                $table->char('sun_closing_flg', 1)->comment('日曜日定休日フラグ');
                $table->tinyInteger('interval_min')->comment('インターバル');
                $table->tinyInteger('reservable_period_days')->comment('未来予約可能期間');
                $table->char('early_reservable_tel_flg', 1)->comment('受付開始前TELフラグ');
                $table->timestamp('publish_from_dt')->comment('予約受付開始日時');
                $table->timestamp('publish_to_dt')->nullable()->comment('予約受付終了日時');
                $table->tinyInteger('reservation_frame_num')->comment('予約枠数（ルーム数）');
                $table->string('shop_key', 50)->comment('店舗JSキー');
                $table->actionBy();
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }
}
