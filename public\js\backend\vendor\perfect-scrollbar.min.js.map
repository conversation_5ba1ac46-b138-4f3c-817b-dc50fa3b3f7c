{"version": 3, "file": "perfect-scrollbar.min.js", "sources": ["../src/handlers/mouse-wheel.js", "../src/update-geometry.js", "../src/lib/css.js", "../src/lib/dom.js", "../src/lib/class-names.js", "../src/process-scroll-diff.js", "../src/lib/util.js", "../src/handlers/drag-thumb.js", "../src/lib/event-manager.js", "../src/index.js", "../src/handlers/click-rail.js", "../src/handlers/keyboard.js", "../src/handlers/touch.js"], "sourcesContent": ["import * as CSS from '../lib/css';\nimport cls from '../lib/class-names';\nimport updateGeometry from '../update-geometry';\nimport { env } from '../lib/util';\n\nexport default function(i) {\n  const element = i.element;\n\n  let shouldPrevent = false;\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    const roundedScrollTop = Math.floor(element.scrollTop);\n    const isTop = element.scrollTop === 0;\n    const isBottom =\n      roundedScrollTop + element.offsetHeight === element.scrollHeight;\n    const isLeft = element.scrollLeft === 0;\n    const isRight =\n      element.scrollLeft + element.offsetWidth === element.scrollWidth;\n\n    let hitsBound;\n\n    // pick axis with primary direction\n    if (Math.abs(deltaY) > Math.abs(deltaX)) {\n      hitsBound = isTop || isBottom;\n    } else {\n      hitsBound = isLeft || isRight;\n    }\n\n    return hitsBound ? !i.settings.wheelPropagation : true;\n  }\n\n  function getDeltaFromEvent(e) {\n    let deltaX = e.deltaX;\n    let deltaY = -1 * e.deltaY;\n\n    if (typeof deltaX === 'undefined' || typeof deltaY === 'undefined') {\n      // OS X Safari\n      deltaX = (-1 * e.wheelDeltaX) / 6;\n      deltaY = e.wheelDeltaY / 6;\n    }\n\n    if (e.deltaMode && e.deltaMode === 1) {\n      // Firefox in deltaMode 1: Line scrolling\n      deltaX *= 10;\n      deltaY *= 10;\n    }\n\n    if (deltaX !== deltaX && deltaY !== deltaY /* NaN checks */) {\n      // IE in some mouse drivers\n      deltaX = 0;\n      deltaY = e.wheelDelta;\n    }\n\n    if (e.shiftKey) {\n      // reverse axis with shift key\n      return [-deltaY, -deltaX];\n    }\n    return [deltaX, deltaY];\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    // FIXME: this is a workaround for <select> issue in FF and IE #571\n    if (!env.isWebKit && element.querySelector('select:focus')) {\n      return true;\n    }\n\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    let cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      const style = CSS.get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        const maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        const maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function mousewheelHandler(e) {\n    const [deltaX, deltaY] = getDeltaFromEvent(e);\n\n    if (shouldBeConsumedByChild(e.target, deltaX, deltaY)) {\n      return;\n    }\n\n    let shouldPrevent = false;\n    if (!i.settings.useBothWheelAxes) {\n      // deltaX will only be used for horizontal scrolling and deltaY will\n      // only be used for vertical scrolling - this is the default\n      element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      element.scrollLeft += deltaX * i.settings.wheelSpeed;\n    } else if (i.scrollbarYActive && !i.scrollbarXActive) {\n      // only vertical scrollbar is active and useBothWheelAxes option is\n      // active, so let's scroll vertical bar using both mouse wheel axes\n      if (deltaY) {\n        element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      } else {\n        element.scrollTop += deltaX * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    } else if (i.scrollbarXActive && !i.scrollbarYActive) {\n      // useBothWheelAxes and only horizontal bar is active, so use both\n      // wheel axes for horizontal bar\n      if (deltaX) {\n        element.scrollLeft += deltaX * i.settings.wheelSpeed;\n      } else {\n        element.scrollLeft -= deltaY * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    }\n\n    updateGeometry(i);\n\n    shouldPrevent = shouldPrevent || shouldPreventDefault(deltaX, deltaY);\n    if (shouldPrevent && !e.ctrlKey) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n\n  if (typeof window.onwheel !== 'undefined') {\n    i.event.bind(element, 'wheel', mousewheelHandler);\n  } else if (typeof window.onmousewheel !== 'undefined') {\n    i.event.bind(element, 'mousewheel', mousewheelHandler);\n  }\n}\n", "import * as CSS from './lib/css';\nimport * as DOM from './lib/dom';\nimport cls from './lib/class-names';\nimport { toInt } from './lib/util';\n\nexport default function(i) {\n  const element = i.element;\n  const roundedScrollTop = Math.floor(element.scrollTop);\n  const rect = element.getBoundingClientRect();\n\n  i.containerWidth = Math.round(rect.width);\n  i.containerHeight = Math.round(rect.height);\n\n  i.contentWidth = element.scrollWidth;\n  i.contentHeight = element.scrollHeight;\n\n  if (!element.contains(i.scrollbarXRail)) {\n    // clean up and append\n    DOM.queryChildren(element, cls.element.rail('x')).forEach(el =>\n      DOM.remove(el)\n    );\n    element.appendChild(i.scrollbarXRail);\n  }\n  if (!element.contains(i.scrollbarYRail)) {\n    // clean up and append\n    DOM.queryChildren(element, cls.element.rail('y')).forEach(el =>\n      DOM.remove(el)\n    );\n    element.appendChild(i.scrollbarYRail);\n  }\n\n  if (\n    !i.settings.suppressScrollX &&\n    i.containerWidth + i.settings.scrollXMarginOffset < i.contentWidth\n  ) {\n    i.scrollbarXActive = true;\n    i.railXWidth = i.containerWidth - i.railXMarginWidth;\n    i.railXRatio = i.containerWidth / i.railXWidth;\n    i.scrollbarXWidth = getThumbSize(\n      i,\n      toInt((i.railXWidth * i.containerWidth) / i.contentWidth)\n    );\n    i.scrollbarXLeft = toInt(\n      ((i.negativeScrollAdjustment + element.scrollLeft) *\n        (i.railXWidth - i.scrollbarXWidth)) /\n        (i.contentWidth - i.containerWidth)\n    );\n  } else {\n    i.scrollbarXActive = false;\n  }\n\n  if (\n    !i.settings.suppressScrollY &&\n    i.containerHeight + i.settings.scrollYMarginOffset < i.contentHeight\n  ) {\n    i.scrollbarYActive = true;\n    i.railYHeight = i.containerHeight - i.railYMarginHeight;\n    i.railYRatio = i.containerHeight / i.railYHeight;\n    i.scrollbarYHeight = getThumbSize(\n      i,\n      toInt((i.railYHeight * i.containerHeight) / i.contentHeight)\n    );\n    i.scrollbarYTop = toInt(\n      (roundedScrollTop * (i.railYHeight - i.scrollbarYHeight)) /\n        (i.contentHeight - i.containerHeight)\n    );\n  } else {\n    i.scrollbarYActive = false;\n  }\n\n  if (i.scrollbarXLeft >= i.railXWidth - i.scrollbarXWidth) {\n    i.scrollbarXLeft = i.railXWidth - i.scrollbarXWidth;\n  }\n  if (i.scrollbarYTop >= i.railYHeight - i.scrollbarYHeight) {\n    i.scrollbarYTop = i.railYHeight - i.scrollbarYHeight;\n  }\n\n  updateCss(element, i);\n\n  if (i.scrollbarXActive) {\n    element.classList.add(cls.state.active('x'));\n  } else {\n    element.classList.remove(cls.state.active('x'));\n    i.scrollbarXWidth = 0;\n    i.scrollbarXLeft = 0;\n    element.scrollLeft = i.isRtl === true ? i.contentWidth : 0;\n  }\n  if (i.scrollbarYActive) {\n    element.classList.add(cls.state.active('y'));\n  } else {\n    element.classList.remove(cls.state.active('y'));\n    i.scrollbarYHeight = 0;\n    i.scrollbarYTop = 0;\n    element.scrollTop = 0;\n  }\n}\n\nfunction getThumbSize(i, thumbSize) {\n  if (i.settings.minScrollbarLength) {\n    thumbSize = Math.max(thumbSize, i.settings.minScrollbarLength);\n  }\n  if (i.settings.maxScrollbarLength) {\n    thumbSize = Math.min(thumbSize, i.settings.maxScrollbarLength);\n  }\n  return thumbSize;\n}\n\nfunction updateCss(element, i) {\n  const xRailOffset = { width: i.railXWidth };\n  const roundedScrollTop = Math.floor(element.scrollTop);\n\n  if (i.isRtl) {\n    xRailOffset.left =\n      i.negativeScrollAdjustment +\n      element.scrollLeft +\n      i.containerWidth -\n      i.contentWidth;\n  } else {\n    xRailOffset.left = element.scrollLeft;\n  }\n  if (i.isScrollbarXUsingBottom) {\n    xRailOffset.bottom = i.scrollbarXBottom - roundedScrollTop;\n  } else {\n    xRailOffset.top = i.scrollbarXTop + roundedScrollTop;\n  }\n  CSS.set(i.scrollbarXRail, xRailOffset);\n\n  const yRailOffset = { top: roundedScrollTop, height: i.railYHeight };\n  if (i.isScrollbarYUsingRight) {\n    if (i.isRtl) {\n      yRailOffset.right =\n        i.contentWidth -\n        (i.negativeScrollAdjustment + element.scrollLeft) -\n        i.scrollbarYRight -\n        i.scrollbarYOuterWidth -\n        9;\n    } else {\n      yRailOffset.right = i.scrollbarYRight - element.scrollLeft;\n    }\n  } else {\n    if (i.isRtl) {\n      yRailOffset.left =\n        i.negativeScrollAdjustment +\n        element.scrollLeft +\n        i.containerWidth * 2 -\n        i.contentWidth -\n        i.scrollbarYLeft -\n        i.scrollbarYOuterWidth;\n    } else {\n      yRailOffset.left = i.scrollbarYLeft + element.scrollLeft;\n    }\n  }\n  CSS.set(i.scrollbarYRail, yRailOffset);\n\n  CSS.set(i.scrollbarX, {\n    left: i.scrollbarXLeft,\n    width: i.scrollbarXWidth - i.railBorderXWidth,\n  });\n  CSS.set(i.scrollbarY, {\n    top: i.scrollbarYTop,\n    height: i.scrollbarYHeight - i.railBorderYWidth,\n  });\n}\n", "export function get(element) {\n  return getComputedStyle(element);\n}\n\nexport function set(element, obj) {\n  for (const key in obj) {\n    let val = obj[key];\n    if (typeof val === 'number') {\n      val = `${val}px`;\n    }\n    element.style[key] = val;\n  }\n  return element;\n}\n", "export function div(className) {\n  const div = document.createElement('div');\n  div.className = className;\n  return div;\n}\n\nconst elMatches =\n  typeof Element !== 'undefined' &&\n  (Element.prototype.matches ||\n    Element.prototype.webkitMatchesSelector ||\n    Element.prototype.mozMatchesSelector ||\n    Element.prototype.msMatchesSelector);\n\nexport function matches(element, query) {\n  if (!elMatches) {\n    throw new Error('No element matching method supported');\n  }\n\n  return elMatches.call(element, query);\n}\n\nexport function remove(element) {\n  if (element.remove) {\n    element.remove();\n  } else {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element);\n    }\n  }\n}\n\nexport function queryChildren(element, selector) {\n  return Array.prototype.filter.call(element.children, child =>\n    matches(child, selector)\n  );\n}\n", "const cls = {\n  main: 'ps',\n  rtl: 'ps__rtl',\n  element: {\n    thumb: x => `ps__thumb-${x}`,\n    rail: x => `ps__rail-${x}`,\n    consuming: 'ps__child--consume',\n  },\n  state: {\n    focus: 'ps--focus',\n    clicking: 'ps--clicking',\n    active: x => `ps--active-${x}`,\n    scrolling: x => `ps--scrolling-${x}`,\n  },\n};\n\nexport default cls;\n\n/*\n * Helper methods\n */\nconst scrollingClassTimeout = { x: null, y: null };\n\nexport function addScrollingClass(i, x) {\n  const classList = i.element.classList;\n  const className = cls.state.scrolling(x);\n\n  if (classList.contains(className)) {\n    clearTimeout(scrollingClassTimeout[x]);\n  } else {\n    classList.add(className);\n  }\n}\n\nexport function removeScrollingClass(i, x) {\n  scrollingClassTimeout[x] = setTimeout(\n    () => i.isAlive && i.element.classList.remove(cls.state.scrolling(x)),\n    i.settings.scrollingThreshold\n  );\n}\n\nexport function setScrollingClassInstantly(i, x) {\n  addScrollingClass(i, x);\n  removeScrollingClass(i, x);\n}\n", "import { setScrollingClassInstantly } from './lib/class-names';\n\nfunction createEvent(name) {\n  if (typeof window.CustomEvent === 'function') {\n    return new CustomEvent(name);\n  } else {\n    const evt = document.createEvent('CustomEvent');\n    evt.initCustomEvent(name, false, false, undefined);\n    return evt;\n  }\n}\n\nexport default function(\n  i,\n  axis,\n  diff,\n  useScrollingClass = true,\n  forceFireReachEvent = false\n) {\n  let fields;\n  if (axis === 'top') {\n    fields = [\n      'contentHeight',\n      'containerHeight',\n      'scrollTop',\n      'y',\n      'up',\n      'down',\n    ];\n  } else if (axis === 'left') {\n    fields = [\n      'contentWidth',\n      'containerWidth',\n      'scrollLeft',\n      'x',\n      'left',\n      'right',\n    ];\n  } else {\n    throw new Error('A proper axis should be provided');\n  }\n\n  processScrollDiff(i, diff, fields, useScrollingClass, forceFireReachEvent);\n}\n\nfunction processScrollDiff(\n  i,\n  diff,\n  [contentHeight, containerHeight, scrollTop, y, up, down],\n  useScrollingClass = true,\n  forceFireReachEvent = false\n) {\n  const element = i.element;\n\n  // reset reach\n  i.reach[y] = null;\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] < 1) {\n    i.reach[y] = 'start';\n  }\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] > i[contentHeight] - i[containerHeight] - 1) {\n    i.reach[y] = 'end';\n  }\n\n  if (diff) {\n    element.dispatchEvent(createEvent(`ps-scroll-${y}`));\n\n    if (diff < 0) {\n      element.dispatchEvent(createEvent(`ps-scroll-${up}`));\n    } else if (diff > 0) {\n      element.dispatchEvent(createEvent(`ps-scroll-${down}`));\n    }\n\n    if (useScrollingClass) {\n      setScrollingClassInstantly(i, y);\n    }\n  }\n\n  if (i.reach[y] && (diff || forceFireReachEvent)) {\n    element.dispatchEvent(createEvent(`ps-${y}-reach-${i.reach[y]}`));\n  }\n}\n", "import * as CSS from './css';\nimport * as DOM from './dom';\n\nexport function toInt(x) {\n  return parseInt(x, 10) || 0;\n}\n\nexport function isEditable(el) {\n  return (\n    DOM.matches(el, 'input,[contenteditable]') ||\n    DOM.matches(el, 'select,[contenteditable]') ||\n    DOM.matches(el, 'textarea,[contenteditable]') ||\n    DOM.matches(el, 'button,[contenteditable]')\n  );\n}\n\nexport function outerWidth(element) {\n  const styles = CSS.get(element);\n  return (\n    toInt(styles.width) +\n    toInt(styles.paddingLeft) +\n    toInt(styles.paddingRight) +\n    toInt(styles.borderLeftWidth) +\n    toInt(styles.borderRightWidth)\n  );\n}\n\nexport const env = {\n  isWebKit:\n    typeof document !== 'undefined' &&\n    'WebkitAppearance' in document.documentElement.style,\n  supportsTouch:\n    typeof window !== 'undefined' &&\n    ('ontouchstart' in window ||\n      ('maxTouchPoints' in window.navigator &&\n        window.navigator.maxTouchPoints > 0) ||\n      (window.DocumentTouch && document instanceof window.DocumentTouch)),\n  supportsIePointer:\n    typeof navigator !== 'undefined' && navigator.msMaxTouchPoints,\n  isChrome:\n    typeof navigator !== 'undefined' &&\n    /Chrome/i.test(navigator && navigator.userAgent),\n};\n", "import * as CSS from '../lib/css';\nimport * as DOM from '../lib/dom';\nimport cls, {\n  addScrollingClass,\n  removeScrollingClass,\n} from '../lib/class-names';\nimport updateGeometry from '../update-geometry';\nimport { toInt } from '../lib/util';\n\nexport default function(i) {\n  bindMouseScrollHandler(i, [\n    'containerWidth',\n    'contentWidth',\n    'pageX',\n    'railXWidth',\n    'scrollbarX',\n    'scrollbarXWidth',\n    'scrollLeft',\n    'x',\n    'scrollbarXRail',\n  ]);\n  bindMouseScrollHandler(i, [\n    'containerHeight',\n    'contentHeight',\n    'pageY',\n    'railYHeight',\n    'scrollbarY',\n    'scrollbarYHeight',\n    'scrollTop',\n    'y',\n    'scrollbarYRail',\n  ]);\n}\n\nfunction bindMouseScrollHandler(\n  i,\n  [\n    containerHeight,\n    contentHeight,\n    pageY,\n    railYHeight,\n    scrollbarY,\n    scrollbarYHeight,\n    scrollTop,\n    y,\n    scrollbarYRail,\n  ]\n) {\n  const element = i.element;\n\n  let startingScrollTop = null;\n  let startingMousePageY = null;\n  let scrollBy = null;\n\n  function mouseMoveHandler(e) {\n    if (e.touches && e.touches[0]) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    element[scrollTop] =\n      startingScrollTop + scrollBy * (e[pageY] - startingMousePageY);\n    addScrollingClass(i, y);\n    updateGeometry(i);\n\n    e.stopPropagation();\n    if (e.type.startsWith('touch') && e.changedTouches.length > 1) {\n      e.preventDefault();\n    }\n  }\n\n  function mouseUpHandler() {\n    removeScrollingClass(i, y);\n    i[scrollbarYRail].classList.remove(cls.state.clicking);\n    i.event.unbind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n  }\n\n  function bindMoves(e, touchMode) {\n    startingScrollTop = element[scrollTop];\n    if (touchMode && e.touches) {\n      e[pageY] = e.touches[0].pageY;\n    }\n    startingMousePageY = e[pageY];\n    scrollBy =\n      (i[contentHeight] - i[containerHeight]) /\n      (i[railYHeight] - i[scrollbarYHeight]);\n    if (!touchMode) {\n      i.event.bind(i.ownerDocument, 'mousemove', mouseMoveHandler);\n      i.event.once(i.ownerDocument, 'mouseup', mouseUpHandler);\n      e.preventDefault();\n    } else {\n      i.event.bind(i.ownerDocument, 'touchmove', mouseMoveHandler);\n    }\n\n    i[scrollbarYRail].classList.add(cls.state.clicking);\n\n    e.stopPropagation();\n  }\n\n  i.event.bind(i[scrollbarY], 'mousedown', e => {\n    bindMoves(e);\n  });\n  i.event.bind(i[scrollbarY], 'touchstart', e => {\n    bindMoves(e, true);\n  });\n}\n", "class EventElement {\n  constructor(element) {\n    this.element = element;\n    this.handlers = {};\n  }\n\n  bind(eventName, handler) {\n    if (typeof this.handlers[eventName] === 'undefined') {\n      this.handlers[eventName] = [];\n    }\n    this.handlers[eventName].push(handler);\n    this.element.addEventListener(eventName, handler, false);\n  }\n\n  unbind(eventName, target) {\n    this.handlers[eventName] = this.handlers[eventName].filter(handler => {\n      if (target && handler !== target) {\n        return true;\n      }\n      this.element.removeEventListener(eventName, handler, false);\n      return false;\n    });\n  }\n\n  unbindAll() {\n    for (const name in this.handlers) {\n      this.unbind(name);\n    }\n  }\n\n  get isEmpty() {\n    return Object.keys(this.handlers).every(\n      key => this.handlers[key].length === 0\n    );\n  }\n}\n\nexport default class EventManager {\n  constructor() {\n    this.eventElements = [];\n  }\n\n  eventElement(element) {\n    let ee = this.eventElements.filter(ee => ee.element === element)[0];\n    if (!ee) {\n      ee = new EventElement(element);\n      this.eventElements.push(ee);\n    }\n    return ee;\n  }\n\n  bind(element, eventName, handler) {\n    this.eventElement(element).bind(eventName, handler);\n  }\n\n  unbind(element, eventName, handler) {\n    const ee = this.eventElement(element);\n    ee.unbind(eventName, handler);\n\n    if (ee.isEmpty) {\n      // remove\n      this.eventElements.splice(this.eventElements.indexOf(ee), 1);\n    }\n  }\n\n  unbindAll() {\n    this.eventElements.forEach(e => e.unbindAll());\n    this.eventElements = [];\n  }\n\n  once(element, eventName, handler) {\n    const ee = this.eventElement(element);\n    const onceHandler = evt => {\n      ee.unbind(eventName, onceHandler);\n      handler(evt);\n    };\n    ee.bind(eventName, onceHandler);\n  }\n}\n", "import * as CSS from './lib/css';\nimport * as DOM from './lib/dom';\nimport cls from './lib/class-names';\nimport EventManager from './lib/event-manager';\nimport processScrollDiff from './process-scroll-diff';\nimport updateGeometry from './update-geometry';\nimport { toInt, outerWidth } from './lib/util';\n\nimport clickRail from './handlers/click-rail';\nimport dragThumb from './handlers/drag-thumb';\nimport keyboard from './handlers/keyboard';\nimport wheel from './handlers/mouse-wheel';\nimport touch from './handlers/touch';\n\nconst defaultSettings = () => ({\n  handlers: ['click-rail', 'drag-thumb', 'keyboard', 'wheel', 'touch'],\n  maxScrollbarLength: null,\n  minScrollbarLength: null,\n  scrollingThreshold: 1000,\n  scrollXMarginOffset: 0,\n  scrollYMarginOffset: 0,\n  suppressScrollX: false,\n  suppressScrollY: false,\n  swipeEasing: true,\n  useBothWheelAxes: false,\n  wheelPropagation: true,\n  wheelSpeed: 1,\n});\n\nconst handlers = {\n  'click-rail': clickRail,\n  'drag-thumb': dragThumb,\n  keyboard,\n  wheel,\n  touch,\n};\n\nexport default class PerfectScrollbar {\n  constructor(element, userSettings = {}) {\n    if (typeof element === 'string') {\n      element = document.querySelector(element);\n    }\n\n    if (!element || !element.nodeName) {\n      throw new Error('no element is specified to initialize PerfectScrollbar');\n    }\n\n    this.element = element;\n\n    element.classList.add(cls.main);\n\n    this.settings = defaultSettings();\n    for (const key in userSettings) {\n      this.settings[key] = userSettings[key];\n    }\n\n    this.containerWidth = null;\n    this.containerHeight = null;\n    this.contentWidth = null;\n    this.contentHeight = null;\n\n    const focus = () => element.classList.add(cls.state.focus);\n    const blur = () => element.classList.remove(cls.state.focus);\n\n    this.isRtl = CSS.get(element).direction === 'rtl';\n    if (this.isRtl === true) {\n      element.classList.add(cls.rtl);\n    }\n    this.isNegativeScroll = (() => {\n      const originalScrollLeft = element.scrollLeft;\n      let result = null;\n      element.scrollLeft = -1;\n      result = element.scrollLeft < 0;\n      element.scrollLeft = originalScrollLeft;\n      return result;\n    })();\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? element.scrollWidth - element.clientWidth\n      : 0;\n    this.event = new EventManager();\n    this.ownerDocument = element.ownerDocument || document;\n\n    this.scrollbarXRail = DOM.div(cls.element.rail('x'));\n    element.appendChild(this.scrollbarXRail);\n    this.scrollbarX = DOM.div(cls.element.thumb('x'));\n    this.scrollbarXRail.appendChild(this.scrollbarX);\n    this.scrollbarX.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarX, 'focus', focus);\n    this.event.bind(this.scrollbarX, 'blur', blur);\n    this.scrollbarXActive = null;\n    this.scrollbarXWidth = null;\n    this.scrollbarXLeft = null;\n    const railXStyle = CSS.get(this.scrollbarXRail);\n    this.scrollbarXBottom = parseInt(railXStyle.bottom, 10);\n    if (isNaN(this.scrollbarXBottom)) {\n      this.isScrollbarXUsingBottom = false;\n      this.scrollbarXTop = toInt(railXStyle.top);\n    } else {\n      this.isScrollbarXUsingBottom = true;\n    }\n    this.railBorderXWidth =\n      toInt(railXStyle.borderLeftWidth) + toInt(railXStyle.borderRightWidth);\n    // Set rail to display:block to calculate margins\n    CSS.set(this.scrollbarXRail, { display: 'block' });\n    this.railXMarginWidth =\n      toInt(railXStyle.marginLeft) + toInt(railXStyle.marginRight);\n    CSS.set(this.scrollbarXRail, { display: '' });\n    this.railXWidth = null;\n    this.railXRatio = null;\n\n    this.scrollbarYRail = DOM.div(cls.element.rail('y'));\n    element.appendChild(this.scrollbarYRail);\n    this.scrollbarY = DOM.div(cls.element.thumb('y'));\n    this.scrollbarYRail.appendChild(this.scrollbarY);\n    this.scrollbarY.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarY, 'focus', focus);\n    this.event.bind(this.scrollbarY, 'blur', blur);\n    this.scrollbarYActive = null;\n    this.scrollbarYHeight = null;\n    this.scrollbarYTop = null;\n    const railYStyle = CSS.get(this.scrollbarYRail);\n    this.scrollbarYRight = parseInt(railYStyle.right, 10);\n    if (isNaN(this.scrollbarYRight)) {\n      this.isScrollbarYUsingRight = false;\n      this.scrollbarYLeft = toInt(railYStyle.left);\n    } else {\n      this.isScrollbarYUsingRight = true;\n    }\n    this.scrollbarYOuterWidth = this.isRtl ? outerWidth(this.scrollbarY) : null;\n    this.railBorderYWidth =\n      toInt(railYStyle.borderTopWidth) + toInt(railYStyle.borderBottomWidth);\n    CSS.set(this.scrollbarYRail, { display: 'block' });\n    this.railYMarginHeight =\n      toInt(railYStyle.marginTop) + toInt(railYStyle.marginBottom);\n    CSS.set(this.scrollbarYRail, { display: '' });\n    this.railYHeight = null;\n    this.railYRatio = null;\n\n    this.reach = {\n      x:\n        element.scrollLeft <= 0\n          ? 'start'\n          : element.scrollLeft >= this.contentWidth - this.containerWidth\n          ? 'end'\n          : null,\n      y:\n        element.scrollTop <= 0\n          ? 'start'\n          : element.scrollTop >= this.contentHeight - this.containerHeight\n          ? 'end'\n          : null,\n    };\n\n    this.isAlive = true;\n\n    this.settings.handlers.forEach(handlerName => handlers[handlerName](this));\n\n    this.lastScrollTop = Math.floor(element.scrollTop); // for onScroll only\n    this.lastScrollLeft = element.scrollLeft; // for onScroll only\n    this.event.bind(this.element, 'scroll', e => this.onScroll(e));\n    updateGeometry(this);\n  }\n\n  update() {\n    if (!this.isAlive) {\n      return;\n    }\n\n    // Recalcuate negative scrollLeft adjustment\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? this.element.scrollWidth - this.element.clientWidth\n      : 0;\n\n    // Recalculate rail margins\n    CSS.set(this.scrollbarXRail, { display: 'block' });\n    CSS.set(this.scrollbarYRail, { display: 'block' });\n    this.railXMarginWidth =\n      toInt(CSS.get(this.scrollbarXRail).marginLeft) +\n      toInt(CSS.get(this.scrollbarXRail).marginRight);\n    this.railYMarginHeight =\n      toInt(CSS.get(this.scrollbarYRail).marginTop) +\n      toInt(CSS.get(this.scrollbarYRail).marginBottom);\n\n    // Hide scrollbars not to affect scrollWidth and scrollHeight\n    CSS.set(this.scrollbarXRail, { display: 'none' });\n    CSS.set(this.scrollbarYRail, { display: 'none' });\n\n    updateGeometry(this);\n\n    processScrollDiff(this, 'top', 0, false, true);\n    processScrollDiff(this, 'left', 0, false, true);\n\n    CSS.set(this.scrollbarXRail, { display: '' });\n    CSS.set(this.scrollbarYRail, { display: '' });\n  }\n\n  onScroll(e) {\n    if (!this.isAlive) {\n      return;\n    }\n\n    updateGeometry(this);\n    processScrollDiff(this, 'top', this.element.scrollTop - this.lastScrollTop);\n    processScrollDiff(\n      this,\n      'left',\n      this.element.scrollLeft - this.lastScrollLeft\n    );\n\n    this.lastScrollTop = Math.floor(this.element.scrollTop);\n    this.lastScrollLeft = this.element.scrollLeft;\n  }\n\n  destroy() {\n    if (!this.isAlive) {\n      return;\n    }\n\n    this.event.unbindAll();\n    DOM.remove(this.scrollbarX);\n    DOM.remove(this.scrollbarY);\n    DOM.remove(this.scrollbarXRail);\n    DOM.remove(this.scrollbarYRail);\n    this.removePsClasses();\n\n    // unset elements\n    this.element = null;\n    this.scrollbarX = null;\n    this.scrollbarY = null;\n    this.scrollbarXRail = null;\n    this.scrollbarYRail = null;\n\n    this.isAlive = false;\n  }\n\n  removePsClasses() {\n    this.element.className = this.element.className\n      .split(' ')\n      .filter(name => !name.match(/^ps([-_].+|)$/))\n      .join(' ');\n  }\n}\n", "import updateGeometry from '../update-geometry';\n\nexport default function(i) {\n  const element = i.element;\n\n  i.event.bind(i.scrollbarY, 'mousedown', e => e.stopPropagation());\n  i.event.bind(i.scrollbarYRail, 'mousedown', e => {\n    const positionTop =\n      e.pageY -\n      window.pageYOffset -\n      i.scrollbarYRail.getBoundingClientRect().top;\n    const direction = positionTop > i.scrollbarYTop ? 1 : -1;\n\n    i.element.scrollTop += direction * i.containerHeight;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n\n  i.event.bind(i.scrollbarX, 'mousedown', e => e.stopPropagation());\n  i.event.bind(i.scrollbarXRail, 'mousedown', e => {\n    const positionLeft =\n      e.pageX -\n      window.pageXOffset -\n      i.scrollbarXRail.getBoundingClientRect().left;\n    const direction = positionLeft > i.scrollbarXLeft ? 1 : -1;\n\n    i.element.scrollLeft += direction * i.containerWidth;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n}\n", "import * as DOM from '../lib/dom';\nimport updateGeometry from '../update-geometry';\nimport { isEditable } from '../lib/util';\n\nexport default function(i) {\n  const element = i.element;\n\n  const elementHovered = () => DOM.matches(element, ':hover');\n  const scrollbarFocused = () =>\n    DOM.matches(i.scrollbarX, ':focus') || DOM.matches(i.scrollbarY, ':focus');\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    const scrollTop = Math.floor(element.scrollTop);\n    if (deltaX === 0) {\n      if (!i.scrollbarYActive) {\n        return false;\n      }\n      if (\n        (scrollTop === 0 && deltaY > 0) ||\n        (scrollTop >= i.contentHeight - i.containerHeight && deltaY < 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n\n    const scrollLeft = element.scrollLeft;\n    if (deltaY === 0) {\n      if (!i.scrollbarXActive) {\n        return false;\n      }\n      if (\n        (scrollLeft === 0 && deltaX < 0) ||\n        (scrollLeft >= i.contentWidth - i.containerWidth && deltaX > 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    return true;\n  }\n\n  i.event.bind(i.ownerDocument, 'keydown', e => {\n    if (\n      (e.isDefaultPrevented && e.isDefaultPrevented()) ||\n      e.defaultPrevented\n    ) {\n      return;\n    }\n\n    if (!elementHovered() && !scrollbarFocused()) {\n      return;\n    }\n\n    let activeElement = document.activeElement\n      ? document.activeElement\n      : i.ownerDocument.activeElement;\n    if (activeElement) {\n      if (activeElement.tagName === 'IFRAME') {\n        activeElement = activeElement.contentDocument.activeElement;\n      } else {\n        // go deeper if element is a webcomponent\n        while (activeElement.shadowRoot) {\n          activeElement = activeElement.shadowRoot.activeElement;\n        }\n      }\n      if (isEditable(activeElement)) {\n        return;\n      }\n    }\n\n    let deltaX = 0;\n    let deltaY = 0;\n\n    switch (e.which) {\n      case 37: // left\n        if (e.metaKey) {\n          deltaX = -i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = -i.containerWidth;\n        } else {\n          deltaX = -30;\n        }\n        break;\n      case 38: // up\n        if (e.metaKey) {\n          deltaY = i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = 30;\n        }\n        break;\n      case 39: // right\n        if (e.metaKey) {\n          deltaX = i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = i.containerWidth;\n        } else {\n          deltaX = 30;\n        }\n        break;\n      case 40: // down\n        if (e.metaKey) {\n          deltaY = -i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = -i.containerHeight;\n        } else {\n          deltaY = -30;\n        }\n        break;\n      case 32: // space bar\n        if (e.shiftKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = -i.containerHeight;\n        }\n        break;\n      case 33: // page up\n        deltaY = i.containerHeight;\n        break;\n      case 34: // page down\n        deltaY = -i.containerHeight;\n        break;\n      case 36: // home\n        deltaY = i.contentHeight;\n        break;\n      case 35: // end\n        deltaY = -i.contentHeight;\n        break;\n      default:\n        return;\n    }\n\n    if (i.settings.suppressScrollX && deltaX !== 0) {\n      return;\n    }\n    if (i.settings.suppressScrollY && deltaY !== 0) {\n      return;\n    }\n\n    element.scrollTop -= deltaY;\n    element.scrollLeft += deltaX;\n    updateGeometry(i);\n\n    if (shouldPreventDefault(deltaX, deltaY)) {\n      e.preventDefault();\n    }\n  });\n}\n", "import updateGeometry from '../update-geometry';\nimport cls from '../lib/class-names';\nimport * as CSS from '../lib/css';\nimport { env } from '../lib/util';\n\nexport default function(i) {\n  if (!env.supportsTouch && !env.supportsIePointer) {\n    return;\n  }\n\n  const element = i.element;\n\n  function shouldPrevent(deltaX, deltaY) {\n    const scrollTop = Math.floor(element.scrollTop);\n    const scrollLeft = element.scrollLeft;\n    const magnitudeX = Math.abs(deltaX);\n    const magnitudeY = Math.abs(deltaY);\n\n    if (magnitudeY > magnitudeX) {\n      // user is perhaps trying to swipe up/down the page\n\n      if (\n        (deltaY < 0 && scrollTop === i.contentHeight - i.containerHeight) ||\n        (deltaY > 0 && scrollTop === 0)\n      ) {\n        // set prevent for mobile Chrome refresh\n        return window.scrollY === 0 && deltaY > 0 && env.isChrome;\n      }\n    } else if (magnitudeX > magnitudeY) {\n      // user is perhaps trying to swipe left/right across the page\n\n      if (\n        (deltaX < 0 && scrollLeft === i.contentWidth - i.containerWidth) ||\n        (deltaX > 0 && scrollLeft === 0)\n      ) {\n        return true;\n      }\n    }\n\n    return true;\n  }\n\n  function applyTouchMove(differenceX, differenceY) {\n    element.scrollTop -= differenceY;\n    element.scrollLeft -= differenceX;\n\n    updateGeometry(i);\n  }\n\n  let startOffset = {};\n  let startTime = 0;\n  let speed = {};\n  let easingLoop = null;\n\n  function getTouch(e) {\n    if (e.targetTouches) {\n      return e.targetTouches[0];\n    } else {\n      // Maybe IE pointer\n      return e;\n    }\n  }\n\n  function shouldHandle(e) {\n    if (e.pointerType && e.pointerType === 'pen' && e.buttons === 0) {\n      return false;\n    }\n    if (e.targetTouches && e.targetTouches.length === 1) {\n      return true;\n    }\n    if (\n      e.pointerType &&\n      e.pointerType !== 'mouse' &&\n      e.pointerType !== e.MSPOINTER_TYPE_MOUSE\n    ) {\n      return true;\n    }\n    return false;\n  }\n\n  function touchStart(e) {\n    if (!shouldHandle(e)) {\n      return;\n    }\n\n    const touch = getTouch(e);\n\n    startOffset.pageX = touch.pageX;\n    startOffset.pageY = touch.pageY;\n\n    startTime = new Date().getTime();\n\n    if (easingLoop !== null) {\n      clearInterval(easingLoop);\n    }\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    let cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      const style = CSS.get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        const maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        const maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function touchMove(e) {\n    if (shouldHandle(e)) {\n      const touch = getTouch(e);\n\n      const currentOffset = { pageX: touch.pageX, pageY: touch.pageY };\n\n      const differenceX = currentOffset.pageX - startOffset.pageX;\n      const differenceY = currentOffset.pageY - startOffset.pageY;\n\n      if (shouldBeConsumedByChild(e.target, differenceX, differenceY)) {\n        return;\n      }\n\n      applyTouchMove(differenceX, differenceY);\n      startOffset = currentOffset;\n\n      const currentTime = new Date().getTime();\n\n      const timeGap = currentTime - startTime;\n      if (timeGap > 0) {\n        speed.x = differenceX / timeGap;\n        speed.y = differenceY / timeGap;\n        startTime = currentTime;\n      }\n\n      if (shouldPrevent(differenceX, differenceY)) {\n        e.preventDefault();\n      }\n    }\n  }\n  function touchEnd() {\n    if (i.settings.swipeEasing) {\n      clearInterval(easingLoop);\n      easingLoop = setInterval(function() {\n        if (i.isInitialized) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        if (!speed.x && !speed.y) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        if (Math.abs(speed.x) < 0.01 && Math.abs(speed.y) < 0.01) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        if (!i.element) {\n          clearInterval(easingLoop);\n          return;\n        }\n\n        applyTouchMove(speed.x * 30, speed.y * 30);\n\n        speed.x *= 0.8;\n        speed.y *= 0.8;\n      }, 10);\n    }\n  }\n\n  if (env.supportsTouch) {\n    i.event.bind(element, 'touchstart', touchStart);\n    i.event.bind(element, 'touchmove', touchMove);\n    i.event.bind(element, 'touchend', touchEnd);\n  } else if (env.supportsIePointer) {\n    if (window.PointerEvent) {\n      i.event.bind(element, 'pointerdown', touchStart);\n      i.event.bind(element, 'pointermove', touchMove);\n      i.event.bind(element, 'pointerup', touchEnd);\n    } else if (window.MSPointerEvent) {\n      i.event.bind(element, 'MSPointerDown', touchStart);\n      i.event.bind(element, 'MSPointerMove', touchMove);\n      i.event.bind(element, 'MSPointerUp', touchEnd);\n    }\n  }\n}\n"], "names": ["Math", "abs", "floor", "get", "element", "getComputedStyle", "set", "obj", "const", "key", "let", "val", "style", "div", "className", "document", "createElement", "matches", "query", "elMatches", "Error", "call", "remove", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "query<PERSON><PERSON><PERSON><PERSON>", "selector", "Array", "prototype", "filter", "children", "child", "addScrollingClass", "i", "x", "classList", "cls", "state", "scrolling", "contains", "clearTimeout", "scrollingClassTimeout", "add", "removeScrollingClass", "setTimeout", "isAlive", "settings", "scrollingT<PERSON>eshold", "setScrollingClassInstantly", "createEvent", "name", "window", "CustomEvent", "evt", "initCustomEvent", "axis", "diff", "useScrollingClass", "forceFireReachEvent", "fields", "processScrollDiff", "ref", "reach", "y", "scrollTop", "contentHeight", "containerHeight", "dispatchEvent", "up", "down", "toInt", "parseInt", "isEditable", "el", "DOM.matches", "outerWidth", "styles", "CSS.get", "width", "paddingLeft", "paddingRight", "borderLeftWidth", "borderRightWidth", "round", "roundedScrollTop", "rect", "getBoundingClientRect", "containerWidth", "height", "contentWidth", "scrollWidth", "scrollHeight", "scrollbarXRail", "DOM.query<PERSON><PERSON><PERSON>n", "rail", "for<PERSON>ach", "DOM.remove", "append<PERSON><PERSON><PERSON>", "scrollbarYRail", "suppressScrollX", "scrollXMarginOffset", "scrollbarXActive", "railXWidth", "railXMarginWidth", "railXRatio", "scrollbarXWidth", "getThumbSize", "scrollbarXLeft", "negativeScrollAdjustment", "scrollLeft", "suppressScrollY", "scrollYMarginOffset", "scrollbarYActive", "railYHeight", "railYMarginHeight", "railYRatio", "scrollbarYHeight", "scrollbarYTop", "updateCss", "active", "isRtl", "thumbSize", "min", "max", "minScrollbar<PERSON><PERSON>th", "maxS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "xRailOffset", "left", "isScrollbarXUsingBottom", "bottom", "scrollbarXBottom", "top", "scrollbarXTop", "CSS.set", "yRailOffset", "isScrollbarYUsingRight", "right", "scrollbarYRight", "scrollbarYOuterWidth", "scrollbarYLeft", "scrollbarX", "railBorderXWidth", "scrollbarY", "railBorderYWidth", "bindMouseScrollHandler", "mouseMoveHandler", "e", "touches", "pageY", "startingScrollTop", "scrollBy", "startingMousePageY", "updateGeometry", "stopPropagation", "type", "startsWith", "changedTouches", "length", "preventDefault", "mouseUpHandler", "clicking", "event", "unbind", "ownerDocument", "bindMoves", "touchMode", "bind", "once", "Element", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "main", "rtl", "thumb", "consuming", "focus", "EventElement", "handlers", "eventName", "handler", "push", "addEventListener", "target", "this", "removeEventListener", "unbindAll", "prototypeAccessors", "isEmpty", "Object", "keys", "every", "EventManager", "eventElements", "eventElement", "ee", "splice", "indexOf", "once<PERSON><PERSON><PERSON>", "env", "isWebKit", "documentElement", "supportsTouch", "navigator", "maxTouchPoints", "DocumentTouch", "supportsIePointer", "msMaxTouchPoints", "isChrome", "test", "userAgent", "defaultSettings", "swipeEasing", "useBothWheelAxes", "wheelPropagation", "wheelSpeed", "positionTop", "pageYOffset", "direction", "positionLeft", "pageX", "pageXOffset", "shouldPreventDefault", "deltaX", "deltaY", "elementHovered", "scrollbarFocused", "isDefaultPrevented", "defaultPrevented", "activeElement", "tagName", "contentDocument", "shadowRoot", "which", "metaKey", "altKey", "shift<PERSON>ey", "hitsBound", "isTop", "isBottom", "offsetHeight", "isLeft", "isRight", "offsetWidth", "getDeltaFromEvent", "wheelDeltaX", "wheelDeltaY", "deltaMode", "wheelDelta", "shouldBeConsumedByChild", "querySelector", "cursor", "overflowY", "match", "maxScrollTop", "clientHeight", "overflowX", "maxScrollLeft", "clientWidth", "mousewheelHandler", "shouldPrevent", "ctrl<PERSON>ey", "onwheel", "onmousew<PERSON><PERSON>", "magnitudeX", "magnitudeY", "scrollY", "applyTouchMove", "differenceX", "differenceY", "getTouch", "targetTouches", "<PERSON><PERSON><PERSON><PERSON>", "pointerType", "buttons", "MSPOINTER_TYPE_MOUSE", "touchStart", "touch", "startOffset", "startTime", "Date", "getTime", "easingLoop", "clearInterval", "touchMove", "currentOffset", "currentTime", "timeGap", "speed", "touchEnd", "setInterval", "isInitialized", "PointerEvent", "MSPointerEvent", "PerfectScrollbar", "userSettings", "nodeName", "blur", "isNegativeScroll", "originalScrollLeft", "result", "DOM.div", "setAttribute", "railXStyle", "isNaN", "display", "marginLeft", "marginRight", "railYStyle", "borderTopWidth", "borderBottomWidth", "marginTop", "marginBottom", "handler<PERSON>ame", "lastScrollTop", "lastScrollLeft", "onScroll", "update", "destroy", "removePsClasses", "split", "join"], "mappings": ";;;;kNAsBQA,IAAI,CAACC,MCfcD,IAAI,CAACE,MCPzB,QAASC,CAAAA,CAAT,CAAaC,CAAb,CAAsB,CAC3B,MAAOC,CAAAA,gBAAgB,CAACD,CAAD,CACxB,CAEM,QAASE,CAAAA,CAAT,CAAaF,CAAb,CAAsBG,CAAtB,CAA2B,CAChC,IAAKC,GAAMC,CAAAA,CAAX,GAAkBF,CAAAA,CAAlB,CAAuB,CACrBG,GAAIC,CAAAA,CAAG,CAAGJ,CAAG,CAACE,CAAD,CAAbC,CACmB,QAAf,QAAOC,CAAAA,CAFU,GAGnBA,CAHmB,MAAA,EAKrBP,CAAO,CAACQ,KAAR,CAAcH,CAAd,EAAqBE,CACtB,CACD,MAAOP,CAAAA,ECZF,QAASS,CAAAA,CAAT,CAAaC,CAAb,CAAwB,CAC7BN,GAAMK,CAAAA,CAAG,CAAGE,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAZR,CAEA,MADAK,CAAAA,CAAG,CAACC,SAAJ,CAAgBA,CAChB,CAAOD,CACR,CASM,QAASI,CAAAA,CAAT,CAAiBb,CAAjB,CAA0Bc,CAA1B,CAAiC,CACtC,GAAI,CAACC,CAAL,CACE,KAAM,IAAIC,CAAAA,KAAJ,CAAU,sCAAV,CAAN,CAGF,MAAOD,CAAAA,CAAS,CAACE,IAAV,CAAejB,CAAf,CAAwBc,CAAxB,CACR,CAEM,QAASI,CAAAA,CAAT,CAAgBlB,CAAhB,CAAyB,CAC1BA,CAAO,CAACkB,MADkB,CAE5BlB,CAAO,CAACkB,MAAR,EAF4B,CAIxBlB,CAAO,CAACmB,UAJgB,EAK1BnB,CAAO,CAACmB,UAAR,CAAmBC,WAAnB,CAA+BpB,CAA/B,CAGL,CAEM,QAASqB,CAAAA,CAAT,CAAuBrB,CAAvB,CAAgCsB,CAAhC,CAA0C,CAC/C,MAAOC,CAAAA,KAAK,CAACC,SAAN,CAAgBC,MAAhB,CAAuBR,IAAvB,CAA4BjB,CAAO,CAAC0B,QAApC,UAA8CC,EAAM,OACzDd,CAAAA,CAAO,CAACc,CAAD,CAAQL,CAAR,CAAiB,CADnB,CAGR,CCZM,QAASM,CAAAA,CAAT,CAA2BC,CAA3B,CAA8BC,CAA9B,CAAiC,IAChCC,CAAAA,CAAS,CAAGF,CAAC,CAAC7B,OAAF,CAAU+B,SADU,CAEhCrB,CAAS,CAAGsB,CAAG,CAACC,KAAJ,CAAUC,SAAV,CAAoBJ,CAApB,CAFoB,CAIlCC,CAAS,CAACI,QAAV,CAAmBzB,CAAnB,CAJkC,CAKpC0B,YAAY,CAACC,CAAqB,CAACP,CAAD,CAAtB,CALwB,CAOpCC,CAAS,CAACO,GAAV,CAAc5B,CAAd,CAEH,CAEM,QAAS6B,CAAAA,CAAT,CAA8BV,CAA9B,CAAiCC,CAAjC,CAAoC,CACzCO,CAAqB,CAACP,CAAD,CAArB,CAA2BU,UAAU,WAChC,OAAGX,CAAAA,CAAC,CAACY,OAAF,EAAaZ,CAAC,CAAC7B,OAAF,CAAU+B,SAAV,CAAoBb,MAApB,CAA2Bc,CAAG,CAACC,KAAJ,CAAUC,SAAV,CAAoBJ,CAApB,CAA3B,CAAkD,CADlC,CAEnCD,CAAC,CAACa,QAAF,CAAWC,kBAFwB,CAItC,CAEM,QAASC,CAAAA,CAAT,CAAoCf,CAApC,CAAuCC,CAAvC,CAA0C,CAC/CF,CAAiB,CAACC,CAAD,CAAIC,CAAJ,CAD8B,CAE/CS,CAAoB,CAACV,CAAD,CAAIC,CAAJ,CACrB,CC1CD,QAASe,CAAAA,CAAT,CAAqBC,CAArB,CAA2B,CACzB,GAAkC,UAA9B,QAAOC,CAAAA,MAAM,CAACC,WAAlB,CACE,MAAO,IAAIA,CAAAA,WAAJ,CAAgBF,CAAhB,CAAP,CAEA1C,GAAM6C,CAAAA,CAAG,CAAGtC,QAAQ,CAACkC,WAAT,CAAqB,aAArB,CAAZzC,CAEA,MADA6C,CAAAA,CAAG,CAACC,eAAJ,CAAoBJ,CAApB,cACA,CAAOG,CAEV,CAEc,UAAA,CACbpB,CADa,CAEbsB,CAFa,CAGbC,CAHa,CAIbC,CAJa,CAKbC,CALa,CAMb,WAAA,IAFiB,GAEjB,YAAA,IADmB,GACnB,EACAhD,GAAIiD,CAAAA,CAAJjD,CACA,GAAa,KAAT,GAAA6C,CAAJ,CACEI,CAAM,CAAG,CACP,eADO,CAEP,iBAFO,CAGP,WAHO,CAIP,GAJO,CAKP,IALO,CAMP,MANO,CADX,KASO,IAAa,MAAT,GAAAJ,CAAJ,CACLI,CAAM,CAAG,CACP,cADO,CAEP,gBAFO,CAGP,YAHO,CAIP,GAJO,CAKP,MALO,CAMP,OANO,CADJ,KAUL,MAAM,IAAIvC,CAAAA,KAAJ,CAAU,kCAAV,CAAN,CAGFwC,CAAiB,CAAC3B,CAAD,CAAIuB,CAAJ,CAAUG,CAAV,CAAkBF,CAAlB,CAAqCC,CAArC,CAClB,CAED,QAASE,CAAAA,CAAT,CACE3B,CADF,CAEEuB,CAFF,CAGEK,CAHF,CAIEJ,CAJF,CAKEC,CALF,CAME,WAAA,OAAA,OAAA,OAAA,OAAA,OAAA,WAAA,IAFiB,GAEjB,YAAA,IADmB,GACnB,EACAlD,GAAMJ,CAAAA,CAAO,CAAG6B,CAAC,CAAC7B,OAAlBI;AAGAyB,CAAC,CAAC6B,KAAF,CAAQC,CAAR,EAAa,IAJb,CAOyB,CAArB,CAAA3D,CAAO,CAAC4D,CAAD,CAPX,GAQE/B,CAAC,CAAC6B,KAAF,CAAQC,CAAR,EAAa,OARf,EAYI3D,CAAO,CAAC4D,CAAD,CAAP,CAAqB/B,CAAC,CAACgC,CAAD,CAAD,CAAmBhC,CAAC,CAACiC,CAAD,CAApB,CAAwC,CAZjE,GAaEjC,CAAC,CAAC6B,KAAF,CAAQC,CAAR,EAAa,KAbf,EAgBIP,CAhBJ,GAiBEpD,CAAO,CAAC+D,aAAR,CAAsBlB,CAAW,cAAcc,CAAd,CAAjC,CAjBF,CAmBa,CAAP,CAAAP,CAnBN,CAoBIpD,CAAO,CAAC+D,aAAR,CAAsBlB,CAAW,cAAcmB,CAAd,CAAjC,CApBJ,CAqBoB,CAAP,CAAAZ,CArBb,EAsBIpD,CAAO,CAAC+D,aAAR,CAAsBlB,CAAW,cAAcoB,CAAd,CAAjC,CAtBJ,CAyBMZ,CAzBN,EA0BIT,CAA0B,CAACf,CAAD,CAAI8B,CAAJ,CA1B9B,EA8BI9B,CAAC,CAAC6B,KAAF,CAAQC,CAAR,IAAeP,CAAI,EAAIE,CAAvB,CA9BJ,EA+BEtD,CAAO,CAAC+D,aAAR,CAAsBlB,CAAW,OAAOc,YAAW9B,CAAC,CAAC6B,KAAF,CAAQC,CAAR,CAAlB,CAAjC,CAEH,CCjFM,QAASO,CAAAA,CAAT,CAAepC,CAAf,CAAkB,CACvB,MAAOqC,CAAAA,QAAQ,CAACrC,CAAD,CAAI,EAAJ,CAAR,EAAmB,CAC3B,CAEM,QAASsC,CAAAA,CAAT,CAAoBC,CAApB,CAAwB,CAC7B,MACEC,CAAAA,CAAW,CAACD,CAAD,CAAK,yBAAL,CAAXC,EACAA,CAAW,CAACD,CAAD,CAAK,0BAAL,CADXC,EAEAA,CAAW,CAACD,CAAD,CAAK,4BAAL,CAFXC,EAGAA,CAAW,CAACD,CAAD,CAAK,0BAAL,CAEd,CAEM,QAASE,CAAAA,CAAT,CAAoBvE,CAApB,CAA6B,CAClCI,GAAMoE,CAAAA,CAAM,CAAGC,CAAO,CAACzE,CAAD,CAAtBI,CACA,MACE8D,CAAAA,CAAK,CAACM,CAAM,CAACE,KAAR,CAAL,CACAR,CAAK,CAACM,CAAM,CAACG,WAAR,CADL,CAEAT,CAAK,CAACM,CAAM,CAACI,YAAR,CAFL,CAGAV,CAAK,CAACM,CAAM,CAACK,eAAR,CAHL,CAIAX,CAAK,CAACM,CAAM,CAACM,gBAAR,CAER,CLpBc,UAAA,CAASjD,CAAT,CAAY,OAKNjC,IAAI,CAACmF,KALC,CACnB/E,CAAO,CAAG6B,CAAC,CAAC7B,OADO,CAEnBgF,CAAgB,CAAG,EAAWhF,CAAO,CAAC4D,SAAnB,CAFA,CAGnBqB,CAAI,CAAGjF,CAAO,CAACkF,qBAAR,EAHY,CAKzBrD,CAAC,CAACsD,cAAF,CAAmB,EAAWF,CAAI,CAACP,KAAhB,CALM,CAMzB7C,CAAC,CAACiC,eAAF,CAAoB,EAAWmB,CAAI,CAACG,MAAhB,CANK,CAQzBvD,CAAC,CAACwD,YAAF,CAAiBrF,CAAO,CAACsF,WARA,CASzBzD,CAAC,CAACgC,aAAF,CAAkB7D,CAAO,CAACuF,YATD,CAWpBvF,CAAO,CAACmC,QAAR,CAAiBN,CAAC,CAAC2D,cAAnB,CAXoB,GAavBC,CAAiB,CAACzF,CAAD,CAAUgC,CAAG,CAAChC,OAAJ,CAAY0F,IAAZ,CAAiB,GAAjB,CAAV,CAAjBD,CAAkDE,OAAlDF,UAA0DpB,EAAG,OAC3DuB,CAAAA,CAAU,CAACvB,CAAD,CAAI,CADhBoB,CAbuB,CAgBvBzF,CAAO,CAAC6F,WAAR,CAAoBhE,CAAC,CAAC2D,cAAtB,CAhBuB,EAkBpBxF,CAAO,CAACmC,QAAR,CAAiBN,CAAC,CAACiE,cAAnB,CAlBoB,GAoBvBL,CAAiB,CAACzF,CAAD,CAAUgC,CAAG,CAAChC,OAAJ,CAAY0F,IAAZ,CAAiB,GAAjB,CAAV,CAAjBD,CAAkDE,OAAlDF,UAA0DpB,EAAG,OAC3DuB,CAAAA,CAAU,CAACvB,CAAD,CAAI,CADhBoB,CApBuB,CAuBvBzF,CAAO,CAAC6F,WAAR,CAAoBhE,CAAC,CAACiE,cAAtB,CAvBuB,EA2BvB,CAACjE,CAAC,CAACa,QAAF,CAAWqD,eAAZ,EACAlE,CAAC,CAACsD,cAAF,CAAmBtD,CAAC,CAACa,QAAF,CAAWsD,mBAA9B,CAAoDnE,CAAC,CAACwD,YA5B/B,EA8BvBxD,CAAC,CAACoE,gBAAF,GA9BuB,CA+BvBpE,CAAC,CAACqE,UAAF,CAAerE,CAAC,CAACsD,cAAF,CAAmBtD,CAAC,CAACsE,gBA/Bb,CAgCvBtE,CAAC,CAACuE,UAAF,CAAevE,CAAC,CAACsD,cAAF,CAAmBtD,CAAC,CAACqE,UAhCb,CAiCvBrE,CAAC,CAACwE,eAAF,CAAoBC,CAAY,CAC9BzE,CAD8B,CAE9BqC,CAAK,CAAErC,CAAC,CAACqE,UAAF,CAAerE,CAAC,CAACsD,cAAlB,CAAoCtD,CAAC,CAACwD,YAAvC,CAFyB,CAjCT,CAqCvBxD,CAAC,CAAC0E,cAAF,CAAmBrC,CAAK,CACrB,CAACrC,CAAC,CAAC2E,wBAAF,CAA6BxG,CAAO,CAACyG,UAAtC,GACE5E,CAAC,CAACqE,UAAF,CAAerE,CAAC,CAACwE,eADnB,CAAD,EAEGxE,CAAC,CAACwD,YAAF,CAAiBxD,CAAC,CAACsD,cAFtB,CADsB,CArCD,EA2CvBtD,CAAC,CAACoE,gBAAF,GA3CuB,CA+CvB,CAACpE,CAAC,CAACa,QAAF,CAAWgE,eAAZ,EACA7E,CAAC,CAACiC,eAAF,CAAoBjC,CAAC,CAACa,QAAF,CAAWiE,mBAA/B,CAAqD9E,CAAC,CAACgC,aAhDhC,EAkDvBhC,CAAC,CAAC+E,gBAAF,GAlDuB,CAmDvB/E,CAAC,CAACgF,WAAF,CAAgBhF,CAAC,CAACiC,eAAF,CAAoBjC,CAAC,CAACiF,iBAnDf,CAoDvBjF,CAAC,CAACkF,UAAF,CAAelF,CAAC,CAACiC,eAAF,CAAoBjC,CAAC,CAACgF,WApDd,CAqDvBhF,CAAC,CAACmF,gBAAF,CAAqBV,CAAY,CAC/BzE,CAD+B,CAE/BqC,CAAK,CAAErC,CAAC,CAACgF,WAAF,CAAgBhF,CAAC,CAACiC,eAAnB,CAAsCjC,CAAC,CAACgC,aAAzC,CAF0B,CArDV,CAyDvBhC,CAAC,CAACoF,aAAF,CAAkB/C,CAAK,CACpBc,CAAgB,EAAInD,CAAC,CAACgF,WAAF,CAAgBhF,CAAC,CAACmF,gBAAtB,CAAjB,EACGnF,CAAC,CAACgC,aAAF,CAAkBhC,CAAC,CAACiC,eADvB,CADqB,CAzDA,EA8DvBjC,CAAC,CAAC+E,gBAAF,GA9DuB,CAiErB/E,CAAC,CAAC0E,cAAF,EAAoB1E,CAAC,CAACqE,UAAF,CAAerE,CAAC,CAACwE,eAjEhB,GAkEvBxE,CAAC,CAAC0E,cAAF,CAAmB1E,CAAC,CAACqE,UAAF,CAAerE,CAAC,CAACwE,eAlEb,EAoErBxE,CAAC,CAACoF,aAAF,EAAmBpF,CAAC,CAACgF,WAAF,CAAgBhF,CAAC,CAACmF,gBApEhB,GAqEvBnF,CAAC,CAACoF,aAAF,CAAkBpF,CAAC,CAACgF,WAAF,CAAgBhF,CAAC,CAACmF,gBArEb,EAwEzBE,CAAS,CAAClH,CAAD,CAAU6B,CAAV,CAxEgB,CA0ErBA,CAAC,CAACoE,gBA1EmB,CA2EvBjG,CAAO,CAAC+B,SAAR,CAAkBO,GAAlB,CAAsBN,CAAG,CAACC,KAAJ,CAAUkF,MAAV,CAAiB,GAAjB,CAAtB,CA3EuB,EA6EvBnH,CAAO,CAAC+B,SAAR,CAAkBb,MAAlB,CAAyBc,CAAG,CAACC,KAAJ,CAAUkF,MAAV,CAAiB,GAAjB,CAAzB,CA7EuB,CA8EvBtF,CAAC,CAACwE,eAAF,CAAoB,CA9EG,CA+EvBxE,CAAC,CAAC0E,cAAF,CAAmB,CA/EI,CAgFvBvG,CAAO,CAACyG,UAAR,CAAqB,KAAA5E,CAAC,CAACuF,KAAF,CAAmBvF,CAAC,CAACwD,YAArB,CAAoC,CAhFlC,EAkFrBxD,CAAC,CAAC+E,gBAlFmB,CAmFvB5G,CAAO,CAAC+B,SAAR,CAAkBO,GAAlB,CAAsBN,CAAG,CAACC,KAAJ,CAAUkF,MAAV,CAAiB,GAAjB,CAAtB,CAnFuB,EAqFvBnH,CAAO,CAAC+B,SAAR,CAAkBb,MAAlB,CAAyBc,CAAG,CAACC,KAAJ,CAAUkF,MAAV,CAAiB,GAAjB,CAAzB,CArFuB,CAsFvBtF,CAAC,CAACmF,gBAAF,CAAqB,CAtFE,CAuFvBnF,CAAC,CAACoF,aAAF,CAAkB,CAvFK,CAwFvBjH,CAAO,CAAC4D,SAAR,CAAoB,CAxFG,CA0F1B,CAED,QAAS0C,CAAAA,CAAT,CAAsBzE,CAAtB,CAAyBwF,CAAzB,CAAoC,OAKpBzH,IAAI,CAAC0H,GALe,GAEpB1H,IAAI,CAAC2H,GAFe,CAOlC,MANI1F,CAAAA,CAAC,CAACa,QAAF,CAAW8E,kBAMf,GALEH,CAAS,CAAG,EAASA,CAAT,CAAoBxF,CAAC,CAACa,QAAF,CAAW8E,kBAA/B,CAKd,EAHI3F,CAAC,CAACa,QAAF,CAAW+E,kBAGf,GAFEJ,CAAS,CAAG,EAASA,CAAT,CAAoBxF,CAAC,CAACa,QAAF,CAAW+E,kBAA/B,CAEd,EAAOJ,CACR,CAED,QAASH,CAAAA,CAAT,CAAmBlH,CAAnB,CAA4B6B,CAA5B,CAA+B,IACvB6F,CAAAA,CAAW,CAAG,CAAEhD,KAAK,CAAE7C,CAAC,CAACqE,UAAX,CADS,CAEvBlB,CAAgB,CAAG,EAAWhF,CAAO,CAAC4D,SAAnB,CAFI,CAK3B8D,CAAW,CAACC,IALe,CAIzB9F,CAAC,CAACuF,KAJuB,CAMzBvF,CAAC,CAAC2E,wBAAF,CACAxG,CAAO,CAACyG,UADR,CAEA5E,CAAC,CAACsD,cAFF,CAGAtD,CAAC,CAACwD,YATuB,CAWRrF,CAAO,CAACyG,UAXA,CAazB5E,CAAC,CAAC+F,uBAbuB,CAc3BF,CAAW,CAACG,MAAZ,CAAqBhG,CAAC,CAACiG,gBAAF,CAAqB9C,CAdf,CAgB3B0C,CAAW,CAACK,GAAZ,CAAkBlG,CAAC,CAACmG,aAAF,CAAkBhD,CAhBT,CAkB7BiD,CAAO,CAACpG,CAAC,CAAC2D,cAAH,CAAmBkC,CAAnB,CAlBsB,CAoB7BtH,GAAM8H,CAAAA,CAAW,CAAG,CAAEH,GAAG,CAAE/C,CAAP,CAAyBI,MAAM,CAAEvD,CAAC,CAACgF,WAAnC,CAApBzG,CACIyB,CAAC,CAACsG,sBArBuB,CAsBvBtG,CAAC,CAACuF,KAtBqB,CAuBzBc,CAAW,CAACE,KAAZ,CACEvG,CAAC,CAACwD,YAAF,EACCxD,CAAC,CAAC2E,wBAAF,CAA6BxG,CAAO,CAACyG,UADtC,EAEA5E,CAAC,CAACwG,eAFF,CAGAxG,CAAC,CAACyG,oBAHF,CAIA,CA5BuB,CA8BzBJ,CAAW,CAACE,KAAZ,CAAoBvG,CAAC,CAACwG,eAAF,CAAoBrI,CAAO,CAACyG,UA9BvB,CAiCvB5E,CAAC,CAACuF,KAjCqB,CAkCzBc,CAAW,CAACP,IAAZ,CACE9F,CAAC,CAAC2E,wBAAF,CACAxG,CAAO,CAACyG,UADR,CAEmB,CAAnB,CAAA5E,CAAC,CAACsD,cAFF,CAGAtD,CAAC,CAACwD,YAHF,CAIAxD,CAAC,CAAC0G,cAJF,CAKA1G,CAAC,CAACyG,oBAxCqB,CA0CzBJ,CAAW,CAACP,IAAZ,CAAmB9F,CAAC,CAAC0G,cAAF,CAAmBvI,CAAO,CAACyG,UA1CrB,CA6C7BwB,CAAO,CAACpG,CAAC,CAACiE,cAAH,CAAmBoC,CAAnB,CA7CsB,CA+C7BD,CAAO,CAACpG,CAAC,CAAC2G,UAAH,CAAe,CACpBb,IAAI,CAAE9F,CAAC,CAAC0E,cADY,CAEpB7B,KAAK,CAAE7C,CAAC,CAACwE,eAAF,CAAoBxE,CAAC,CAAC4G,gBAFT,CAAf,CA/CsB,CAmD7BR,CAAO,CAACpG,CAAC,CAAC6G,UAAH,CAAe,CACpBX,GAAG,CAAElG,CAAC,CAACoF,aADa,CAEpB7B,MAAM,CAAEvD,CAAC,CAACmF,gBAAF,CAAqBnF,CAAC,CAAC8G,gBAFX,CAAf,CAIR,CMhID,QAASC,CAAAA,CAAT,CACE/G,CADF,CAEE4B,CAFF,CAaE,CAOA,QAASoF,CAAAA,CAAT,CAA0BC,CAA1B,CAA6B,CACvBA,CAAC,CAACC,OAAF,EAAaD,CAAC,CAACC,OAAF,CAAU,CAAV,CADU,GAEzBD,CAAC,CAACE,CAAD,CAAD,CAAWF,CAAC,CAACC,OAAF,CAAU,CAAV,EAAaC,KAFC,EAI3BhJ,CAAO,CAAC4D,CAAD,CAAP,CACEqF,CAAiB,CAAGC,CAAQ,EAAIJ,CAAC,CAACE,CAAD,CAAD,CAAWG,CAAf,CALH,CAM3BvH,CAAiB,CAACC,CAAD,CAAI8B,CAAJ,CANU,CAO3ByF,CAAc,CAACvH,CAAD,CAPa,CAS3BiH,CAAC,CAACO,eAAF,EAT2B,CAUvBP,CAAC,CAACQ,IAAF,CAAOC,UAAP,CAAkB,OAAlB,GAAwD,CAA1B,CAAAT,CAAC,CAACU,cAAF,CAAiBC,MAVxB,EAWzBX,CAAC,CAACY,cAAF,EAEH,CAED,QAASC,CAAAA,CAAT,EAA0B,CACxBpH,CAAoB,CAACV,CAAD,CAAI8B,CAAJ,CADI,CAExB9B,CAAC,CAACiE,CAAD,CAAD,CAAkB/D,SAAlB,CAA4Bb,MAA5B,CAAmCc,CAAG,CAACC,KAAJ,CAAU2H,QAA7C,CAFwB,CAGxB/H,CAAC,CAACgI,KAAF,CAAQC,MAAR,CAAejI,CAAC,CAACkI,aAAjB,CAAgC,WAAhC,CAA6ClB,CAA7C,CACD,CAED,QAASmB,CAAAA,CAAT,CAAmBlB,CAAnB,CAAsBmB,CAAtB,CAAiC,CAC/BhB,CAAiB,CAAGjJ,CAAO,CAAC4D,CAAD,CADI,CAE3BqG,CAAS,EAAInB,CAAC,CAACC,OAFY,GAG7BD,CAAC,CAACE,CAAD,CAAD,CAAWF,CAAC,CAACC,OAAF,CAAU,CAAV,EAAaC,KAHK,EAK/BG,CAAkB,CAAGL,CAAC,CAACE,CAAD,CALS,CAM/BE,CAAQ,CACN,CAACrH,CAAC,CAACgC,CAAD,CAAD,CAAmBhC,CAAC,CAACiC,CAAD,CAArB,GACCjC,CAAC,CAACgF,CAAD,CAAD,CAAiBhF,CAAC,CAACmF,CAAD,CADnB,CAP6B,CAS1BiD,CAT0B,CAc7BpI,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAarI,CAAC,CAACkI,aAAf,CAA8B,WAA9B,CAA2ClB,CAA3C,CAd6B,EAU7BhH,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAarI,CAAC,CAACkI,aAAf,CAA8B,WAA9B,CAA2ClB,CAA3C,CAV6B,CAW7BhH,CAAC,CAACgI,KAAF,CAAQM,IAAR,CAAatI,CAAC,CAACkI,aAAf,CAA8B,SAA9B,CAAyCJ,CAAzC,CAX6B,CAY7Bb,CAAC,CAACY,cAAF,EAZ6B,EAiB/B7H,CAAC,CAACiE,CAAD,CAAD,CAAkB/D,SAAlB,CAA4BO,GAA5B,CAAgCN,CAAG,CAACC,KAAJ,CAAU2H,QAA1C,CAjB+B,CAmB/Bd,CAAC,CAACO,eAAF,EACD,CAhDD,UAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CACMrJ,CAAO,CAAG6B,CAAC,CAAC7B,OADlB,CAGIiJ,CAAiB,CAAG,IAHxB,CAIIE,CAAkB,CAAG,IAJzB,CAKID,CAAQ,CAAG,IALf,CAkDArH,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAarI,CAAC,CAAC6G,CAAD,CAAd,CAA4B,WAA5B,UAAyCI,EAAE,CACzCkB,CAAS,CAAClB,CAAD,CACV,CAFD,CAlDA,CAqDAjH,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAarI,CAAC,CAAC6G,CAAD,CAAd,CAA4B,YAA5B,UAA0CI,EAAE,CAC1CkB,CAAS,CAAClB,CAAD,IACV,CAFD,CAGD,IJjGK/H,CAAAA,CAAS,CACM,WAAnB,QAAOqJ,CAAAA,OAAP,GACCA,OAAO,CAAC5I,SAAR,CAAkBX,OAAlB,EACCuJ,OAAO,CAAC5I,SAAR,CAAkB6I,qBADnB,EAECD,OAAO,CAAC5I,SAAR,CAAkB8I,kBAFnB,EAGCF,OAAO,CAAC5I,SAAR,CAAkB+I,iBAJpB,ECPIvI,CAAG,CAAG,CACVwI,IAAI,CAAE,IADI,CAEVC,GAAG,CAAE,SAFK,CAGVzK,OAAO,CAAE,CACP0K,KAAK,UAAE5I,EAAE,oBAAgBA,CAAG,CADrB,CAEP4D,IAAI,UAAE5D,EAAE,mBAAeA,CAAG,CAFnB,CAGP6I,SAAS,CAAE,oBAHJ,CAHC,CAQV1I,KAAK,CAAE,CACL2I,KAAK,CAAE,WADF,CAELhB,QAAQ,CAAE,cAFL,CAGLzC,MAAM,UAAErF,EAAE,qBAAiBA,CAAG,CAHzB,CAILI,SAAS,UAAEJ,EAAE,wBAAoBA,CAAG,CAJ/B,CARG,EAqBNO,CAAqB,CAAG,CAAEP,CAAC,CAAE,IAAL,CAAW6B,CAAC,CAAE,IAAd,EIrBxBkH,CAAY,CAChB,SAAY7K,CAAZ,CAAqB,CACnB,KAAKA,OAAL,CAAeA,CADI,CAEnB,KAAK8K,QAAL,CAAgB,kCAGpBD,WAAA,CAAEX,IAAF,UAAOa,EAAWC,EAAS,CACiB,WAApC,QAAO,MAAKF,QAAL,CAAcC,CAAd,CADY,GAEvB,KAAOD,QAAP,CAAgBC,CAAhB,EAA6B,EAFN,EAIzB,KAAOD,QAAP,CAAgBC,CAAhB,EAA2BE,IAA3B,CAAgCD,CAAhC,CAJyB,CAKvB,KAAKhL,OAAL,CAAakL,gBAAb,CAA8BH,CAA9B,CAAyCC,CAAzC,MAGJH,WAAA,CAAEf,MAAF,UAASiB,EAAWI,EAAQ,YACxB,KAAKL,QAAL,CAAcC,CAAd,EAA2B,KAAKD,QAAL,CAAcC,CAAd,EAAyBtJ,MAAzB,UAAgCuJ,EAAQ,UAC7DG,CAAM,EAAIH,CAAO,GAAKG,CADuC,IAIjEC,CAAI,CAACpL,OAALoL,CAAaC,mBAAbD,CAAiCL,CAAjCK,CAA4CJ,CAA5CI,IAJiE,IAMlE,CAN0B,GAS/BP,WAAA,CAAES,SAAF,WAAc,CACZ,IAAOlL,GAAM0C,CAAAA,CAAb,GAAqB,MAAKgI,QAA1B,CACI,KAAKhB,MAAL,CAAYhH,CAAZ,GAINyI,EAAMC,OAAN,IAAA,WAAgB,YACd,MAASC,CAAAA,MAAM,CAACC,IAAP,CAAY,KAAKZ,QAAjB,EAA2Ba,KAA3B,CACP,SAAEtL,CAAF,CAAM,OAAiC,EAA9B+K,GAAAA,CAAI,CAACN,QAALM,CAAc/K,CAAd+K,EAAmB3B,MAAY,CADjC,CAGR,yCAGY,GAAMmC,CAAAA,CAAY,CAC/B,UAAc,CACZ,KAAKC,aAAL,CAAqB,GAFV,CAKfD,WAAA,CAAEE,YAAF,UAAe9L,EAAS,CACtB,GAAM+L,CAAAA,CAAE,CAAG,KAAKF,aAAL,CAAmBpK,MAAnB,UAA0BsK,EAAG,OAAGA,CAAAA,CAAE,CAAC/L,OAAH,GAAeA,CAAO,CAAtD,EAAwD,CAAxD,CAAX,CAKA,MAJO+L,CAAAA,CAIP,GAHIA,CAAE,CAAG,GAAIlB,CAAAA,CAAJ,CAAiB7K,CAAjB,CAGT,CAFE,KAAO6L,aAAP,CAAqBZ,IAArB,CAA0Bc,CAA1B,CAEF,EAASA,GAGXH,WAAA,CAAE1B,IAAF,UAAOlK,EAAS+K,EAAWC,EAAS,CAChC,KAAKc,YAAL,CAAkB9L,CAAlB,EAA2BkK,IAA3B,CAAgCa,CAAhC,CAA2CC,CAA3C,GAGJY,WAAA,CAAE9B,MAAF,UAAS9J,EAAS+K,EAAWC,EAAS,CACpC,GAAQe,CAAAA,CAAE,CAAG,KAAKD,YAAL,CAAkB9L,CAAlB,CAAb,CACA+L,CAAI,CAACjC,MAAL,CAAYiB,CAAZ,CAAuBC,CAAvB,CAFoC,CAI9Be,CAAE,CAACP,OAJ2B,EAMhC,KAAKK,aAAL,CAAmBG,MAAnB,CAA0B,KAAKH,aAAL,CAAmBI,OAAnB,CAA2BF,CAA3B,CAA1B,CAA0D,CAA1D,GAINH,WAAA,CAAEN,SAAF,WAAc,CACV,KAAKO,aAAL,CAAmBlG,OAAnB,UAA2BmD,EAAE,OAAGA,CAAAA,CAAC,CAACwC,SAAF,EAAa,CAA7C,CADU,CAEV,KAAKO,aAAL,CAAqB,IAGzBD,WAAA,CAAEzB,IAAF,UAAOnK,EAAS+K,EAAWC,EAAS,IAC1Be,CAAAA,CAAE,CAAG,KAAKD,YAAL,CAAkB9L,CAAlB,CADqB,CAE1BkM,CAAW,UAAGjJ,EAAI,CACxB8I,CAAI,CAACjC,MAAL,CAAYiB,CAAZ,CAAuBmB,CAAvB,CADwB,CAEtBlB,CAAO,CAAC/H,CAAD,CACR,CAL+B,CAMlC8I,CAAI,CAAC7B,IAAL,CAAUa,CAAV,CAAqBmB,CAArB,CACC,KFlDUC,CAAAA,CAAG,CAAG,CACjBC,QAAQ,CACc,WAApB,QAAOzL,CAAAA,QAAP,EACA,oBAAsBA,CAAAA,QAAQ,CAAC0L,eAAT,CAAyB7L,KAHhC,CAIjB8L,aAAa,CACO,WAAlB,QAAOvJ,CAAAA,MAAP,GACC,gBAAkBA,CAAAA,MAAlB,EACE,kBAAoBA,CAAAA,MAAM,CAACwJ,SAA3B,EACmC,CAAlC,CAAAxJ,MAAM,CAACwJ,SAAP,CAAiBC,cAFpB,EAGEzJ,MAAM,CAAC0J,aAAP,EAAwB9L,QAAQ,WAAYoC,CAAAA,MAAM,CAAC0J,aAJtD,CALe,CAUjBC,iBAAiB,CACM,WAArB,QAAOH,CAAAA,SAAP,EAAoCA,SAAS,CAACI,gBAX/B,CAYjBC,QAAQ,CACe,WAArB,QAAOL,CAAAA,SAAP,EACA,UAAUM,IAAV,CAAeN,SAAS,EAAIA,SAAS,CAACO,SAAtC,CAde,EGbbC,CAAe,WAAM,OAAI,CAC7BjC,QAAQ,CAAE,CAAC,YAAD,CAAe,YAAf,CAA6B,UAA7B,CAAyC,OAAzC,CAAkD,OAAlD,CADmB,CAE7BrD,kBAAkB,CAAE,IAFS,CAG7BD,kBAAkB,CAAE,IAHS,CAI7B7E,kBAAkB,CAAE,GAJS,CAK7BqD,mBAAmB,CAAE,CALQ,CAM7BW,mBAAmB,CAAE,CANQ,CAO7BZ,eAAe,GAPc,CAQ7BW,eAAe,GARc,CAS7BsG,WAAW,GATkB,CAU7BC,gBAAgB,GAVa,CAW7BC,gBAAgB,GAXa,CAY7BC,UAAU,CAAE,CAZiB,CAa7B,EAEIrC,CAAQ,CAAG,CACf,aC5Ba,SAASjJ,CAAT,CAAY,CACTA,CAAC,CAAC7B,OADO,CAGzB6B,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAarI,CAAC,CAAC6G,UAAf,CAA2B,WAA3B,UAAwCI,EAAE,OAAGA,CAAAA,CAAC,CAACO,eAAF,EAAmB,CAAhE,CAHyB,CAIzBxH,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAarI,CAAC,CAACiE,cAAf,CAA+B,WAA/B,UAA4CgD,EAAE,IACtCsE,CAAAA,CAAW,CACftE,CAAC,CAACE,KAAF,CACAjG,MAAM,CAACsK,WADP,CAEAxL,CAAC,CAACiE,cAAF,CAAiBZ,qBAAjB,GAAyC6C,GAJC,CAKtCuF,CAAS,CAAGF,CAAW,CAAGvL,CAAC,CAACoF,aAAhB,CAAgC,CAAhC,CAAoC,CAAC,CALX,CAO5CpF,CAAC,CAAC7B,OAAF,CAAU4D,SAAV,EAAuB0J,CAAS,CAAGzL,CAAC,CAACiC,eAPO,CAQ5CsF,CAAc,CAACvH,CAAD,CAR8B,CAU5CiH,CAAC,CAACO,eAAF,EACD,CAXD,CAJyB,CAiBzBxH,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAarI,CAAC,CAAC2G,UAAf,CAA2B,WAA3B,UAAwCM,EAAE,OAAGA,CAAAA,CAAC,CAACO,eAAF,EAAmB,CAAhE,CAjByB,CAkBzBxH,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAarI,CAAC,CAAC2D,cAAf,CAA+B,WAA/B,UAA4CsD,EAAE,IACtCyE,CAAAA,CAAY,CAChBzE,CAAC,CAAC0E,KAAF,CACAzK,MAAM,CAAC0K,WADP,CAEA5L,CAAC,CAAC2D,cAAF,CAAiBN,qBAAjB,GAAyCyC,IAJC,CAKtC2F,CAAS,CAAGC,CAAY,CAAG1L,CAAC,CAAC0E,cAAjB,CAAkC,CAAlC,CAAsC,CAAC,CALb,CAO5C1E,CAAC,CAAC7B,OAAF,CAAUyG,UAAV,EAAwB6G,CAAS,CAAGzL,CAAC,CAACsD,cAPM,CAQ5CiE,CAAc,CAACvH,CAAD,CAR8B,CAU5CiH,CAAC,CAACO,eAAF,EACD,CAXD,CAYD,CDHgB,CAEf,aFtBa,SAASxH,CAAT,CAAY,CACzB+G,CAAsB,CAAC/G,CAAD,CAAI,CACxB,gBADwB,CAExB,cAFwB,CAGxB,OAHwB,CAIxB,YAJwB,CAKxB,YALwB,CAMxB,iBANwB,CAOxB,YAPwB,CAQxB,GARwB,CASxB,gBATwB,CAAJ,CADG,CAYzB+G,CAAsB,CAAC/G,CAAD,CAAI,CACxB,iBADwB,CAExB,eAFwB,CAGxB,OAHwB,CAIxB,aAJwB,CAKxB,YALwB,CAMxB,kBANwB,CAOxB,WAPwB,CAQxB,GARwB,CASxB,gBATwB,CAAJ,CAWvB,CEHgB,UEzBF,SAASA,CAAT,CAAY,CAOzB,QAAS6L,CAAAA,CAAT,CAA8BC,CAA9B,CAAsCC,CAAtC,CAA8C,CAC5CxN,GAAMwD,CAAAA,CAAS,CAAG,EAAW5D,CAAO,CAAC4D,SAAnB,CAAlBxD,CACA,GAAe,CAAX,GAAAuN,CAAJ,CAAkB,CAChB,GAAI,CAAC9L,CAAC,CAAC+E,gBAAP,CACE,SAEF,GACiB,CAAd,GAAAhD,CAAS,EAAmB,CAAT,CAAAgK,CAApB,EACChK,CAAS,EAAI/B,CAAC,CAACgC,aAAF,CAAkBhC,CAAC,CAACiC,eAAjC,EAA6D,CAAT,CAAA8J,CAFvD,CAIE,MAAO,CAAC/L,CAAC,CAACa,QAAF,CAAWwK,gBAEtB,CAED9M,GAAMqG,CAAAA,CAAU,CAAGzG,CAAO,CAACyG,UAA3BrG,CACA,GAAe,CAAX,GAAAwN,CAAJ,CAAkB,CAChB,GAAI,CAAC/L,CAAC,CAACoE,gBAAP,CACE,SAEF,GACkB,CAAf,GAAAQ,CAAU,EAAmB,CAAT,CAAAkH,CAArB,EACClH,CAAU,EAAI5E,CAAC,CAACwD,YAAF,CAAiBxD,CAAC,CAACsD,cAAjC,EAA4D,CAAT,CAAAwI,CAFtD,CAIE,MAAO,CAAC9L,CAAC,CAACa,QAAF,CAAWwK,gBAEtB,CACD,QACD,CAlCwB,GACnBlN,CAAAA,CAAO,CAAG6B,CAAC,CAAC7B,OADO,CAGnB6N,CAAc,WAAM,OAAGvJ,CAAAA,CAAW,CAACtE,CAAD,CAAU,QAAV,CAAmB,CAHlC,CAInB8N,CAAgB,WAAM,OAC1BxJ,CAAAA,CAAW,CAACzC,CAAC,CAAC2G,UAAH,CAAe,QAAf,CAAXlE,EAAuCA,CAAW,CAACzC,CAAC,CAAC6G,UAAH,CAAe,QAAf,CAAwB,CALnD,CAoCzB7G,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAarI,CAAC,CAACkI,aAAf,CAA8B,SAA9B,UAAyCjB,EAAE,CACzC,KACGA,CAAC,CAACiF,kBAAF,EAAwBjF,CAAC,CAACiF,kBAAF,EAAzB,EACAjF,CAAC,CAACkF,gBAFJ,IAOKH,CAAc,EAAf,EAAsBC,CAAgB,EAP1C,GAWAxN,GAAI2N,CAAAA,CAAa,CAAGtN,QAAQ,CAACsN,aAAT,CAChBtN,QAAQ,CAACsN,aADO,CAEhBpM,CAAC,CAACkI,aAAF,CAAgBkE,aAFpB3N,CAGA,GAAI2N,CAAJ,CAAmB,CACjB,GAA8B,QAA1B,GAAAA,CAAa,CAACC,OAAlB,CACED,CAAa,CAAGA,CAAa,CAACE,eAAd,CAA8BF,aADhD;AAAA,KAISA,CAAa,CAACG,UAJvB,EAKIH,CAAa,CAAGA,CAAa,CAACG,UAAd,CAAyBH,aAAzC,CAGJ,GAAI7J,CAAU,CAAC6J,CAAD,CAAd,CACE,MAEH,CA1BD,GA4BIN,CAAAA,CAAM,CAAG,CA5Bb,CA6BIC,CAAM,CAAG,CA7Bb,CA+BA,OAAQ9E,CAAC,CAACuF,KAAV,EACE,IAAK,GAAL,CAEIV,CAFJ,CACM7E,CAAC,CAACwF,OADR,CAEa,CAACzM,CAAC,CAACwD,YAFhB,CAGayD,CAAC,CAACyF,MAHf,CAIa,CAAC1M,CAAC,CAACsD,cAJhB,CAMa,CAAC,EANd,CAQE,MACF,IAAK,GAAL,CAEIyI,CAFJ,CACM9E,CAAC,CAACwF,OADR,CAEazM,CAAC,CAACgC,aAFf,CAGaiF,CAAC,CAACyF,MAHf,CAIa1M,CAAC,CAACiC,eAJf,CAMa,EANb,CAQE,MACF,IAAK,GAAL,CAEI6J,CAFJ,CACM7E,CAAC,CAACwF,OADR,CAEazM,CAAC,CAACwD,YAFf,CAGayD,CAAC,CAACyF,MAHf,CAIa1M,CAAC,CAACsD,cAJf,CAMa,EANb,CAQE,MACF,IAAK,GAAL,CAEIyI,CAFJ,CACM9E,CAAC,CAACwF,OADR,CAEa,CAACzM,CAAC,CAACgC,aAFhB,CAGaiF,CAAC,CAACyF,MAHf,CAIa,CAAC1M,CAAC,CAACiC,eAJhB,CAMa,CAAC,EANd,CAQE,MACF,IAAK,GAAL,CAEI8J,CAFJ,CACM9E,CAAC,CAAC0F,QADR,CAEa3M,CAAC,CAACiC,eAFf,CAIa,CAACjC,CAAC,CAACiC,eAJhB,CAME,MACF,IAAK,GAAL,CACE8J,CAAM,CAAG/L,CAAC,CAACiC,eADb,CAEE,MACF,IAAK,GAAL,CACE8J,CAAM,CAAG,CAAC/L,CAAC,CAACiC,eADd,CAEE,MACF,IAAK,GAAL,CACE8J,CAAM,CAAG/L,CAAC,CAACgC,aADb,CAEE,MACF,IAAK,GAAL,CACE+J,CAAM,CAAG,CAAC/L,CAAC,CAACgC,aADd,CAEE,MACF,QACE,OAzDJ,CA4DIhC,CAAC,CAACa,QAAF,CAAWqD,eAAX,EAAyC,CAAX,GAAA4H,CA3FlC,EA8FI9L,CAAC,CAACa,QAAF,CAAWgE,eAAX,EAAyC,CAAX,GAAAkH,CA9FlC,GAkGA5N,CAAO,CAAC4D,SAAR,EAAqBgK,CAlGrB,CAmGA5N,CAAO,CAACyG,UAAR,EAAsBkH,CAnGtB,CAoGAvE,CAAc,CAACvH,CAAD,CApGd,CAsGI6L,CAAoB,CAACC,CAAD,CAASC,CAAT,CAtGxB,EAuGE9E,CAAC,CAACY,cAAF,EAvGF,EAyGD,CA1GD,CA2GD,CFtHgB,OTxBF,SAAS7H,CAAT,CAAY,CAKzB,QAAS6L,CAAAA,CAAT,CAA8BC,CAA9B,CAAsCC,CAAtC,CAA8C,IASxCa,CAAAA,CATwC,CACtCzJ,CAAgB,CAAG,EAAWhF,CAAO,CAAC4D,SAAnB,CADmB,CAEtC8K,CAAK,CAAyB,CAAtB,GAAA1O,CAAO,CAAC4D,SAFsB,CAGtC+K,CAAQ,CACZ3J,CAAgB,CAAGhF,CAAO,CAAC4O,YAA3B,GAA4C5O,CAAO,CAACuF,YAJV,CAKtCsJ,CAAM,CAA0B,CAAvB,GAAA7O,CAAO,CAACyG,UALqB,CAMtCqI,CAAO,CACX9O,CAAO,CAACyG,UAAR,CAAqBzG,CAAO,CAAC+O,WAA7B,GAA6C/O,CAAO,CAACsF,WAPX,CAkB5C,MALEmJ,CAAAA,CAKF,CANI,EAASb,CAAT,EAAmB,EAASD,CAAT,CAMvB,CALce,CAAK,EAAIC,CAKvB,CAHcE,CAAM,EAAIC,CAGxB,EAAOL,CAAP,EAAmB,CAAC5M,CAAC,CAACa,QAAF,CAAWwK,gBAChC,CAED,QAAS8B,CAAAA,CAAT,CAA2BlG,CAA3B,CAA8B,IACxB6E,CAAAA,CAAM,CAAG7E,CAAC,CAAC6E,MADa,CAExBC,CAAM,CAAG,CAAC,CAAD,CAAK9E,CAAC,CAAC8E,MAFQ,QAIN,WAAlB,QAAOD,CAAAA,CAAP,EAAmD,WAAlB,QAAOC,CAAAA,CAJhB,IAM1BD,CAAM,CAAI,CAAC,CAAD,CAAK7E,CAAC,CAACmG,WAAR,CAAuB,CANN,CAO1BrB,CAAM,CAAG9E,CAAC,CAACoG,WAAF,CAAgB,CAPC,EAUxBpG,CAAC,CAACqG,SAAF,EAA+B,CAAhB,GAAArG,CAAC,CAACqG,SAVO,GAY1BxB,CAAM,EAAI,EAZgB,CAa1BC,CAAM,EAAI,EAbgB,EAgBxBD,CAAM,GAAKA,CAAX,EAAqBC,CAAM,GAAKA,iBAhBR,GAkB1BD,CAAM,CAAG,CAlBiB,CAmB1BC,CAAM,CAAG9E,CAAC,CAACsG,UAnBe,EAsBxBtG,CAAC,CAAC0F,QAtBsB,CAwBnB,CAAC,CAACZ,CAAF,CAAU,CAACD,CAAX,CAxBmB,CA0BrB,CAACA,CAAD,CAASC,CAAT,CACR,CAED,QAASyB,CAAAA,CAAT,CAAiClE,CAAjC,CAAyCwC,CAAzC,CAAiDC,CAAjD,CAAyD;AAEvD,GAAI,CAACzB,CAAG,CAACC,QAAL,EAAiBpM,CAAO,CAACsP,aAAR,CAAsB,cAAtB,CAArB,CACE,SAGF,GAAI,CAACtP,CAAO,CAACmC,QAAR,CAAiBgJ,CAAjB,CAAL,CACE,SAPqD,IAUvD7K,GAAIiP,CAAAA,CAAM,CAAGpE,CAV0C,CAYhDoE,CAAM,EAAIA,CAAM,GAAKvP,CAZ2B,EAYlB,CACnC,GAAIuP,CAAM,CAACxN,SAAP,CAAiBI,QAAjB,CAA0BH,CAAG,CAAChC,OAAJ,CAAY2K,SAAtC,CAAJ,CACE,SAGFvK,GAAMI,CAAAA,CAAK,CAAGiE,CAAO,CAAC8K,CAAD,CAArBnP;AAGA,GAAIwN,CAAM,EAAIpN,CAAK,CAACgP,SAAN,CAAgBC,KAAhB,CAAsB,eAAtB,CAAd,CAAsD,CACpDrP,GAAMsP,CAAAA,CAAY,CAAGH,CAAM,CAAChK,YAAP,CAAsBgK,CAAM,CAACI,YAAlDvP,CACA,GAAmB,CAAf,CAAAsP,CAAJ,GAEwB,CAAnB,CAAAH,CAAM,CAAC3L,SAAP,EAAiC,CAAT,CAAAgK,CAAzB,EACC2B,CAAM,CAAC3L,SAAP,CAAmB8L,CAAnB,EAA4C,CAAT,CAAA9B,CAHxC,EAKI,QAGL;AAED,GAAID,CAAM,EAAInN,CAAK,CAACoP,SAAN,CAAgBH,KAAhB,CAAsB,eAAtB,CAAd,CAAsD,CACpDrP,GAAMyP,CAAAA,CAAa,CAAGN,CAAM,CAACjK,WAAP,CAAqBiK,CAAM,CAACO,WAAlD1P,CACA,GAAoB,CAAhB,CAAAyP,CAAJ,GAEyB,CAApB,CAAAN,CAAM,CAAC9I,UAAP,EAAkC,CAAT,CAAAkH,CAA1B,EACC4B,CAAM,CAAC9I,UAAP,CAAoBoJ,CAApB,EAA8C,CAAT,CAAAlC,CAH1C,EAKI,QAGL,CAED4B,CAAM,CAAGA,CAAM,CAACpO,UACjB,CAED,QACD,CAED,QAAS4O,CAAAA,CAAT,CAA2BjH,CAA3B,CAA8B,MACN,CAAGkG,CAAiB,CAAClG,CAAD,CADd,OAAA,OAAA,CAG5B,IAAIuG,CAAuB,CAACvG,CAAC,CAACqC,MAAH,CAAWwC,CAAX,CAAmBC,CAAnB,CAA3B,EAIAtN,GAAI0P,CAAAA,CAAa,GAAjB1P,CACKuB,CAAC,CAACa,QAAF,CAAWuK,gBALhB,CAUWpL,CAAC,CAAC+E,gBAAF,EAAsB,CAAC/E,CAAC,CAACoE,gBAVpC,EAaM2H,CAbN,CAcI5N,CAAO,CAAC4D,SAAR,EAAqBgK,CAAM,CAAG/L,CAAC,CAACa,QAAF,CAAWyK,UAd7C,CAgBInN,CAAO,CAAC4D,SAAR,EAAqB+J,CAAM,CAAG9L,CAAC,CAACa,QAAF,CAAWyK,UAhB7C,CAkBE6C,CAAa,GAlBf,EAmBWnO,CAAC,CAACoE,gBAAF,EAAsB,CAACpE,CAAC,CAAC+E,gBAnBpC,GAsBM+G,CAtBN,CAuBI3N,CAAO,CAACyG,UAAR,EAAsBkH,CAAM,CAAG9L,CAAC,CAACa,QAAF,CAAWyK,UAvB9C,CAyBInN,CAAO,CAACyG,UAAR,EAAsBmH,CAAM,CAAG/L,CAAC,CAACa,QAAF,CAAWyK,UAzB9C,CA2BE6C,CAAa,GA3Bf,GAQEhQ,CAAO,CAAC4D,SAAR,EAAqBgK,CAAM,CAAG/L,CAAC,CAACa,QAAF,CAAWyK,UAR3C,CASEnN,CAAO,CAACyG,UAAR,EAAsBkH,CAAM,CAAG9L,CAAC,CAACa,QAAF,CAAWyK,UAT5C,EA8BA/D,CAAc,CAACvH,CAAD,CA9Bd,CAgCAmO,CAAa,CAAGA,CAAa,EAAItC,CAAoB,CAACC,CAAD,CAASC,CAAT,CAhCrD,CAiCIoC,CAAa,EAAI,CAAClH,CAAC,CAACmH,OAjCxB,GAkCEnH,CAAC,CAACO,eAAF,EAlCF,CAmCEP,CAAC,CAACY,cAAF,EAnCF,EAqCD,CAhJDtJ,GAAMJ,CAAAA,CAAO,CAAG6B,CAAC,CAAC7B,OAAlBI,CAkJ8B,WAA1B,QAAO2C,CAAAA,MAAM,CAACmN,OAnJO,CAqJiB,WAA/B,QAAOnN,CAAAA,MAAM,CAACoN,YArJA,EAsJvBtO,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAalK,CAAb,CAAsB,YAAtB,CAAoC+P,CAApC,CAtJuB,CAoJvBlO,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAalK,CAAb,CAAsB,OAAtB,CAA+B+P,CAA/B,CAIH,CShIgB,OGxBF,SAASlO,CAAT,CAAY,CAOzB,QAASmO,CAAAA,CAAT,CAAuBrC,CAAvB,CAA+BC,CAA/B,CAAuC,IAC/BhK,CAAAA,CAAS,CAAG,EAAW5D,CAAO,CAAC4D,SAAnB,CADmB,CAE/B6C,CAAU,CAAGzG,CAAO,CAACyG,UAFU,CAG/B2J,CAAU,CAAG,EAASzC,CAAT,CAHkB,CAI/B0C,CAAU,CAAG,EAASzC,CAAT,CAJkB,CAMrC,GAAIyC,CAAU,CAAGD,CAAjB;AAGE,GACY,CAAT,CAAAxC,CAAM,EAAQhK,CAAS,GAAK/B,CAAC,CAACgC,aAAF,CAAkBhC,CAAC,CAACiC,eAAjD,EACU,CAAT,CAAA8J,CAAM,EAAsB,CAAd,GAAAhK,CAFjB;AAKE,MAA0B,EAAnB,GAAAb,MAAM,CAACuN,OAAP,EAAiC,CAAT,CAAA1C,CAAxB,EAAsCzB,CAAG,CAACS,QAAjD,CARJ,KAUO,IAAIwD,CAAU,CAAGC,CAAjB,GAIO,CAAT,CAAA1C,CAAM,EAAQlH,CAAU,GAAK5E,CAAC,CAACwD,YAAF,CAAiBxD,CAAC,CAACsD,cAAjD,EACU,CAAT,CAAAwI,CAAM,EAAuB,CAAf,GAAAlH,CALZ;AAOH,SAIJ,QACD,CAED,QAAS8J,CAAAA,CAAT,CAAwBC,CAAxB,CAAqCC,CAArC,CAAkD,CAChDzQ,CAAO,CAAC4D,SAAR,EAAqB6M,CAD2B,CAEhDzQ,CAAO,CAACyG,UAAR,EAAsB+J,CAF0B,CAIhDpH,CAAc,CAACvH,CAAD,CACf,CAOD,QAAS6O,CAAAA,CAAT,CAAkB5H,CAAlB,CAAqB,OACfA,CAAAA,CAAC,CAAC6H,aADa,CAEV7H,CAAC,CAAC6H,aAAF,CAAgB,CAAhB,CAFU,CAKV7H,CAEV,CAED,QAAS8H,CAAAA,CAAT,CAAsB9H,CAAtB,CAAyB,SACnBA,CAAC,CAAC+H,WAAF,EAAmC,KAAlB,GAAA/H,CAAC,CAAC+H,WAAnB,EAA0D,CAAd,GAAA/H,CAAC,CAACgI,OAD3B,OAInBhI,CAAC,CAAC6H,aAAF,EAA8C,CAA3B,GAAA7H,CAAC,CAAC6H,aAAF,CAAgBlH,MAJhB,MAQrBX,CAAC,CAAC+H,WAAF,EACkB,OAAlB,GAAA/H,CAAC,CAAC+H,WADF,EAEA/H,CAAC,CAAC+H,WAAF,GAAkB/H,CAAC,CAACiI,oBAVC,EAexB,CAED,QAASC,CAAAA,CAAT,CAAoBlI,CAApB,CAAuB,CACrB,GAAK8H,CAAY,CAAC9H,CAAD,CAAjB,EAIA1I,GAAM6Q,CAAAA,CAAK,CAAGP,CAAQ,CAAC5H,CAAD,CAAtB1I,CAEA8Q,CAAW,CAAC1D,KAAZ,CAAoByD,CAAK,CAACzD,KAN1B,CAOA0D,CAAW,CAAClI,KAAZ,CAAoBiI,CAAK,CAACjI,KAP1B,CASAmI,CAAS,CAAG,GAAIC,CAAAA,IAAJ,GAAWC,OAAX,EATZ,CAWmB,IAAf,GAAAC,CAXJ,EAYEC,aAAa,CAACD,CAAD,CAZf,CAcD,CAED,QAASjC,CAAAA,CAAT,CAAiClE,CAAjC,CAAyCwC,CAAzC,CAAiDC,CAAjD,CAAyD,CACvD,GAAI,CAAC5N,CAAO,CAACmC,QAAR,CAAiBgJ,CAAjB,CAAL,CACE,SAFqD,IAKvD7K,GAAIiP,CAAAA,CAAM,CAAGpE,CAL0C,CAOhDoE,CAAM,EAAIA,CAAM,GAAKvP,CAP2B,EAOlB,CACnC,GAAIuP,CAAM,CAACxN,SAAP,CAAiBI,QAAjB,CAA0BH,CAAG,CAAChC,OAAJ,CAAY2K,SAAtC,CAAJ,CACE,SAGFvK,GAAMI,CAAAA,CAAK,CAAGiE,CAAO,CAAC8K,CAAD,CAArBnP;AAGA,GAAIwN,CAAM,EAAIpN,CAAK,CAACgP,SAAN,CAAgBC,KAAhB,CAAsB,eAAtB,CAAd,CAAsD,CACpDrP,GAAMsP,CAAAA,CAAY,CAAGH,CAAM,CAAChK,YAAP,CAAsBgK,CAAM,CAACI,YAAlDvP,CACA,GAAmB,CAAf,CAAAsP,CAAJ,GAEwB,CAAnB,CAAAH,CAAM,CAAC3L,SAAP,EAAiC,CAAT,CAAAgK,CAAzB,EACC2B,CAAM,CAAC3L,SAAP,CAAmB8L,CAAnB,EAA4C,CAAT,CAAA9B,CAHxC,EAKI,QAGL;AAED,GAAID,CAAM,EAAInN,CAAK,CAACoP,SAAN,CAAgBH,KAAhB,CAAsB,eAAtB,CAAd,CAAsD,CACpDrP,GAAMyP,CAAAA,CAAa,CAAGN,CAAM,CAACjK,WAAP,CAAqBiK,CAAM,CAACO,WAAlD1P,CACA,GAAoB,CAAhB,CAAAyP,CAAJ,GAEyB,CAApB,CAAAN,CAAM,CAAC9I,UAAP,EAAkC,CAAT,CAAAkH,CAA1B,EACC4B,CAAM,CAAC9I,UAAP,CAAoBoJ,CAApB,EAA8C,CAAT,CAAAlC,CAH1C,EAKI,QAGL,CAED4B,CAAM,CAAGA,CAAM,CAACpO,UACjB,CAED,QACD,CAED,QAASqQ,CAAAA,CAAT,CAAmB1I,CAAnB,CAAsB,CACpB,GAAI8H,CAAY,CAAC9H,CAAD,CAAhB,CAAqB,IACbmI,CAAAA,CAAK,CAAGP,CAAQ,CAAC5H,CAAD,CADH,CAGb2I,CAAa,CAAG,CAAEjE,KAAK,CAAEyD,CAAK,CAACzD,KAAf,CAAsBxE,KAAK,CAAEiI,CAAK,CAACjI,KAAnC,CAHH,CAKbwH,CAAW,CAAGiB,CAAa,CAACjE,KAAd,CAAsB0D,CAAW,CAAC1D,KALnC,CAMbiD,CAAW,CAAGgB,CAAa,CAACzI,KAAd,CAAsBkI,CAAW,CAAClI,KANnC,CAQnB,GAAIqG,CAAuB,CAACvG,CAAC,CAACqC,MAAH,CAAWqF,CAAX,CAAwBC,CAAxB,CAA3B,CACE,OAGFF,CAAc,CAACC,CAAD,CAAcC,CAAd,CAZK,CAanBS,CAAW,CAAGO,CAbK,IAebC,CAAAA,CAAW,CAAG,GAAIN,CAAAA,IAAJ,GAAWC,OAAX,EAfD,CAiBbM,CAAO,CAAGD,CAAW,CAAGP,CAjBX,CAkBL,CAAV,CAAAQ,CAlBe,GAmBjBC,CAAK,CAAC9P,CAAN,CAAU0O,CAAW,CAAGmB,CAnBP,CAoBjBC,CAAK,CAACjO,CAAN,CAAU8M,CAAW,CAAGkB,CApBP,CAqBjBR,CAAS,CAAGO,CArBK,EAwBf1B,CAAa,CAACQ,CAAD,CAAcC,CAAd,CAxBE,EAyBjB3H,CAAC,CAACY,cAAF,EAEH,CACF,CACD,QAASmI,CAAAA,CAAT,EAAoB,CACdhQ,CAAC,CAACa,QAAF,CAAWsK,WADG,GAEhBuE,aAAa,CAACD,CAAD,CAFG,CAGhBA,CAAU,CAAGQ,WAAW,CAAC,UAAW,OAC9BjQ,CAAAA,CAAC,CAACkQ,aAD4B,KAEhCR,CAAAA,aAAa,CAACD,CAAD,CAFmB,CAM7BM,CAAK,CAAC9P,CAAP,EAAa8P,CAAK,CAACjO,CANW,CAWV,GAApB,GAASiO,CAAK,CAAC9P,CAAf,GAAgD,GAApB,GAAS8P,CAAK,CAACjO,CAAf,CAXE,KAYhC4N,CAAAA,aAAa,CAACD,CAAD,CAZmB,CAgB7BzP,CAAC,CAAC7B,OAhB2B,MAqBlCuQ,CAAc,CAAW,EAAV,CAAAqB,CAAK,CAAC9P,CAAP,CAAyB,EAAV,CAAA8P,CAAK,CAACjO,CAArB,CArBoB,CAuBlCiO,CAAK,CAAC9P,CAAN,EAAW,EAvBuB,CAwBlC8P,CAAK,CAACjO,CAAN,EAAW,EAxBuB,MAiBhC4N,CAAAA,aAAa,CAACD,CAAD,CAjBmB,KAOhCC,CAAAA,aAAa,CAACD,CAAD,CAkBhB,CAzBuB,CAyBrB,EAzBqB,CAHR,CA8BnB,CApMD,GAAKnF,CAAG,CAACG,aAAL,EAAuBH,CAAG,CAACO,iBAA/B,KAIM1M,CAAAA,CAAO,CAAG6B,CAAC,CAAC7B,OAJlB,CA2CIkR,CAAW,CAAG,EA3ClB,CA4CIC,CAAS,CAAG,CA5ChB,CA6CIS,CAAK,CAAG,EA7CZ,CA8CIN,CAAU,CAAG,IA9CjB,CAsMInF,CAAG,CAACG,aAtMR,EAuMEzK,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAalK,CAAb,CAAsB,YAAtB,CAAoCgR,CAApC,CAvMF,CAwMEnP,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAalK,CAAb,CAAsB,WAAtB,CAAmCwR,CAAnC,CAxMF,CAyME3P,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAalK,CAAb,CAAsB,UAAtB,CAAkC6R,CAAlC,CAzMF,EA0MW1F,CAAG,CAACO,iBA1Mf,GA2MM3J,MAAM,CAACiP,YA3Mb,EA4MInQ,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAalK,CAAb,CAAsB,aAAtB,CAAqCgR,CAArC,CA5MJ,CA6MInP,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAalK,CAAb,CAAsB,aAAtB,CAAqCwR,CAArC,CA7MJ,CA8MI3P,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAalK,CAAb,CAAsB,WAAtB,CAAmC6R,CAAnC,CA9MJ,EA+Ma9O,MAAM,CAACkP,cA/MpB,GAgNIpQ,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAalK,CAAb,CAAsB,eAAtB,CAAuCgR,CAAvC,CAhNJ,CAiNInP,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAalK,CAAb,CAAsB,eAAtB,CAAuCwR,CAAvC,CAjNJ,CAkNI3P,CAAC,CAACgI,KAAF,CAAQK,IAAR,CAAalK,CAAb,CAAsB,aAAtB,CAAqC6R,CAArC,CAlNJ,GAqND,CH9LgB,EAQIK,CAAgB,CACnC,SAAYlS,CAAZ,CAAqBmS,CAArB,CAAwC,YAKxC,aAAA,IALiC,CAAG,EAKpC,EAJyB,QAAnB,QAAOnS,CAAAA,CAIb,GAHEA,CAAS,CAAGW,QAAQ,CAAC2O,aAAT,CAAuBtP,CAAvB,CAGd,EAAM,CAACA,CAAD,EAAY,CAACA,CAAO,CAACoS,QAA3B,CACI,KAAM,IAAIpR,CAAAA,KAAJ,CAAU,wDAAV,CAAN,CAQF,IAAKZ,GAAMC,CAAAA,CAAX,GALA,MAAKL,OAAL,CAAeA,CAKf,CAHFA,CAAS,CAAC+B,SAAV,CAAoBO,GAApB,CAAwBN,CAAG,CAACwI,IAA5B,CAGE,CADA,KAAK9H,QAAL,CAAgBqK,CAAe,EAC/B,CAAkBoF,CAAlB,CACA,KAAOzP,QAAP,CAAgBrC,CAAhB,EAAuB8R,CAAY,CAAC9R,CAAD,CAAnC,CAGA,KAAK8E,cAAL,CAAsB,IAlBgB,CAmBtC,KAAKrB,eAAL,CAAuB,IAnBe,CAoBtC,KAAKuB,YAAL,CAAoB,IApBkB,CAqBtC,KAAKxB,aAAL,CAAqB,IArBiB,IAuBhC+G,CAAAA,CAAK,WAAM,OAAG5K,CAAAA,CAAO,CAAC+B,SAAR,CAAkBO,GAAlB,CAAsBN,CAAG,CAACC,KAAJ,CAAU2I,KAAhC,CAAsC,CAvBpB,CAwBhCyH,CAAI,WAAM,OAAGrS,CAAAA,CAAO,CAAC+B,SAAR,CAAkBb,MAAlB,CAAyBc,CAAG,CAACC,KAAJ,CAAU2I,KAAnC,CAAyC,CAxBtB,CA0BtC,KAAKxD,KAAL,CAA4C,KAA/B3C,GAAAA,CAAO,CAACzE,CAAD,CAAPyE,CAAiB6I,SA1BQ,CA2BlC,UAAKlG,KA3B6B,EA4BtCpH,CAAS,CAAC+B,SAAV,CAAoBO,GAApB,CAAwBN,CAAG,CAACyI,GAA5B,CA5BsC,CA8BtC,KAAK6H,gBAAL,WAA4B,IACpBC,CAAAA,CAAkB,CAAGvS,CAAO,CAACyG,UADT,CAEtB+L,CAAM,CAAG,IAFa,CAM5B,MAHExS,CAAAA,CAAO,CAACyG,UAAR,CAAqB,CAAC,CAGxB,CAFE+L,CAAM,CAAwB,CAArB,CAAAxS,CAAO,CAACyG,UAEnB,CADEzG,CAAO,CAACyG,UAAR,CAAqB8L,CACvB,CAASC,CACR,CAPuB,EA9Bc,CAsCtC,KAAKhM,wBAAL,CAAgC,KAAK8L,gBAAL,CAC5BtS,CAAO,CAACsF,WAAR,CAAsBtF,CAAO,CAAC8P,WADF,CAE5B,CAxCkC,CAyCtC,KAAKjG,KAAL,CAAa,GAAI+B,CAAAA,CAzCqB,CA0CxC,KAAO7B,aAAP,CAAuB/J,CAAO,CAAC+J,aAAR,EAAyBpJ,QA1CR,CA4CtC,KAAK6E,cAAL,CAAsBiN,CAAO,CAACzQ,CAAG,CAAChC,OAAJ,CAAY0F,IAAZ,CAAiB,GAAjB,CAAD,CA5CS,CA6CxC1F,CAAS,CAAC6F,WAAV,CAAsB,KAAKL,cAA3B,CA7CwC,CA8CtC,KAAKgD,UAAL,CAAkBiK,CAAO,CAACzQ,CAAG,CAAChC,OAAJ,CAAY0K,KAAZ,CAAkB,GAAlB,CAAD,CA9Ca,CA+CxC,KAAOlF,cAAP,CAAsBK,WAAtB,CAAkC,KAAK2C,UAAvC,CA/CwC,CAgDxC,KAAOA,UAAP,CAAkBkK,YAAlB,CAA+B,UAA/B,CAA2C,CAA3C,CAhDwC,CAiDtC,KAAK7I,KAAL,CAAWK,IAAX,CAAgB,KAAK1B,UAArB,CAAiC,OAAjC,CAA0CoC,CAA1C,CAjDsC,CAkDtC,KAAKf,KAAL,CAAWK,IAAX,CAAgB,KAAK1B,UAArB,CAAiC,MAAjC,CAAyC6J,CAAzC,CAlDsC,CAmDtC,KAAKpM,gBAAL,CAAwB,IAnDc,CAoDtC,KAAKI,eAAL,CAAuB,IApDe,CAqDtC,KAAKE,cAAL,CAAsB,IArDgB,CAsDtCnG,GAAMuS,CAAAA,CAAU,CAAGlO,CAAO,CAAC,KAAKe,cAAN,CAA1BpF,CACA,KAAK0H,gBAAL,CAAwB3D,QAAQ,CAACwO,CAAU,CAAC9K,MAAZ,CAAoB,EAApB,CAvDM,CAwDlC+K,KAAK,CAAC,KAAK9K,gBAAN,CAxD6B,EAyDpC,KAAKF,uBAAL,GAzDoC,CA0DtC,KAAOI,aAAP,CAAuB9D,CAAK,CAACyO,CAAU,CAAC5K,GAAZ,CA1DU,EA4DpC,KAAKH,uBAAL,GA5DoC,CA8DxC,KAAOa,gBAAP,CACIvE,CAAK,CAACyO,CAAU,CAAC9N,eAAZ,CAAL,CAAoCX,CAAK,CAACyO,CAAU,CAAC7N,gBAAZ,CA/DL,CAiEtCmD,CAAO,CAAC,KAAKzC,cAAN,CAAsB,CAAEqN,OAAO,CAAE,OAAX,CAAtB,CAjE+B,CAkExC,KAAO1M,gBAAP,CACIjC,CAAK,CAACyO,CAAU,CAACG,UAAZ,CAAL,CAA+B5O,CAAK,CAACyO,CAAU,CAACI,WAAZ,CAnEA,CAoEtC9K,CAAO,CAAC,KAAKzC,cAAN,CAAsB,CAAEqN,OAAO,CAAE,EAAX,CAAtB,CApE+B,CAqEtC,KAAK3M,UAAL,CAAkB,IArEoB,CAsEtC,KAAKE,UAAL,CAAkB,IAtEoB,CAwEtC,KAAKN,cAAL,CAAsB2M,CAAO,CAACzQ,CAAG,CAAChC,OAAJ,CAAY0F,IAAZ,CAAiB,GAAjB,CAAD,CAxES,CAyExC1F,CAAS,CAAC6F,WAAV,CAAsB,KAAKC,cAA3B,CAzEwC,CA0EtC,KAAK4C,UAAL,CAAkB+J,CAAO,CAACzQ,CAAG,CAAChC,OAAJ,CAAY0K,KAAZ,CAAkB,GAAlB,CAAD,CA1Ea,CA2ExC,KAAO5E,cAAP,CAAsBD,WAAtB,CAAkC,KAAK6C,UAAvC,CA3EwC,CA4ExC,KAAOA,UAAP,CAAkBgK,YAAlB,CAA+B,UAA/B,CAA2C,CAA3C,CA5EwC,CA6EtC,KAAK7I,KAAL,CAAWK,IAAX,CAAgB,KAAKxB,UAArB,CAAiC,OAAjC,CAA0CkC,CAA1C,CA7EsC,CA8EtC,KAAKf,KAAL,CAAWK,IAAX,CAAgB,KAAKxB,UAArB,CAAiC,MAAjC,CAAyC2J,CAAzC,CA9EsC,CA+EtC,KAAKzL,gBAAL,CAAwB,IA/Ec,CAgFtC,KAAKI,gBAAL,CAAwB,IAhFc,CAiFtC,KAAKC,aAAL,CAAqB,IAjFiB,CAkFtC7G,GAAM4S,CAAAA,CAAU,CAAGvO,CAAO,CAAC,KAAKqB,cAAN,CAA1B1F,CACA,KAAKiI,eAAL,CAAuBlE,QAAQ,CAAC6O,CAAU,CAAC5K,KAAZ,CAAmB,EAAnB,CAnFO,CAoFlCwK,KAAK,CAAC,KAAKvK,eAAN,CApF6B,EAqFpC,KAAKF,sBAAL,GArFoC,CAsFtC,KAAOI,cAAP,CAAwBrE,CAAK,CAAC8O,CAAU,CAACrL,IAAZ,CAtFS,EAwFpC,KAAKQ,sBAAL,GAxFoC,CA0FtC,KAAKG,oBAAL,CAA4B,KAAKlB,KAAL,CAAa7C,CAAU,CAAC,KAAKmE,UAAN,CAAvB,CAA2C,IA1FjC,CA2FxC,KAAOC,gBAAP,CACIzE,CAAK,CAAC8O,CAAU,CAACC,cAAZ,CAAL,CAAmC/O,CAAK,CAAC8O,CAAU,CAACE,iBAAZ,CA5FJ,CA6FtCjL,CAAO,CAAC,KAAKnC,cAAN,CAAsB,CAAE+M,OAAO,CAAE,OAAX,CAAtB,CA7F+B,CA8FxC,KAAO/L,iBAAP,CACI5C,CAAK,CAAC8O,CAAU,CAACG,SAAZ,CAAL,CAA8BjP,CAAK,CAAC8O,CAAU,CAACI,YAAZ,CA/FC,CAgGtCnL,CAAO,CAAC,KAAKnC,cAAN,CAAsB,CAAE+M,OAAO,CAAE,EAAX,CAAtB,CAhG+B,CAiGtC,KAAKhM,WAAL,CAAmB,IAjGmB,CAkGtC,KAAKE,UAAL,CAAkB,IAlGoB,CAoGxC,KAAOrD,KAAP,CAAe,CACX5B,CAAC,CACuB,CAAtB,EAAA9B,CAAO,CAACyG,UAAR,CACI,OADJ,CAEIzG,CAAO,CAACyG,UAAR,EAAsB,KAAKpB,YAAL,CAAoB,KAAKF,cAA/C,CACA,KADA,CAEA,IANK,CAOXxB,CAAC,CACsB,CAArB,EAAA3D,CAAO,CAAC4D,SAAR,CACI,OADJ,CAEI5D,CAAO,CAAC4D,SAAR,EAAqB,KAAKC,aAAL,CAAqB,KAAKC,eAA/C,CACA,KADA,CAEA,IAZK,CApGyB,CAmHtC,KAAKrB,OAAL,GAnHsC,CAqHtC,KAAKC,QAAL,CAAcoI,QAAd,CAAuBnF,OAAvB,UAA+B0N,EAAY,OAAGvI,CAAAA,CAAQ,CAACuI,CAAD,CAAR,CAAsBjI,CAAtB,CAA2B,CAAzE,CArHsC,CAuHtC,KAAKkI,aAAL,CAAqB,EAAWtT,CAAO,CAAC4D,SAAnB,CAvHiB,CAwHtC,KAAK2P,cAAL,CAAsBvT,CAAO,CAACyG,UAxHQ,CAyHxC,KAAOoD,KAAP,CAAaK,IAAb,CAAkB,KAAKlK,OAAvB,CAAgC,QAAhC,UAA0C8I,EAAE,OAAGsC,CAAAA,CAAI,CAACoI,QAALpI,CAActC,CAAdsC,CAAgB,CAA/D,CAzHwC,CA0HtChC,CAAc,CAAC,IAAD,SAGlB8I,CAAAA,WAAA,CAAEuB,MAAF,WAAW,CACF,KAAKhR,OADH;;;AAMP,KAAK+D,wBAAL,CAAgC,KAAK8L,gBAAL,CAC5B,KAAKtS,OAAL,CAAasF,WAAb,CAA2B,KAAKtF,OAAL,CAAa8P,WADZ,CAE5B,CARG,CAWP7H,CAAO,CAAC,KAAKzC,cAAN,CAAsB,CAAEqN,OAAO,CAAE,OAAX,CAAtB,CAXA,CAYP5K,CAAO,CAAC,KAAKnC,cAAN,CAAsB,CAAE+M,OAAO,CAAE,OAAX,CAAtB,CAZA,CAaT,KAAO1M,gBAAP,CACIjC,CAAK,CAACO,CAAO,CAAC,KAAKe,cAAN,CAAPf,CAA6BqO,UAA9B,CAAL,CACA5O,CAAK,CAACO,CAAO,CAAC,KAAKe,cAAN,CAAPf,CAA6BsO,WAA9B,CAfA,CAgBT,KAAOjM,iBAAP,CACI5C,CAAK,CAACO,CAAO,CAAC,KAAKqB,cAAN,CAAPrB,CAA6B0O,SAA9B,CAAL,CACAjP,CAAK,CAACO,CAAO,CAAC,KAAKqB,cAAN,CAAPrB,CAA6B2O,YAA9B,CAlBA,CAqBPnL,CAAO,CAAC,KAAKzC,cAAN,CAAsB,CAAEqN,OAAO,CAAE,MAAX,CAAtB,CArBA,CAsBP5K,CAAO,CAAC,KAAKnC,cAAN,CAAsB,CAAE+M,OAAO,CAAE,MAAX,CAAtB,CAtBA,CAwBPzJ,CAAc,CAAC,IAAD,CAxBP,CA0BP5F,CAAiB,CAAC,IAAD,CAAO,KAAP,CAAc,CAAd,OA1BV,CA2BPA,CAAiB,CAAC,IAAD,CAAO,MAAP,CAAe,CAAf,OA3BV,CA6BPyE,CAAO,CAAC,KAAKzC,cAAN,CAAsB,CAAEqN,OAAO,CAAE,EAAX,CAAtB,CA7BA,CA8BP5K,CAAO,CAAC,KAAKnC,cAAN,CAAsB,CAAE+M,OAAO,CAAE,EAAX,CAAtB,CA9BA,GAiCXX,WAAA,CAAEsB,QAAF,WAAc,CACL,KAAK/Q,OADA,GAKV2G,CAAc,CAAC,IAAD,CALJ,CAMV5F,CAAiB,CAAC,IAAD,CAAO,KAAP,CAAc,KAAKxD,OAAL,CAAa4D,SAAb,CAAyB,KAAK0P,aAA5C,CANP,CAOV9P,CAAiB,CACf,IADe,CAEf,MAFe,CAGjB,KAAOxD,OAAP,CAAeyG,UAAf,CAA4B,KAAK8M,cAHhB,CAPP,CAaV,KAAKD,aAAL,CAAqB,EAAW,KAAKtT,OAAL,CAAa4D,SAAxB,CAbX,CAcZ,KAAO2P,cAAP,CAAwB,KAAKvT,OAAL,CAAayG,UAdzB,GAiBdyL,WAAA,CAAEwB,OAAF,WAAY,CACH,KAAKjR,OADF;AAKR,KAAKoH,KAAL,CAAWyB,SAAX,EALQ,CAMV1F,CAAY,CAAC,KAAK4C,UAAN,CANF,CAOV5C,CAAY,CAAC,KAAK8C,UAAN,CAPF,CAQV9C,CAAY,CAAC,KAAKJ,cAAN,CARF,CASVI,CAAY,CAAC,KAAKE,cAAN,CATF,CAUR,KAAK6N,eAAL,EAVQ,CAaR,KAAK3T,OAAL,CAAe,IAbP,CAcR,KAAKwI,UAAL,CAAkB,IAdV,CAeR,KAAKE,UAAL,CAAkB,IAfV,CAgBR,KAAKlD,cAAL,CAAsB,IAhBd,CAiBR,KAAKM,cAAL,CAAsB,IAjBd,CAmBR,KAAKrD,OAAL,GAnBQ,GAsBZyP,WAAA,CAAEyB,eAAF,WAAoB,CAClB,KAAO3T,OAAP,CAAeU,SAAf,CAA2B,KAAKV,OAAL,CAAaU,SAAb,CACtBkT,KADsB,CAChB,GADgB,EAEtBnS,MAFsB,UAEfqB,EAAK,OAAG,CAACA,CAAI,CAAC2M,KAAL,CAAW,eAAX,CAA2B,CAFrB,EAGtBoE,IAHsB,CAGjB,GAHiB,CAI1B"}