<?php

namespace App\Http\Middleware\Backend;

use Closure;
use Illuminate\Http\Request;

class RedirectDefault
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if(shopGuard()->check()) {
            return redirect()->to(buildShopInfoUrl());
        }elseif(backendGuard()->check()) {
            return redirect()->to(buildListShopUrl());
        }
        return $next($request);
    }
}
