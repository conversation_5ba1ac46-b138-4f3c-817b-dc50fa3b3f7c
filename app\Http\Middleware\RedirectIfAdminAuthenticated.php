<?php

namespace App\Http\Middleware;

/**
 * Class RedirectIfAdminAuthenticated
 * @package App\Http\Middleware
 */
class RedirectIfAdminAuthenticated extends Authenticated
{
    public function init()
    {
        $this->setGuard($this->_guard());
    }

    public function _guard()
    {
        $guard = backendGuard();
        if(shopGuard()->check()) {
           $guard = shopGuard();
        }
        return $guard;
    }
}
