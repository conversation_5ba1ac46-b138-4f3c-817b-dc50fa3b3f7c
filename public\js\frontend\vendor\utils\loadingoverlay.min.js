/***************************************************************************************************
LoadingOverlay - A flexible loading overlay jQuery plugin
    Author          : Gaspare Sganga
    Version         : 1.5.3
    License         : MIT
    Documentation   : http://gasparesganga.com/labs/jquery-loading-overlay/
****************************************************************************************************/
!function(A,B){function C(C,g){C=A(C);var E=C.is("body"),I=C.data("LoadingOverlayCount");if(I===B&&(I=0),0==I){var o=A("<div>",{class:"loadingoverlay",css:{"background-color":g.color,position:"relative",display:"flex","flex-direction":"column","align-items":"center","justify-content":"center"}});if(g.zIndex!==B&&o.css("z-index",g.zIndex),g.image&&o.css({"background-image":"url("+g.image+")","background-position":g.imagePosition,"background-repeat":"no-repeat"}),g.fontawesome&&A("<div>",{class:"loadingoverlay_fontawesome "+g.fontawesome}).appendTo(o),g.custom&&A(g.custom).appendTo(o),E?o.css({position:"fixed",top:0,left:0,width:"100%",height:"100%"}):o.css("position","fixed"==C.css("position")?"fixed":"absolute"),Q(C,o,g,E),g.resizeInterval>0){var w=setInterval(function(){Q(C,o,g,E)},g.resizeInterval);C.data("LoadingOverlayResizeIntervalId",w)}g.fade?g.fade===!0?g.fade=[400,200]:"string"!=typeof g.fade&&"number"!=typeof g.fade||(g.fade=[g.fade,g.fade]):g.fade=[0,0],C.data({LoadingOverlay:o,LoadingOverlayFadeOutDuration:g.fade[1]}),o.hide().appendTo("body").fadeIn(g.fade[0])}I++,C.data("LoadingOverlayCount",I)}function g(C,g){C=A(C);var Q=C.data("LoadingOverlayCount");if(Q!==B)if(Q--,g||Q<=0){var E=C.data("LoadingOverlayResizeIntervalId");E&&clearInterval(E),C.data("LoadingOverlay").fadeOut(C.data("LoadingOverlayFadeOutDuration"),function(){A(this).remove()}),C.removeData(["LoadingOverlay","LoadingOverlayCount","LoadingOverlayFadeOutDuration","LoadingOverlayResizeIntervalId"])}else C.data("LoadingOverlayCount",Q)}function Q(B,C,g,Q){if(!Q){var E="fixed"==B.css("position")?B.position():B.offset();C.css({top:E.top+parseInt(B.css("border-top-width"),10),left:E.left+parseInt(B.css("border-left-width"),10),width:B.innerWidth(),height:B.innerHeight()})}var I=Q?A(window):B,o="auto";g.size&&"auto"!=g.size&&(o=Math.min(I.innerWidth(),I.innerHeight())*parseFloat(g.size)/100,g.maxSize&&o>parseInt(g.maxSize,10)&&(o=parseInt(g.maxSize,10)+"px"),g.minSize&&o<parseInt(g.minSize,10)&&(o=parseInt(g.minSize,10)+"px")),C.css("background-size",o),C.children(".loadingoverlay_fontawesome").css("font-size",o)}var E={color:"rgba(255, 255, 255, 0.8)",custom:"<div class=\"spinner\"></div>",fade:!0,fontawesome:"",image:"",imagePosition:"center center",maxSize:"100px",minSize:"20px",resizeInterval:50,size:"50%",zIndex:9999};A.LoadingOverlaySetup=function(B){A.extend(!0,E,B)},A.LoadingOverlay=function(B,Q){switch(B.toLowerCase()){case"show":var I=A.extend(!0,{},E,Q);C("body",I);break;case"hide":g("body",Q)}},A.fn.LoadingOverlay=function(B,Q){switch(B.toLowerCase()){case"show":var I=A.extend(!0,{},E,Q);return this.each(function(){C(this,I)});case"hide":return this.each(function(){g(this,Q)})}}}(jQuery);