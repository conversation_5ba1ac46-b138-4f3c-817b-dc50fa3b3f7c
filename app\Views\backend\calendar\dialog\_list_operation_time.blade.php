
<div class="custom-dialog-inner">
    <div class="form custom-dialog-form" id="reserve-time-form">
        <input type="hidden" name="reserve_date" value="{{ $reserve_date }}" id="set-reserve-date">
        <div class="reserve-date-target">
            <div class="label">編集対象</div>
            <div class="target">
                <div id="show-reserve-date">{{ toDateJa($reserve_date) }}</div>
            </div>
        </div>
        <ul id="reserve-time-list" class="reserve-time-list">
            @if(sizeof($data))
                @foreach($data as $value)
                    @php
                        $isDeny = $value['deny'] == getConstant('DENY.ON');
                    @endphp

                    <li class="reserve_num_item {{ $isDeny ? 'disabled' : '' }}" data-id="{{ isset($value['id']) ? $value['id'] : '' }}">
                        <div class="time-range" data-from="{{ $value['from'] }}" data-to="{{ $value['to'] }}"> {{ $value['from'] }} 〜 {{ $value['to'] }}</div>
                        <div class="receiving-quantity">{{ $isDeny ? '拒否設定済み' : '受入可能数' }}  ： <span>{{ !empty($dataShop->reservation_frame_num) ? $dataShop->reservation_frame_num: '' }}</span></div>
                        <div class="receiving-number">予約受付数 ：
                            @if($isDeny)
                                <input type="number" value="{{ isset($value['reserve_num']) ? $value['reserve_num'] : 0 }}" class="form-control" tabindex="-1">
                            @else
                                <input type="number" value="{{ isset($value['reserve_num']) ? $value['reserve_num'] : 0 }}" class="form-control">
                            @endif
                        </div>
                    </li>
                    <li class="text-error"><div></div></li>
                @endforeach
            @else
                <p>選択できません</p>
            @endif
        </ul>
    </div>
</div>
