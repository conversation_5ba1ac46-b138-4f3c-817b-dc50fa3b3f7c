<?php

namespace App\Repositories;

use App\Model\Entities\ReserveDenials;
use App\Repositories\Base\CustomRepository;
use App\Validators\Backend\ReserveDenial;

class ReserveDenialsRepository extends CustomRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    function model()
    {
        return ReserveDenials::class;
    }

    public function validator()
    {
        return ReserveDenial::class;
    }

    /**
     * @param $shopId
     * @return array
     */
    public function getTimeDenyByShop($shopId, $isDataInt = false)
    {
        $entities = $this->select(['denial_time_from', 'denial_time_to'])->where('shop_id', $shopId)->orderBy('denial_time_from', 'asc')->get();

        $data = [];
        if (sizeof($entities) > 0) {
            foreach ($entities as $key => $value) {
                $data[$key] = [
                    'from' => toMinute($value['denial_time_from']),
                    'to' => toMinute($value['denial_time_to']),
                ];
            }
        }

        if ($isDataInt) {
            foreach ($data as $key => $value) {
                $data[$key] = [$value['from'], $value['to']];
            }
        }

        return $data;
    }
}
