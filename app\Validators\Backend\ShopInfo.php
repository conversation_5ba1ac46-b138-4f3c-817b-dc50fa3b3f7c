<?php

namespace App\Validators\Backend;

use App\Validators\Base\BaseValidator;

/**
 * Class AdminUserInfo
 * @package App\Validator
 */
class ShopInfo extends BaseValidator
{
    /**
     * @param $data
     * @return bool
     */
    public function validateLogin($data)
    {
        $rules = array(
            'email' => 'required|email', // make sure the email is an actual email
            'password' => 'required' // password can only be alphanumeric and has to be greater than 3 characters
        );
        $messages = [
            'email.required' => trans('auth.id_required'),
            'email.email' => trans('auth.email_password_invalid'),
            'password.required' => trans('auth.password_required'),
        ];
        return $this->_addRules($rules, $messages)->with($data)->passes();
    }

    /**
     * @return array
     */
    protected function _getRulesDefault()
    {
        $fileRule = getConfig('file.admin.avatar');
        $imageType = implode(',', $fileRule['ext']);
        $groups = getKeysConfig('user.groups');
        $rules = [
            'email' => 'required|email|max:256' . $this->_getUniqueInDbRule(),
            'login_password' => 'nullable|same:password_confirmation|min:6|max:8',
            'groups' => 'required',
        ];
        foreach ($this->getData('groups', []) as $idx => $datum) {
            $rules += [
                'groups.' . $idx . $this->_getInArrayRule($groups)
            ];
        }
        if ($this->_hasFileUpload('avatar')) {
            $rules += [
                'avatar' => 'mimes:' . $imageType . '|max:' . $fileRule['size']['max'] * 1024,
            ];
        }
        return $rules;
    }

    /**
     * @return array
     */
    protected function _buildCreateRules()
    {
        return [
            'rules' => $this->_buildRules([
                'login_password' => 'required|same:password_confirmation|min:6|max:8',
                'password_confirmation' => 'required|same:password_confirmation|min:6|max:8',
            ])
        ];
    }

    /**
     * @return array
     */
    protected function _buildUpdateRules()
    {
        return parent::_buildUpdateRules(); // TODO: Change the autogenerated stub
    }

    /**
     * @return array
     */
    protected function _buildSearchRules()
    {
        return parent::_buildSearchRules(); // TODO: Change the autogenerated stub
    }

    /**
     * @return array
     */
    protected function _buildDestroyRules()
    {
        return parent::_buildDestroyRules(); // TODO: Change the autogenerated stub
    }


    /**
     * @param $data
     * @return bool
     */
    public function validateResetPassword($data)
    {
        $rules = array(
            'email' => 'required|email', // make sure the email is an actual email
        );
        return $this->_addRules($rules)->with($data)->passes();
    }

    /**
     * @param $data
     * @return bool
     */
    public function validateConfirmReset($data)
    {
        $rules = array(
            'password' => 'required|min:5|max:12|same:password_confirmation',
            'password_confirmation' => 'required',
        );
        $message = [];
        return $this->_addRules($rules, $message)->with($data)->passes();
    }

    public function validateDestroy($data)
    {
        return $this->_addRules([], [])->with($data)->passes();
    }

    public function getRulesDefault($params)
    {
        $rules = [
            'shop_name' => 'required',
            'reserve_form_url' => 'required|max:500|regex:/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/',
            'address' => 'required',
            'shop_user_email' => 'required|max:500|email|regex:/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/' . $this->_getUniqueInDbRule(\App\Model\Entities\ShopInfo::class, ['shop_user_email' => request('shop_user_email')]) . $this->_getUniqueInDbRule(\App\Model\Entities\AdminUserInfo::class, ['email' => request('shop_user_email')]),
            'interval_min' => 'required|integer|in:30,60,90,120',
            'reservable_period_days' => 'required|numeric',
            'reservation_frame_num' => 'required|numeric|max:99',
            'tel' => 'required|numeric|digits_between:1,20'
        ];

        if (!empty($params['publish_to_dt_date']) | !empty($params['publish_to_dt_time'])) {
            $rules += [
                'publish_to_dt' => 'required|after:publish_from_dt',
            ];
        }

        foreach (getConfig('week_day') as $day) {
            if (isset($params[$day . '_closing_flg']) && $params[$day . '_closing_flg'] == getConstant('CLOSING_FLG.OFF')) {
                $rules += [
                    $day . '_operation_from' => 'required',
                    $day . '_operation_to' => 'required|after:' . $day . '_operation_from',
                ];
            }
        }

        return $rules;
    }

    public function validateCreate($data)
    {
        $rules = [
            'shop_user_pwd' => 'required|max:500',
            'publish_from_dt' => 'required|date',
        ];

        $messages = [
            'publish_from_dt.required' => trans('validation.attributes.public_from_dt_required'),
            'reservable_period_days.required' => trans('validation.attributes.reservable_period_days_required'),
        ];

        return $this->_addRules(array_merge($rules, $this->getRulesDefault($data)), array_merge($messages, $this->_getMessagesDefault()))->with($data)->passes();
    }

    public function validateUpdate($data)
    {
        $rules = [];

        if ($data['publish_from_dt_old'] != $data['publish_from_dt']) {
            $rules += [
                'publish_from_dt' => 'required|date',
            ];
        }

        $messages = [
            'publish_from_dt.required' => trans('validation.attributes.public_from_dt_required'),
            'reservable_period_days.required' => trans('validation.attributes.reservable_period_days_required'),
        ];

        return $this->_addRules(array_merge($rules, $this->getRulesDefault($data)), array_merge($messages, $this->_getMessagesDefault()))->with($data)->passes();
    }

    public function validateChangePass($data)
    {
        $rules = [];
        if (empty($data['shop_user_pwd_current'])) {
            $rules += [
                'shop_user_pwd_current' => 'required',
            ];
        } else {
            $rules += [
                'check_current_password' => 'required',
                'shop_user_pwd' => 'required|max:500|same:shop_user_pwd_confirm',
                'shop_user_pwd_confirm' => 'required',
            ];
        }

        $messages = [
            'check_current_password.required' => trans('validation.attributes.check_current_password'),
            'shop_user_pwd_current.required' => trans('validation.attributes.shop_user_pwd_current_required')
        ];

        return $this->_addRules($rules, $messages)->with($data)->passes();
    }
}
