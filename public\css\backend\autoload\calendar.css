.select-time {
    height: 35px;
    background: #ffffff url("../img/icon_time.svg") right no-repeat;
    outline: none;
    border: none;
    border-radius: 6px;
    max-width: 200px;
    width: 100%;
    -webkit-appearance: none;
    -moz-appearance: none;
    text-overflow: '';
    padding-left: 10px;
}

.icon-to {
    -webkit-mask-size: cover;
    mask-size: cover;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    display: inline-block;
    background: #000000;
    -webkit-mask: url('../img/to.svg');
    mask: url('../img/to.svg');
    height: 25px;
    width: 16px;
    margin-top: 6px;
}

/* Begin: dialog */
#dialog-reserve-denials .reserve-date-target {
    background-color: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    align-items: center;
    padding: 2rem 2rem;
    margin-bottom: 3rem;
    white-space: nowrap;
}

@media (max-width: 767px) {
    #dialog-reserve-denials .reserve-date-target {
        padding: 2rem 1.25rem;
    }
}

#dialog-reserve-denials .reserve-date-target .label {
    width: 200px;
}

@media (max-width: 767px) {
    #dialog-reserve-denials .reserve-date-target .label {
        margin-right: auto;
        width: auto;
    }
}

#dialog-reserve-denials .reserve-date-target .target {
    flex: 1;
}

@media (max-width: 767px) {
    #dialog-reserve-denials .reserve-date-target .target {
        margin-left: auto;
    }
}

#dialog-reserve-denials .reserve-time-list {
    background-color: #fff;
    padding: 2rem 2rem;
}

#dialog-reserve-operation-time .reserve-time-list {
    padding: 2rem 0;
}

@media (max-width: 767px) {
    #dialog-reserve-denials .reserve-time-list {
        padding: 1.25rem;
    }
}

#dialog-reserve-denials .reserve-time-list li {
    white-space: nowrap;
    margin-bottom: 2rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    align-items: center;
    width: 100%;
    padding: 0 20px;
}

@media (max-width: 767px) {
    #dialog-reserve-denials .reserve-time-list li {
        display: block;
    }
}

#dialog-reserve-denials .reserve-time-list li:last-child {
    margin-bottom: 0;
}

#dialog-reserve-denials .reserve-time-list li .time-range {
    width: 200px;
}

@media (max-width: 767px) {
    #dialog-reserve-denials .reserve-time-list li .time-range {
        font-weight: bold;
        width: auto;
        margin-bottom: .5rem;
    }
}

#dialog-reserve-denials .reserve-time-list li .receiving-quantity {
    margin-right: 3rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    align-items: center;
}

@media (max-width: 767px) {
    #dialog-reserve-denials .reserve-time-list li .receiving-quantity {
        padding-left: 1.5rem;
        margin-right: auto;
        margin-bottom: .5rem;
    }
}

#dialog-reserve-denials .reserve-time-list li .receiving-quantity span {
    border: 1px solid #C0C0C0;
    background-color: #f5f5f5;
    display: inline-block;
    padding: 5px 15px;
    line-height: 1.5;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    border-radius: 3px;
}

@media (max-width: 767px) {
    #dialog-reserve-denials .reserve-time-list li .receiving-quantity span {
        background-color: #fff;
        border: none;
        display: block;
        padding: 0;
        -webkit-border-radius: 0;
        -moz-border-radius: 0;
        -ms-border-radius: 0;
        border-radius: 0;
    }
}

#dialog-reserve-denials .reserve-time-list li .receiving-number {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    align-items: center;
}

@media (max-width: 767px) {
    #dialog-reserve-denials .reserve-time-list li .receiving-number {
        padding-left: 1.5rem;
    }
}

#dialog-reserve-denials .reserve-time-list li .receiving-number input.form-control {
    border: 1px solid #C0C0C0;
    padding: 5px 8px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    border-radius: 3px;
}

#dialog-reserve-denials .reserve-time-list li .receiving-number input.form-control:focus {
    border-color: #508FF4;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    box-shadow: none;
}

#dialog-reserve-denials .line-period {
    margin-bottom: 15px;
}

#dialog-reserve-denials .item-period .box-period {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#dialog-reserve-denials .item-period .text-error {
    padding: 10px 0 0 4px;
    display: none;
}

#dialog-reserve-denials .item-period {
    margin-bottom: 20px;
}

#dialog-reserve-denials .btn-add-period {
    background: #E6E6E6;
    border-radius: 8px;
    text-align: center;
    width: 100px;
    height: 35px;
    float: right;
    padding: 0;
    outline: none;
}

#dialog-reserve-denials {
    padding-left: 100px;
    padding-right: 100px;
}

#dialog-reserve-denials .btn-remove-period {
    background: #E67E7E;
    color: white;
    margin-left: 10px;
    border-radius: 8px;
    text-align: center;
    width: 100px;
    height: 35px;
    float: right;
    padding: 0;
    outline: none;
    justify-content: space-between;
}

#dialog-reserve-denials .justify-between {
    justify-content: space-between !important;
}

#dialog-reserve-denials .justify-between {
    display: flex;
}

#dialog-reserve-denials .btn-remove-period .d-flex {
    display: flex !important;
}

.ml-60 { margin-left: 60px }
.mr-60 { margin-right: 60px }
/* End: dialog */

/* Begin: dialog reserve-num */
#dialog-reserve-operation-time .reserve-date-target .label {
    width: 200px;
    text-align: left;
    font-size: 100%;
    color: #333;
    font-weight: normal;
}

#dialog-reserve-operation-time .reserve-time-list li {
    display: flex;
    justify-content: space-between;
    padding: 0 2rem;
}

#dialog-reserve-operation-time .reserve-time-list li .receiving-quantity span
{
    border-radius: 0;
    padding: 5px 22.5px;
}

#dialog-reserve-operation-time .reserve-time-list li .receiving-number input
{
    width: 55px;
    border-radius: 0 !important;
    text-align: center;
}

#dialog-reserve-operation-time .reserve-time-list li .receiving-number .border-error
{
    border: 1px solid #a94442 !important;
}

#dialog-reserve-operation-time .text-error {
    color: #a94442;
    margin: -15px;
    width: auto !important;
    display: none !important;
}

#dialog-reserve-operation-time .text-error div {
    width: 100%;
    margin-bottom: -15px;
    text-align: right;
    padding-right: 15px;
}

#dialog-reserve-operation-time .disabled {
    background: #C6C6C6;
}

#dialog-reserve-operation-time .disabled .receiving-quantity span,
#dialog-reserve-operation-time .disabled .receiving-number input
{
    background: #A7A7A7 !important;
    pointer-events: none;
    user-select: none;
    cursor: default;
}

#dialog-reserve-operation-time .reserve-time-list li {
    line-height: inherit;
    padding-top: 5px !important;
    padding-bottom: 5px !important;
    margin-bottom: 13px;
}


#dialog-reserve-operation-time .reserve-time-list li .receiving-quantity {
    margin-right: 3rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    align-items: center;
    justify-content: flex-end;
    width: 130px;
}

div[aria-describedby="dialog-reserve"] .ui-button,
div[aria-describedby="dialog-reserve-denials"] .ui-button
{
    height: 50px !important;
    width: 138px !important;
    border-radius: 8px !important;
}

div[aria-describedby="dialog-reserve"] .btn-save,
div[aria-describedby="dialog-reserve-denials"] .btn-save
{
    margin-left: 35px !important;
}

div[aria-describedby="dialog-reserve"] .btn-cancell,
div[aria-describedby="dialog-reserve-denials"] .btn-cancell
{
    margin-right: 35px !important;
}

.ui-dialog-buttonset {
    text-align: center;
    float: none !important;
}

/* End: dialog reserve-num */

.status-holiday {
    background-color: #f5f5f5;
}

.input-error {
    border: 1px solid #a94442 !important;
}

.ui-dialog-buttonset button {
    height: 50px !important;
}

div[aria-describedby="dialog-reserve-denials"] {
    top: 10% !important;
}

#dialog-reserve-denials {
    max-height: 470px !important;
}
