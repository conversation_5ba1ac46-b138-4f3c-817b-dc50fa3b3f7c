$(document).ready(function () {
    // init
    window.onload = function () {
        let data = JSON.parse($('#json_date').val());
        CALENDER_MONTH.init(data, $('#now').val());
    };

    const CALENDER_MONTH = {
        init: function (data, date) {
            var calendarEL = document.getElementById('calendar');
            calendar = new FullCalendar.Calendar(calendarEL, {
                events: data,
                initialDate: date,
                initialView: 'dayGridMonth',
                locale: 'ja',
                height: 470,
                firstDay: 1,
                headerToolbar: {
                    left: 'prev',
                    center: 'title',
                    right: 'next'
                },
                titleFormat: {
                    year: 'numeric'
                },
                dayMaxEvents: true,
                customButtons: {
                    prev: {
                        text: '前の月',
                        click: function () {
                            var startE = new Date($('#start_date').val());
                            var firstDayOfMonth = new Date(startE.getFullYear(), startE.getMonth(), 1);

                            var start = calPrevStartDate(firstDayOfMonth, startE);
                            var end = calPrevEndDate(startE.getDay(), startE);

                            CALENDER_MONTH.prev(start, end);
                        }
                    },
                    next: {
                        text: '次の月',
                        click: function () {
                            var endE = new Date($('#end_date').val());
                            var lastDayOfMonth = new Date(endE.getFullYear(), endE.getMonth()+1, 1);

                            var start = calNextStartDate(endE.getDay(), endE);
                            var end = calNextEndDate(lastDayOfMonth);

                            CALENDER_MONTH.next(start, end);
                        }
                    },
                },
                dayCellClassNames: function (info) {
                    var cell_date = new Date(info.date);
                    var format_date = cell_date.getFullYear() + '-' + ("0" + (cell_date.getMonth() + 1)).slice(-2) + '-' + ("0" + cell_date.getDate()).slice(-2);
                    var indexDate = data.filter(function (item, index) {
                        if (item.day == format_date) return item;
                    });
                    if (indexDate.length > 0) {
                        return [CALENDER_MONTH.setCellClassName(indexDate[0].count, indexDate[0].tel)];
                    } else {
                        return ['reserve-status-none']
                    }
                },
                dayCellContent: function (e) {
                    var cell_month = e.date.getMonth() + 1;
                    var cell_day = e.date.getDate();
                    e.dayNumberText = cell_month + '/' + cell_day;
                },
                dateClick: function (info) {
                    if ($(info.dayEl).hasClass('fc-day-past')) return;
                    if ($(info.dayEl).hasClass('reserve-status-tel')) {
                        window.location.href = 'tel:' + $('#tel-shop').val();
                        return;
                    }
                    var click_date = info.date;
                    var format_date = click_date.getFullYear() + '-' + ("0" + (click_date.getMonth() + 1)).slice(-2) + '-' + ("0" + click_date.getDate()).slice(-2);

                    var form = $('#calender-form');
                    CALENDER_MONTH.submitFnc(format_date);
                },
                fixedWeekCount: false,
                datesSet: function (dateInfo) {
                    var today = new Date();
                    today.setHours(0);
                    today.setMinutes(0);
                    today.setSeconds(0);
                    today.setMilliseconds(0);

                    var minDate = today.setDate(1); //今の月より前の月は表示しない
                        // maxDate = today.setMonth(today.getMonth() + 2), //最大で表示する未来の月を設定
                        // maxDate = today.setDate(0);
                    var viewDate = calendar.view.currentStart;
                    var minDate = new Date(minDate),
                        maxDate = new Date(maxDate);
                    // Past
                    if (minDate >= viewDate) {
                        $(".fc-prev-button").prop('disabled', true);
                        $(".fc-prev-button").addClass('fc-state-disabled');
                    } else {
                        $(".fc-prev-button").removeClass('fc-state-disabled');
                        $(".fc-prev-button").prop('disabled', false);
                    }
                    // Future
                    if (maxDate <= viewDate) {
                        $(".fc-next-button").prop('disabled', true);
                        $(".fc-next-button").addClass('fc-state-disabled');
                    } else {
                        $(".fc-next-button").removeClass('fc-state-disabled');
                        $(".fc-next-button").prop('disabled', false);
                    }

                    $('.fc-prev-button').text('前の月');
                    $('.fc-next-button').text('次の月');
                }
            });

            calendar.render();
        },

        prev: function (start, end) {
            let date_focus = new Date(start).addDays(14);
            showLoading();
            $.ajax({
                url: $('#get-data-calendar-month').val(),
                type: 'get',
                data: {
                    'shop_id': $('#shop_id').val(),
                    'start_date': start,
                    'end_date': end
                },

                success: function (res) {
                    $('#start_date').val(start);
                    $('#end_date').val(end);
                    CALENDER_MONTH.init(res.data.data, date_focus);

                    // set data for calendar Week
                    $('#json_date').val(JSON.stringify(res.data.data));

                    hideLoading();
                },

                error: function (res) {
                    hideLoading();
                }
            });
        },

        next: function (start, end) {;
            let date_focus = new Date(start).addDays(14);

            showLoading();

            $.ajax({
                url: $('#get-data-calendar-month').val(),
                type: 'get',
                data: {
                    'shop_id': $('#shop_id').val(),
                    'start_date': start,
                    'end_date': end
                },

                success: function (res) {
                    $('#start_date').val(start);
                    $('#end_date').val(end);
                    CALENDER_MONTH.init(res.data.data, date_focus);

                    // set data for calendar Week
                    $('#json_date').val(JSON.stringify(res.data.data));

                    hideLoading();
                },

                error: function (res) {
                    hideLoading();
                }
            });
        },

        setCellClassName: function (count, tel) {
            var className = 'reserve-status-none';
            if (tel) {
                className = 'reserve-status-tel';
            } else {
                if (count === 0) {
                    className = 'reserve-status-none';
                } else if (count >= 5) {
                    className = 'reserve-status-1';
                } else if (count >= 3) {
                    className = 'reserve-status-2';
                } else if (count <= 2) {
                    className = 'reserve-status-3';
                } else if (count == 'tel') {
                    className = 'reserve-status-tel';
                }
            }

            return className;
        },

        submitFnc: function (date) {
            showLoading();
            setTimeout(function () {
                hideLoading();
            }, 500);
            $.ajax({
                url: $('#route-calendar-week').val(),
                type: 'get',
                data: {
                    date: date
                },
                success: function (res) {
                    //set param date in url
                    var currentURL = window.location.protocol + "//" + window.location.host + window.location.pathname + '?date=' + date;
                    window.history.pushState({path: currentURL}, '', currentURL);
                    //change html
                    $('.post').html(res.data.data);
                    //add script of week
                    var script = document.createElement('script');
                    script.src = $('#script_week_url').val();
                    document.body.appendChild(script);
                },
            });
        },
    };
});

// add days
Date.prototype.addDays = function (num) {
    var value = this.valueOf();
    value += 86400000 * num;
    return new Date(value);
}

// convert to string date
function toDateString(date) {
    var d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

    if (month.length < 2)
        month = '0' + month;
    if (day.length < 2)
        day = '0' + day;

    return [year, month, day].join('-');
}

function calNextStartDate(day, end) {
    var start = '';
    switch (day) {
        case 0:
            start = end.addDays(-6);
            break;
        case 1:
            start = end.addDays(-5);
            break;
        case 2:
            start = end.addDays(-4);
            break;
        case 3:
            start = end.addDays(-3);
            break;
        case 4:
            start = end.addDays(-2);
            break;
        case 5:
            start = end.addDays(-1);
            break;
        case 6:
            start = end;
            break;
        default:
        // code block
    }

    return toDateString(start);
}

function calNextEndDate(day) {
    var end = '';
    var endDay = day.getDay();

    switch (endDay) {
        case 0:
            end = day;
            break;
        case 1:
            end = day.addDays(6);
            break;
        case 2:
            end = day.addDays(5);
            break;
        case 3:
            end = day.addDays(4);
            break;
        case 4:
            end = day.addDays(3);
            break;
        case 5:
            end = day.addDays(2);
            break;
        case 6:
            end = day.addDays(1);
            break;
        default:

    }
    return toDateString(end);
}

function calPrevEndDate(day, start) {
    var end = '';
    switch (day) {
        case 0:
            end = start;
            break;
        case 1:
            end = start.addDays(6);
            break;
        case 2:
            end = start.addDays(5);
            break;
        case 3:
            end = start.addDays(4);
            break;
        case 4:
            end = start.addDays(3);
            break;
        case 5:
            end = start.addDays(2);
            break;
        case 6:
            end = start.addDays(1);
            break;
        default:
        // code block
    }

    return toDateString(end);
}

function calPrevStartDate(day, startE) {
    var start = '';
    var startDay = day.getDay();

    switch (startDay) {
        case 0:
            start = day.addDays(-6);
            break;
        case 1:
            start = day;
            break;
        case 2:
            start = day.addDays(-1);
            break;
        case 3:
            start = day.addDays(-2);
            break;
        case 4:
            start = day.addDays(-3);
            break;
        case 5:
            start = day.addDays(-4);
            break;
        case 6:
            start = day.addDays(-5);
            break;
        default:

    }

    if (toDateString(start) == toDateString(startE)) {
        start = new Date(startE.getFullYear(), startE.getMonth() - 1, startE.getDate());
        return calPrevStartDate(start, startE);
    } else {
        return toDateString(start);
    }
}

