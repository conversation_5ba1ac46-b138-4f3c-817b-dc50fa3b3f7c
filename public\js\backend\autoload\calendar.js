const week = ["<PERSON>", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON>i", "<PERSON>t", "<PERSON>"];
const todayDate = new Date().toLocaleString({timeZone: 'Asia/Tokyo'});
const today = new Date(todayDate);
const minMonth = 0;
const maxMonth = parseInt($('#number_months_next').val()) > 0 ? parseInt($('#number_months_next').val()) : 999;
var showDate = new Date(today.getFullYear(), today.getMonth(), 1);

$(document).ready(function () {
    $(this).on('click', '*[data-click]', function (e) {
        let func = $(this).data('click');
        switch (func) {
            case 'addPeriodDeny':
                CALENDAR_SETTING.addPeriodDeny($(this));
                break;
            case 'removePeriodDeny':
                CALENDAR_SETTING.removePeriodDeny($(this));
                break;
            case 'openDialogDenials':
                CALENDAR_SETTING.openDialogDenials(this);
                break;
            case 'changeFlgReserveTel':
                CALENDAR_SETTING.changeFlgReserveTel(this);
                break;
            case 'prevMonth':
                CALENDAR_SETTING.prevMonth(this);
                break;
            case 'nextMonth':
                CALENDAR_SETTING.nextMonth(this);
                break;
            case 'openDialogReserve':
                CALENDAR_SETTING.openDialogReserve(e);
                break;
            default:
        }
    });

    // init
    window.onload = function () {
        let today = moment(new Date()).format('YYYY/MM/DD');
        CALENDAR_SETTING.ajaxRenderCalendar(today);
    };

    const CALENDAR_SETTING = {
        addPeriodDeny: function (el) {
            var html = `
                <div class="item-period" data-shop-id="0">
                    <div class="box-period">
                        <div class="d-flex justify-between" style="width: 80%">
                            <select name="denial_time_from" class="select-time">
                                ` + CALENDAR_SETTING.timeInDay() + `
                            </select>
                            <i class="icon-to"></i>
                            <select name="denial_time_to" class="select-time">
                                ` + CALENDAR_SETTING.timeInDay() + `
                            </select>
                        </div>
                        <button data-click="removePeriodDeny" class="btn btn-remove-period" href="javascript:void(0)">削除</button>
                    </div>

                    <div class="text-error error-denial_time_from text-danger"></div>
                    <div class="text-error error-denial_time_to text-danger"></div>
                </div>
            `;

            $('#dialog-reserve-denials .list-period').append(html);
        },

        removePeriodDeny: function (el) {
            el.closest('.item-period').remove();
        },

        openDialogDenials: function (el) {
            let route = $(el).data('route');

            showLoading();

            // get data reserve denials by shop
            $.ajax({
                url: route,
                type: 'GET',

                success: function (res) {
                    if (res.status == true) {
                        hideLoading();

                        var html = res.data.html;
                        $('#dialog-reserve-denials .list-period').html(html);

                        // show dialog
                        $("#dialog-reserve-denials").dialog({
                            width: 840,
                            maxHeight: 900,
                            title: '受入拒否時間帯一括設定',
                            dialogClass: 'custom-dialog',
                            resizable: true,
                            position: {my: "center", at: "center", of: window},
                            show: {
                                effect: "fade",
                                duration: 120,
                            },
                            hide: {
                                effect: "fade",
                                duration: 120,
                            },
                            autoOpen: true,
                            modal: true,
                            open: function (event, ui) {
                                // do something
                            },
                            create: function (event, ui) {
                                // do something
                            },
                            buttons: [
                                {
                                    text: 'キャンセル',
                                    class: 'btn btn-primary btn-cancell',
                                    click: function () {
                                        $(this).dialog('close');
                                        $('.open-dialog-denials').attr('style', 'background-color: #508FF4 !important');
                                    }
                                },
                                {
                                    text: '保存',
                                    class: 'btn btn-primary btn-save-deny',
                                    click: function () {
                                        CALENDAR_SETTING.saveReserveDenials();
                                        $('.open-dialog-denials').attr('style', 'background-color: #508FF4 !important');
                                    }
                                }]
                        });
                    }
                    hideLoading();
                },

                error: function () {
                    hideLoading();
                },
            });
        },

        saveReserveDenials: function () {
            let itemDenials = $('#dialog-reserve-denials').find('.item-period');
            let route = $('#dialog-reserve-denials').data('route-save');
            let shop_id = $('#current-shop-id').val();
            let data = [];

            // disable button
            $('.btn-save-deny').attr('disabled', 'disabled');

            showLoading();

            if (itemDenials.length > 0) {
                $.each(itemDenials, function (key, value) {
                    let id = $(value).data('id');
                    let denial_time_from = $(value).find('select[name="denial_time_from"]').val();
                    let denial_time_to = $(value).find('select[name="denial_time_to"]').val();

                    data.push({
                        id,
                        denial_time_from,
                        denial_time_to,
                    });
                });
            }

            $.ajax({
                url: route,
                type: 'POST',
                data: {
                    shop_id,
                    data,
                },

                success: function (res) {
                    if (res.status == true) {
                        hideLoading();

                        $('#dialog-reserve-denials').dialog('close');
                        CALENDAR_SETTING.showNoty('update_success');
                    } else if (res.status == 2) {
                        hideLoading();

                        // valid errors
                        if (typeof res.errors != "undefined") {
                            var itm = $('#dialog-reserve-denials .item-period');
                            $('#dialog-reserve-denials .text-error').css('display', 'none').html('');
                            itm.find('select').removeClass('input-error');

                            $.each(res.errors, function (key, value) {
                                var key_error = key.split('.');
                                var index = key_error[1];
                                var field = key_error[2];
                                $(itm[index]).find('.error-' + field).css('display', 'block').html(value[0]);
                                $(itm[index]).find('select').addClass('input-error');
                            });
                        }
                    } else if (res.success === 0) {
                        hideLoading();

                        CALENDAR_SETTING.showNoty('update_failed');
                    }

                    setTimeout(function() {
                        $('.btn-save-deny').removeAttr('disabled');
                    }, 500);
                },

                error: function (res) {
                    hideLoading();

                    $('#dialog-reserve-denials').dialog('close');
                    setTimeout(function() {
                        $('.btn-save-deny').removeAttr('disabled');
                    }, 500);
                },
            });
        },

        changeFlgReserveTel: function (el) {
            let route = $('#route-change-flag-reserve-tel').val();
            let shop_id = $('#current-shop-id').val();
            let data = $(el).attr('name').split('_');
            let reserve_date = data[1];
            let tel_flg = $(el).is(":checked") ? 1 : 0;

            showLoading();

            $.ajax({
                url: route,
                type: 'POST',
                data: {shop_id, reserve_date, tel_flg},

                success: function (res) {
                    let flg = typeof res.data.tel_flg != "undefined" ? res.data.tel_flg : 0;
                    $(el).attr('checked', flg === 1);
                    hideLoading();
                },

                error: function () {
                    hideLoading();
                },
            });
        },

        nextMonth: function (el) {
            showDate.setMonth(showDate.getMonth() + 1);
            let date = showDate.getFullYear() + '/' + (showDate.getMonth() + 1) + '/' + '1';
            CALENDAR_SETTING.ajaxRenderCalendar(date);
        },

        prevMonth: function (el) {
            showDate.setMonth(showDate.getMonth() - 1);
            let date = showDate.getFullYear() + '/' + (showDate.getMonth() + 1) + '/' + '1';
            CALENDAR_SETTING.ajaxRenderCalendar(date);
        },

        ajaxRenderCalendar: function (date) {
            showLoading();
            let route = $('#route-get-data-calendar').val();
            let shop_id = $('#current-shop-id').val();

            $.ajax({
                url: route,
                type: 'get',
                data: {date, shop_id},

                success: function (res) {
                    if (res.status == true) {
                        CALENDAR_SETTING.showProcess(showDate, res.data);
                        hideLoading();
                    }
                },

                error: function (res) {
                    hideLoading();
                },
            });
        },

        setCellClassName: function (count, tel = 0, holiday = 0) {
            var className;

            if (holiday == 1 || count == 0) {
                className = 'reserve-status-none';
            } else {
                if (count >= 5) {
                    className = 'reserve-status-1';
                } else if (count >= 3) {
                    className = 'reserve-status-2';
                } else if (count <= 2) {
                    className = 'reserve-status-3';
                }
            }

            return className;
        },

        createProcess: function (year, month, jsonDate) {
            // day of week
            var calendar = "<table><tr class='dayOfWeek'>";
            for (var i = 0; i < week.length; i++) {
                calendar += "<th>" + week[i] + "</th>";
            }

            calendar += "</tr>";

            var count = 0;
            var startDayOfWeek = new Date(year, month, 0).getDay();
            var endDate = new Date(year, month + 1, 0).getDate();
            var lastMonthEndDate = new Date(year, month, 0).getDate();
            var row = Math.ceil((startDayOfWeek + endDate) / week.length);

            // 1 Set line by line
            for (var i = 0; i < row; i++) {
                calendar += "<tr>";
                // 1 column Set in units
                for (var j = 0; j < week.length; j++) {
                    if (i == 0 && j < startDayOfWeek) {
                        // 1 Set the date of the last month up to the 1st in the line
                        // calendar += "<td class='disabled'>" + (lastMonthEndDate - startDayOfWeek + j + 1) + "</td>";
                        calendar += "<td class='disabled'></td>";
                    } else if (count >= endDate) {
                        // Set the date of the next month after the last day on the last line
                        count++;
                        // calendar += "<td class='disabled'>" + (count - endDate) + "</td>";
                        calendar += "<td class='disabled'></td>";
                    } else {
                        // Set the date of the current month against the day of the week
                        count++;
                        let count_date = year + '-' + ("0" + (month + 1)).slice(-2) + '-' + ("0" + count).slice(-2);

                        var indexDate = jsonDate.filter(function (item, index) {
                            if (item.day == count_date) return item;
                        });

                        if (indexDate.length > 0) {
                            var className = CALENDAR_SETTING.setCellClassName(indexDate[0].count, indexDate[0].tel, indexDate[0].disable);
                        } else {
                            var className = 'reserve-status-none';
                        }

                        let holidayClass = typeof indexDate[0] != "undefined" && indexDate[0].disable == constant('CLOSING_FLG.ON') ? 'status-holiday' : '';
                        let isHoliday = typeof indexDate[0] != "undefined" && indexDate[0].holiday == constant('CLOSING_FLG.ON') ? true : false;

                        if (year == today.getFullYear()
                            && month == (today.getMonth())
                            && count == today.getDate()) {
                            calendar += "<td class='today day " + className + " " + holidayClass + " '><div class='col-day'>" + count + "</div>";
                        } else {
                            calendar += "<td class='day " + className + " " + holidayClass + " '><div class='col-day'>" + count + "</div>";
                        }

                        let disableClass = typeof indexDate[0] != "undefined" && indexDate[0].disable == constant('CLOSING_FLG.ON') ? 'disabled-event' : ''

                        calendar += "<div class='col-count-btn'><a href='#' data-click='openDialogReserve' class='open-dialog btn btn-primary " + (isHoliday || disableClass ? 'disabled-event' : '') + "' data-dialog='dialog-reserve' data-btn-date='" + count_date + "'>予約数入力</a></div>"
                            + "<div class='form-checkbox'><a href='tel:" + $('#tel-shop').val() +"'>TEL</a><label class='form-checkbox-label " + (isHoliday || disableClass ? 'disabled-event' : '') + "'>"
                            + "<input data-click='changeFlgReserveTel' class='form-checkbox-input' data-id='' name='tel_" + count_date + "' " + (typeof indexDate[0] != "undefined" && indexDate[0].tel ? 'checked' : '') + " type='checkbox'><span class='form-checkbox-text'></span>"
                            + "</label></div>"
                            + "</td>";
                    }
                }

                calendar += "</tr>";
            }

            return calendar;
        },

        showProcess: function (date, dataDAte) {
            var year = date.getFullYear();
            var month = date.getMonth();
            document.querySelector('#calender-month').innerHTML = (month + 1) + "月 " + year;

            var calendar = CALENDAR_SETTING.createProcess(year, month, dataDAte);
            document.querySelector('#calendar').innerHTML = calendar;

            // handle disable prev button

            var publish_from_dt = $('#publish_from_dt').val();
            var publish_to_dt = $('#publish_to_dt').val();
            var active_from =  "";
            var active_to =  "";
            if (publish_from_dt !== ""){
                active_from = new Date(publish_from_dt);
            }
            if (publish_to_dt !== ""){
                active_to = new Date(publish_to_dt);
            }

            var prev = document.getElementById('calendar-prev-button');
            if (active_from !== ""  && new Date(active_from.getFullYear(), active_from.getMonth(), active_from.getDate())  < new Date(year, month, 0)) {
                prev.classList.remove('is-disabled');
            } else {
                prev.classList.add('is-disabled');
            }

            // handle disable next button
            var next = document.getElementById('calendar-next-button');
            if (active_to !== "") {
                if (new Date(active_to.getFullYear(), active_to.getMonth(), active_to.getDate()) > new Date(year, month + 1, 0)) {
                    next.classList.remove('is-disabled');
                } else {
                    next.classList.add('is-disabled');
                }
            }
        },

        openDialogReserve: function (e) {
            showLoading();

            let shop_id = $('#current-shop-id').val();
            let reserve_date = e.target.getAttribute("data-btn-date");
            let route = $('#route-get-reserve-operation-time').val();

            // ajax get data
            $.ajax({
                url: route,
                type: 'get',
                data: {shop_id, reserve_date},

                success: function (res) {
                    if (res.status == true) {
                        hideLoading();

                        $('#dialog-reserve-operation-time').html(res.data.html);
                        CALENDAR_SETTING.showDialogOperationTime(e);
                    }

                    hideLoading();
                },

                error: function (res) {
                    hideLoading();
                },
            });
        },

        showDialogOperationTime: function (e) {
            var date = $(e.target).data('btn-date');
            $("#dialog-reserve-operation-time").dialog({
                width: 840,
                maxHeight: 900,
                title: '予約数入力',
                dialogClass: 'custom-dialog',
                resizable: true,
                position: {my: "center", at: "center", of: window},
                show: {
                    effect: "fade",
                    duration: 120,
                },
                hide: {
                    effect: "fade",
                    duration: 120,
                },
                autoOpen: true,
                modal: true,
                open: function (event, ui) {
                },
                create: function (event, ui) {
                },
                buttons: [
                    {
                        text: 'キャンセル',
                        class: 'btn btn-primary btn-cancell',
                        click: function () {
                            $(this).dialog('close');
                            $('#calendar .col-count-btn a').attr('style', 'background-color: #508FF4 !important');
                        }
                    },
                    {
                        text: '保存',
                        class: 'btn btn-primary btn-save',
                        click: function () {
                            CALENDAR_SETTING.saveOperationTime();
                            $('#calendar .col-count-btn a').attr('style', 'background-color: #508FF4 !important');
                        }
                    }]
            });
        },

        saveOperationTime: function () {
            let route = $('#route-save-operation-time').val();
            let shop_id = $('#current-shop-id').val();
            let time_list = $('#reserve-time-list li.reserve_num_item');
            let reserve_date = $('#dialog-reserve-operation-time input[name="reserve_date"]').val();

            let data = [];
            if (time_list.length > 0) {
                $.each(time_list, function (key, value) {
                    data.push({
                        'id': $(value).data('id'),
                        'reserve_time': $(value).find('.time-range').data('from'),
                        'reserve_num': $(value).find('input[type="number"]').val(),
                    });
                });
            }

            showLoading();

            $.ajax({
                url: route,
                type: 'post',
                data: {shop_id, data, reserve_date},

                success: function (res) {
                    if (res.status === true) {
                        hideLoading();
                        $('#dialog-reserve-operation-time').dialog('close');
                        CALENDAR_SETTING.showNoty('update_success');

                        // show stock after create or update
                        let className = CALENDAR_SETTING.setCellClassName(res.data.stock);
                        let thisTd = $('#calendar').find($('#calendar').find('a[data-btn-date="' + reserve_date + '"]')).closest('td');
                        thisTd.removeClass('reserve-status-1 reserve-status-2 reserve-status-3 reserve-status-none').addClass(className);
                    } else if (res.status == 2) { // case validator failed
                        hideLoading();

                        var itm = $('#dialog-reserve-operation-time .reserve-time-list li.reserve_num_item');
                        var error = $('#dialog-reserve-operation-time .reserve-time-list li.text-error');
                        itm.find('input[type="number"]').removeClass('border-error');
                        error.find('div').html('');

                        $.each(res.errors, function (key, value) {
                            var key_error = key.split('.');
                            var index = key_error[1];
                            var field = key_error[2];

                            if (field === 'reserve_num') {
                                $(itm[index]).find('input[type="number"]').addClass('border-error').focus();
                                $(error[index]).find('div').html(value[0]);
                                $(error[index]).attr('style', 'display: block !important');
                            }
                        });
                    }

                    hideLoading();
                },

                error: function (res) {
                    hideLoading();

                    $('#dialog-reserve-operation-time').dialog('close');
                    CALENDAR_SETTING.showNoty('update_failed');
                },
            });
        },

        showNoty: function (type) {
            let msg = {
                'update_success': '編集しました。',
                'update_failed': '更新に失敗しました。',
            };

            let success = $('#success_msg_custom');
            let error = $('#error_msg_custom');

            switch (type) {
                case 'update_success':
                    success.find('strong').html(msg.update_success);
                    success.attr('style', 'display: block !important');
                    break;
                case 'update_failed':
                    error.find('strong').html(msg.update_failed);
                    error.attr('style', 'display: block !important');
                    break;
                default:
                    break;
            }

            setTimeout(function () {
                $('#success_msg_custom').attr('style', 'display: none !important');
                $('#error_msg_custom').attr('style', 'display: none !important');
            }, 3000);
        },

        timeInDay: function () {
            return `
                <option value=""></option>
                <option value="00:00">00:00</option>
                <option value="00:30">00:30</option>
                <option value="01:00">01:00</option>
                <option value="01:30">01:30</option>
                <option value="02:00">02:00</option>
                <option value="02:30">02:30</option>
                <option value="03:00">03:00</option>
                <option value="03:30">03:30</option>
                <option value="04:00">04:00</option>
                <option value="04:30">04:30</option>
                <option value="05:00">05:00</option>
                <option value="05:30">05:30</option>
                <option value="06:00">06:00</option>
                <option value="06:30">06:30</option>
                <option value="07:00">07:00</option>
                <option value="07:30">07:30</option>
                <option value="08:00">08:00</option>
                <option value="08:30">08:30</option>
                <option value="09:00">09:00</option>
                <option value="09:30">09:30</option>
                <option value="10:00">10:00</option>
                <option value="10:30">10:30</option>
                <option value="11:00">11:00</option>
                <option value="11:30">11:30</option>
                <option value="12:00">12:00</option>
                <option value="12:30">12:30</option>
                <option value="13:00">13:00</option>
                <option value="13:30">13:30</option>
                <option value="14:00">14:00</option>
                <option value="14:30">14:30</option>
                <option value="15:00">15:00</option>
                <option value="15:30">15:30</option>
                <option value="16:00">16:00</option>
                <option value="16:30">16:30</option>
                <option value="17:00">17:00</option>
                <option value="17:30">17:30</option>
                <option value="18:00">18:00</option>
                <option value="18:30">18:30</option>
                <option value="19:00">19:00</option>
                <option value="19:30">19:30</option>
                <option value="20:00">20:00</option>
                <option value="20:30">20:30</option>
                <option value="21:00">21:00</option>
                <option value="21:30">21:30</option>
                <option value="22:00">22:00</option>
                <option value="22:30">22:30</option>
                <option value="23:00">23:00</option>
                <option value="23:30">23:30</option>
            `;
        }
    };
});
