<?php

namespace App\Http\Controllers\Backend\Auth;

use App\Http\Controllers\Base\BackendController;
use App\Repositories\AdminUserInfoRepository;
use App\Repositories\ShopInfoRepository;
use Illuminate\Support\MessageBag;
use Illuminate\Support\Facades\Request;
use Validator;

class LoginController extends BackendController
{
    protected $_area = 'backend';

    protected $_userLoginHistoryRepository = null;

    /**
     * @return null
     */
    public function getUserLoginHistoryRepository()
    {
        return $this->_userLoginHistoryRepository;
    }

    /**
     * @param null $userLoginHistoryRepository
     */
    public function setUserLoginHistoryRepository($userLoginHistoryRepository)
    {
        $this->_userLoginHistoryRepository = $userLoginHistoryRepository;
    }

    public function __construct(AdminUserInfoRepository $userInfoRepository, ShopInfoRepository $shopInfoRepository)
    {
        $this->setRepository($userInfoRepository);
        $this->setRepository($shopInfoRepository);
        parent::__construct();
    }

    public function showLoginForm()
    {
        if (backendGuard()->check() || shopGuard()->check()) {
            return $this->_redirectToHome();
        }
        $this->setTitle(getConfig('page_title.backend.auth'));
        $this->setEntity($this->_findOrNewEntity());
        return $this->render('backend.auth.login');
    }

    public function login()
    {

        $validator = $this->getRepository()->getValidator();
        if (!$validator->validateLogin(Request::all())) {
            return $this->_backWithError($validator->errors());
        }

        $userData = array(
            'email' => request()->get('email'),
            'password' => request()->get('password')
        );

        $shopData = array(
            'shop_user_email' => request()->get('email'),
            'password' => request()->get('password')
        );

        if (backendGuard()->attempt($userData) || shopGuard()->attempt($shopData)) {
            return $this->_redirectToHome();
        }
        $errors = new MessageBag(['login_password' => [trans('auth.email_password_invalid')]]);
        return $this->_backWithError($errors);
    }

    protected function _backWithError($errors)
    {
        return $this->_back()
            ->withErrors($errors)// send back all errors to the login form
            ->withInput(Request::except('password')); // send back the input (not the password) so that we can repopulate the form
    }

    public function logout()
    {
        backendGuard()->logout(); // log the user out of our application
        shopGuard()->logout();
        return $this->_redirectToHome();
    }

    protected function _redirectToHome()
    {
        $url = request()->get('return_url');
        if (empty($url)) {
            if (backendGuard()->check()) {
                $url = buildListShopUrl();
            } elseif(shopGuard()->check()) {
                $url = buildShopInfoUrl();
            } else {
                $params = ['return_url' => buildDashBoardUrl()];
                $url = route('backend.login', $params);
            }
        }
        return $this->_to($url);
    }
}
