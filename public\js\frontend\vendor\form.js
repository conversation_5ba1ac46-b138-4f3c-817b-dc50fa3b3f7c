$(function () {



  /* alert
   * --------------------------------------------------------- */
   function confirmDeleteDisp() {
     if(window.confirm('登録がある場合、空欄となります。本当に削除しますか？')){
       window.alert('削除しました。');
 	   }
 	   else {
 		   window.alert('キャンセルされました。');
     }
   }
   $('.js-confirm-delete').on('click',function(){
     confirmDeleteDisp();
   });



   var calendarWeek = document.getElementById('calendar-week');
   if (calendarWeek != null) {
     var week_calendar = new FullCalendar.Calendar(calendarWeek, {
       initialView: 'dayGridWeek',
       locale: 'ja',
       headerToolbar: {
         left: 'prev',
         center: 'title',
         right: 'next'
       },
       dayMaxEvents: true,
       dayCellContent: function (e) {
         e.dayNumberText = e.dayNumberText.replace('日', '');
       },
       eventSources: [
         {
           url: '/luminous/assets/js/event.php',
           method: 'POST',
           failure: function() {
             alert('there was an error while fetching events!');
           },
         }
       ],
       eventClick: function(info) {
         console.log(info.event.start);
       },
       datesSet: function (dateInfo) {
         var today = new Date();
             today.setHours(0);
             today.setMinutes(0);
             today.setSeconds(0);
             today.setMilliseconds(0);
         var minDate = today.setDate(1), //今の月より前の月は表示しない
   		      maxDate = today.setMonth(today.getMonth() + 2), //最大で表示する未来の月を設定
             maxDate = today.setDate(0);
         var viewDate = week_calendar.view.currentStart;
         var minDate = new Date(minDate),
             maxDate = new Date(maxDate);
     		// Past
     		if (minDate >= viewDate) {
     			$(".fc-prev-button").prop('disabled', true);
     			$(".fc-prev-button").addClass('fc-state-disabled');
     		}
     		else {
     			$(".fc-prev-button").removeClass('fc-state-disabled');
     			$(".fc-prev-button").prop('disabled', false);
     		}
     		// Future
     		if (maxDate <= viewDate) {
     			$(".fc-next-button").prop('disabled', true);
     			$(".fc-next-button").addClass('fc-state-disabled');
     		} else {
     			$(".fc-next-button").removeClass('fc-state-disabled');
     			$(".fc-next-button").prop('disabled', false);
     		}
       }
     });
     week_calendar.render();
   }
});
