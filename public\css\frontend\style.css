@charset "UTF-8";
body {
    font-family: "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック体", "Yu Gothic", <PERSON><PERSON><PERSON><PERSON>, "メイリオ", Meiryo, Osaka, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif;
    margin: 0;
    padding: 0;
    color: #515C6F;
    font-size: 13px;
    position: relative;
}

/* =============================================
 *  01. Common settings
 *      - reset
 *      - common
 *      - padding
 *      - margin
 *      - width
 *  02. Layout
 *      - common
 *      - clearfix
 *  03. Font
 *  04. Parts
 *      - button
 *  05. Form
 *      - parts
 *  06. Calender
 * ============================================= */
/* ---------------------------------------------------------------------------------------- */
/* ==============================================
 01. Common settings
=============================================== */
/* reset
----------------------------------------------- */
html, body, div, span, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
abbr, address, cite, code,
del, dfn, em, img, ins, kbd, q, samp,
small, strong, sub, sup, var,
b, i,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, figcaption, figure,
footer, header, hgroup, menu, nav, section, summary,
time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    font-size: 100%;
    vertical-align: bottom;
}

article, aside, details, figcaption, figure, main,
footer, header, hgroup, menu, nav, section {
    display: block;
}

nav ul {
    list-style: none;
}

blockquote, q {
    quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
    content: '';
    content: none;
}

a {
    margin: 0;
    padding: 0;
    font-size: 100%;
    vertical-align: baseline;
    background: transparent;
}

/* change colours to suit your needs */
ins {
    background-color: #ff9;
    color: #000;
    text-decoration: none;
}

/* change colours to suit your needs */
mark {
    background-color: #ff9;
    color: #000;
    font-style: italic;
    font-weight: bold;
}

del {
    text-decoration: line-through;
}

abbr[title], dfn[title] {
    border-bottom: 1px dotted;
    cursor: help;
}

table {
    border-collapse: separate;
    border-spacing: 0;
}

/* change border colour to suit your needs */
hr {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px solid #cccccc;
    margin: 1em 0;
    padding: 0;
}

input, select {
    vertical-align: middle;
}

span {
    vertical-align: baseline;
}

/* common
----------------------------------------------- */
*, *:before, *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -o-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
}

html {
    font-size: 62.5%;
    overflow-y: scroll;
    margin: 0;
    width: 100%;
}

body {
    background: #fff;
    color: #515C6F;
    font-family: "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック体", "Yu Gothic", YuGothic, "メイリオ", Meiryo, Osaka, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif;
    font-size: 14px;
    font-size: 1.4rem;
    font-weight: normal;
    font-weight: 400;
    letter-spacing: 0;
    margin: 0;
    padding: 0;
    -webkit-text-size-adjust: 100%;
    width: 100%;
}

@media (max-width: 767px) {
    body {
        font-size: 13px;
        font-size: 1.3rem;
    }
}

.preload * {
    -webkit-transition: none 0.14s ease-out;
    -moz-transition: none 0.14s ease-out;
    -ms-transition: none 0.14s ease-out;
    -o-transition: none 0.14s ease-out;
    transition: none 0.14s ease-out;
}

a {
    color: #515C6F;
    outline: medium none;
    text-decoration: none;
}

a:visited {
    outline: medium none;
}

a:focus {
    outline: medium none;
}

a:active, a:hover {
    outline: medium none;
}

a:hover {
    color: #515C6F;
    text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
    clear: both;
    font-weight: bold;
    font-weight: 600;
    margin: 0;
}

address {
    font-style: italic;
    margin: 0 0 24px;
}

abbr[title] {
    border-bottom: 1px dotted;
}

b, strong {
    font-weight: bold;
}

dfn {
    font-style: italic;
}

mark {
    background: none repeat scroll 0 0 #FFFF00;
    color: #000000;
}

p {
    line-height: 1.6;
    margin: 0 0 24px;
    max-height: 100%;
}

code, kbd, pre, samp {
    -moz-hyphens: none;
    font-family: monospace,serif;
    font-size: 14px;
}

pre {
    background: none repeat scroll 0 0 #F5F5F5;
    color: #666666;
    font-family: monospace;
    font-size: 14px;
    margin: 20px 0;
    overflow: auto;
    padding: 20px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

blockquote, q {
    -moz-hyphens: none;
    quotes: none;
}

blockquote:before, blockquote:after, q:before, q:after {
    content: none;
}

blockquote {
    font-size: 18px;
    font-style: italic;
    font-weight: 300;
    margin: 24px 40px;
}

blockquote blockquote {
    margin-right: 0;
}

blockquote cite, blockquote small {
    font-size: 14px;
    font-weight: normal;
    text-transform: uppercase;
}

blockquote em, blockquote i {
    font-style: normal;
    font-weight: 300;
}

blockquote strong, blockquote b {
    font-weight: 400;
}

small {
    font-size: 13px;
    font-size: 1.3rem;
}

sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

dl {
    margin: 0;
}

dt {
    line-height: 1.8;
    font-weight: bold;
    font-weight: 600;
    margin: 0;
}

dd {
    line-height: 1.8;
    margin: 0;
}

menu, ol, ul {
    margin: 0;
    padding: 0;
}

ul {
    list-style-type: none;
}

li {
    line-height: 1.8;
}

th {
    font-weight: bold;
}

img {
    border: 0 none;
    height: auto;
    max-width: 100%;
    vertical-align: middle;
    -webkit-backface-visibility: hidden;
}

input[type="text"]:focus,
textarea:focus {
    outline: 0;
}

a[href^="tel:"] {
    cursor: default;
    pointer-events: none;
}

/* ==============================================
 02. Layout
=============================================== */
/* Common
----------------------------------------------- */
.content {
    padding-top: 50px;
    padding-bottom: 50px;
}

.container {
    padding-right: 25px;
    padding-left: 25px;
    max-width: 1060px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
}

@media (max-width: 767px) {
    .container {
        padding-left: 12px;
        padding-right: 12px;
    }
}

.flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
}

.flex-wrap {
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.align-items-center {
    -webkit-align-items: center;
    -moz-align-items: center;
    -ms-align-items: center;
    align-items: center;
}

.justify-content-center {
    -webkit-justify-content: center;
    -moz-justify-content: center;
    -ms-justify-content: center;
    justify-content: center;
}

/* clearfix
----------------------------------------------- */
.cf:before,
.cf:after {
    content: " ";
    display: table;
}

.cf:after {
    clear: both;
}

.cf {
    *zoom: 1;
}

/* ==============================================
 03. Font
=============================================== */
/* ==============================================
 04. Parts
=============================================== */
/* button
----------------------------------------------- */
/* page title
----------------------------------------------- */
.page-title {
    margin-bottom: 2rem;
}

@media (max-width: 767px) {
    .page-title {
        margin-bottom: 1.5rem;
    }
}

/* heading
----------------------------------------------- */
.heading-1 {
    font-size: 24px;
    font-weight: bold;
    line-height: 1.25;
    position: relative;
}

@media (max-width: 767px) {
    .heading-1 {
        font-size: 18px;
    }
}

/* scrollbox
----------------------------------------------- */
.scroll-area {
    overflow: auto;
    position: relative;
}

.scroll-area.ps--active-x {
    padding-bottom: 10px;
}

.scroll-area.ps--active-x .ps__rail-x {
    opacity: 1;
}

.scroll-area.ps--active-x .ps__rail-x .ps__thumb-x {
    height: 10px;
    height: 4px;
}

/* ==============================================
 05. Form
=============================================== */
/* parts
----------------------------------------------- */
.form-message {
    font-size: 120%;
}

@media (max-width: 767px) {
    .form-message {
        font-size: 100%;
    }
}

/* ==============================================
 06. Calendar
=============================================== */
@media (max-width: 767px) {
    .reservation-calender .fc .fc-view-harness {
        position: relative;
        margin-left: -12px;
        margin-right: -12px;
    }
}

.reservation-calender .fc .fc-toolbar-title {
    font-family: "Lato", sans-serif;
    font-size: 26px;
}

@media (max-width: 767px) {
    .reservation-calender .fc .fc-toolbar-title {
        font-size: 20px;
    }
}

.reservation-calender .fc .fc-col-header-cell {
    background-color: #E5E6E6;
    font-weight: normal;
    border-color: #DEDEDE;
}

.reservation-calender .fc .fc-theme-standard td,
.reservation-calender .fc .fc-theme-standard th {
    border-color: #DEDEDE;
}

.reservation-calender .fc .fc-daygrid-day {
    font-family: "Lato", sans-serif;
    font-size: 106%;
    background-position: 50% 70%;
    background-repeat: no-repeat;
}

.reservation-calender .fc .fc-daygrid-day.reserve-status-1 {
    background-image: url("images/day_mark01.svg");
    background-size: 25px 25px;
}

.reservation-calender .fc .fc-daygrid-day.reserve-status-2 {
    background-image: url("images/day_mark02.svg");
    background-size: 25px 25px;
}

.reservation-calender .fc .fc-daygrid-day.reserve-status-3 {
    background-image: url("images/day_mark03.svg");
    background-size: 24px 24px;
}

.reservation-calender .fc .fc-daygrid-day.reserve-status-none {
    background-image: url("images/day_mark04.svg");
    background-size: 18px 18px;
}

.reservation-calender .fc .fc-daygrid-day.reserve-status-tel {
    background-image: url("images/day_mark05.svg");
    background-size: 30px 13px;
}

@media (max-width: 767px) {
    .reservation-calender .fc .fc-daygrid-day.reserve-status-tel {
        background-size: 26px 11px;
    }
}

.reservation-calender .fc .fc-daygrid-day.fc-day-past {
    background-color: #F2F2F2;
    background-image: url("images/day_mark04.svg");
    background-size: 20px 20px;
}

.reservation-calender .fc .fc-daygrid-day.fc-day-past .fc-daygrid-day-top {
    opacity: 1;
}

.reservation-calender .fc .fc-daygrid-day.fc-day-future .fc-daygrid-day-top {
    opacity: 1;
}

.reservation-calender .fc .fc-daygrid-day.fc-day-today {
    background-color: #fff;
}

.reservation-calender .fc .fc-daygrid-day.reserve-active-day .fc-daygrid-day-frame::before {
    border: 2px solid #FD6066;
    content: "";
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    width: 100%;
}

.reservation-calender .fc .fc-daygrid-day-top {
    text-align: center;
    display: block;
    padding-top: .75rem;
}

.reservation-calender .fc .fc-toolbar-chunk .fc-button {
    border: none;
    font-weight: bold;
    color: #515C6F;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: 6px 10px;
    width: 120px;
    -webkit-box-shadow: 0 2px 4px rgba(81, 92, 111, 0.3);
    -moz-box-shadow: 0 2px 4px rgba(81, 92, 111, 0.3);
    -ms-box-shadow: 0 2px 4px rgba(81, 92, 111, 0.3);
    box-shadow: 0 2px 4px rgba(81, 92, 111, 0.3);
}

@media (max-width: 767px) {
    .reservation-calender .fc .fc-toolbar-chunk .fc-button {
        width: auto;
    }
}

.reservation-calender .fc .fc-toolbar-chunk .fc-button:active {
    box-shadow: none;
}

.reservation-calender .fc .fc-toolbar-chunk .fc-prev-button {
    background-image: url("images/arrow_prev.svg");
    background-position: left 10px top 50%;
}

@media (max-width: 767px) {
    .reservation-calender .fc .fc-toolbar-chunk .fc-prev-button {
        padding-left: 20px;
    }
}

.reservation-calender .fc .fc-toolbar-chunk .fc-next-button {
    background-image: url("images/arrow_next.svg");
    background-position: right 10px top 50%;
}

@media (max-width: 767px) {
    .reservation-calender .fc .fc-toolbar-chunk .fc-next-button {
        padding-right: 20px;
    }
}

.reservation-time-select {
    margin-top: 4rem;
}

@media (max-width: 767px) {
    .reservation-time-select {
        margin-top: 3rem;
    }
}

.reservation-time-select .time-select-title {
    font-family: "Lato", sans-serif;
    font-size: 135%;
    margin-bottom: 1.75rem;
    font-weight: normal;
}

@media (max-width: 767px) {
    .reservation-time-select .time-select-title {
        font-size: 120%;
        margin-bottom: 1rem;
    }
}

.reservation-time-select .time-select-list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

@media (max-width: 767px) {
    .reservation-time-select .time-select-list {
        display: block;
    }
}

.reservation-time-select .time-select-list li {
    margin-right: 10px;
    margin-bottom: 10px;
}

@media (max-width: 767px) {
    .reservation-time-select .time-select-list li {
        margin-right: auto;
    }
}

.reservation-time-select .time-select-list li:last-child {
    margin-right: auto;
}

.reservation-time-select .time-select-list li a {
    background-color: #fff;
    background-position: 50% 70%;
    background-repeat: no-repeat;
    border: 1px solid #ccc;
    display: block;
    font-family: "Lato", sans-serif;
    font-size: 107%;
    height: 104px;
    text-align: center;
    width: 120px;
}

@media (max-width: 767px) {
    .reservation-time-select .time-select-list li a {
        background-position: 12px 50%;
        height: auto;
        text-align: left;
        padding: 12px 12px 12px 50px;
        position: relative;
        width: auto;
    }
    .reservation-time-select .time-select-list li a::after {
        right: 14px;
        content: "";
        position: absolute;
        top: 50%;
        margin-top: -5px;
        width: 10px;
        height: 10px;
        border-top: 1px solid #2680EB;
        border-right: 1px solid #2680EB;
        -webkit-transform: rotate(45deg);
        -moz-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        -o-transform: rotate(45deg);
        transform: rotate(45deg);
    }
}

.reservation-time-select .time-select-list li a.reserve-status-1 {
    background-image: url("images/day_mark01.svg");
    background-size: 25px 25px;
}

.reservation-time-select .time-select-list li a.reserve-status-2 {
    background-image: url("images/day_mark02.svg");
    background-size: 25px 25px;
}

.reservation-time-select .time-select-list li a.reserve-status-3 {
    background-image: url("images/day_mark03.svg");
    background-size: 24px 24px;
}

.reservation-time-select .time-select-list li a.reserve-status-none {
    background-image: url("images/day_mark04.svg");
    background-size: 18px 18px;
}

.reservation-time-select .time-select-list li a.reserve-status-tel {
    background-image: url("images/day_mark05.svg");
    background-size: 30px 13px;
}

@media (max-width: 767px) {
    .reservation-time-select .time-select-list li a.reserve-status-tel {
        background-size: 26px 11px;
    }
}

.reservation-time-select .time-select-list li a .time-range {
    border-bottom: 1px solid #ccc;
    background-color: #F2F2F2;
    display: block;
    padding: 2px;
}

@media (max-width: 767px) {
    .reservation-time-select .time-select-list li a .time-range {
        border-bottom: none;
        background-color: inherit;
        padding: 0;
    }
}

/* ==============================================
 Media Queries
=============================================== */
@media screen and (max-width: 767px) {
    a[href^="tel:"] {
        cursor: pointer;
        pointer-events: auto;
    }
}

/* ==============================================
 Media Queries
=============================================== */
/* hidden
----------------------------------------------- */
@media screen and (min-width: 992px) {
    .hidden-lg {
        display: none !important;
    }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
    .hidden-md {
        display: none !important;
    }
}

@media screen and (min-width: 544px) and (max-width: 767px) {
    .hidden-sm {
        display: none !important;
    }
}

@media screen and (max-width: 543px) {
    .hidden-xs {
        display: none !important;
    }
}

/* column
----------------------------------------------- */
.col-lg-1 {
    width: 12%;
}

.col-lg-2 {
    width: 16.6666%;
}

.col-lg-3 {
    width: 25%;
}

.col-lg-4 {
    width: 33.3333%;
}

.col-lg-5 {
    width: 41.6666%;
}

.col-lg-6 {
    width: 50%;
}

.col-lg-7 {
    width: 57.0833%;
}

.col-lg-8 {
    width: 66.6667%;
}

.col-lg-9 {
    width: 75%;
}

.col-lg-10 {
    width: 83.3334%;
}

.col-lg-11 {
    width: 91.6666%;
}

.col-lg-12 {
    width: 100%;
}

/* pc min
----------------------------------------------- */
@media screen and (max-width: 1119px) {
    /* column
    ----------------------------------------------- */
    .col-md-1 {
        width: 12%;
    }
    .col-md-2 {
        width: 16.6666%;
    }
    .col-md-3 {
        width: 25%;
    }
    .col-md-4 {
        width: 33.3333%;
    }
    .col-md-5 {
        width: 41.6666%;
    }
    .col-md-6 {
        width: 50%;
    }
    .col-md-7 {
        width: 57.0833%;
    }
    .col-md-8 {
        width: 66.6667%;
    }
    .col-md-9 {
        width: 75%;
    }
    .col-md-10 {
        width: 83.3334%;
    }
    .col-md-11 {
        width: 91.6666%;
    }
    .col-md-12 {
        width: 100%;
    }
}

/* tablet
----------------------------------------------- */
@media screen and (max-width: 991px) {
    body.sp-nav-open {
        overflow: hidden !important;
    }
    /* ==============================================
       Layout
      =============================================== */
    /* column
      ----------------------------------------------- */
    .col-sm-1 {
        width: 12%;
    }
    .col-sm-2 {
        width: 16.6666%;
    }
    .col-sm-3 {
        width: 25%;
    }
    .col-sm-4 {
        width: 33.3333%;
    }
    .col-sm-5 {
        width: 41.6666%;
    }
    .col-sm-6 {
        width: 50%;
    }
    .col-sm-7 {
        width: 57.0833%;
    }
    .col-sm-8 {
        width: 66.6667%;
    }
    .col-sm-9 {
        width: 75%;
    }
    .col-sm-10 {
        width: 83.3334%;
    }
    .col-sm-11 {
        width: 91.6666%;
    }
    .col-sm-12 {
        width: 100%;
    }
}

/* column
----------------------------------------------- */
@media screen and (max-width: 767px) {
    /* column
      ----------------------------------------------- */
    .col-xs-1 {
        width: 12%;
    }
    .col-xs-2 {
        width: 16.6666%;
    }
    .col-xs-3 {
        width: 25%;
    }
    .col-xs-4 {
        width: 33.3333%;
    }
    .col-xs-5 {
        width: 41.6666%;
    }
    .col-xs-6 {
        width: 50%;
    }
    .col-xs-7 {
        width: 57.0833%;
    }
    .col-xs-8 {
        width: 66.6667%;
    }
    .col-xs-9 {
        width: 75%;
    }
    .col-xs-10 {
        width: 83.3334%;
    }
    .col-xs-11 {
        width: 91.6666%;
    }
    .col-xs-12 {
        width: 100%;
    }
}

@media print {
    body {
        zoom: 75%;
    }
}

.reservation-calender .fc .fc-daygrid-day.reserve-status-1.reserve-active-day,
.reservation-calender .fc .fc-daygrid-day.reserve-status-2.reserve-active-day,
.reservation-calender .fc .fc-daygrid-day.reserve-status-3.reserve-active-day,
.reservation-calender .fc .fc-daygrid-day.reserve-status-none.reserve-active-day,
.reservation-calender .fc .fc-daygrid-day.reserve-status-tel.reserve-active-day
{
    background-color: #f5cccd;
}

tbody[role="presentation"] {
    cursor: pointer;
}
