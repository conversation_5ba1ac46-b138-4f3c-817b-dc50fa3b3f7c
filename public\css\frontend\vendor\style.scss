@charset "utf-8";

// color
$FONT_COLOR: #515C6F;
$DEEP_COLOR: #111111;
$LINK_COLOR: #2680EB;
$MAIN_COLOR: #2680EB;
$DANGER_COLOR: #C90303;
$BORDER_COLOR: #DEDEDE;
$BASE_COLOR: #EAF2FF;
$MUTE_COLOR: #777777;
$FORM_COLOR: #6F6F6F;


// font family
$FONT_FAMILY_SANS: "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "ヒラギノ角ゴ Pro", "Hiragino Kaku Gothic Pro", "游ゴシック Medium", "Yu Gothic Medium", "游ゴシック体", "Yu Gothic", YuGothic, "メイリオ", Meiryo, Osaka, "ＭＳ Ｐゴシック", "MS PGothic", sans-serif;
$FONT_FAMILY_EN: 'Lato', sans-serif;

body {
  font-family: $FONT_FAMILY_SANS;
  margin: 0;
  padding: 0;
  color: $FONT_COLOR;
  font-size: 13px;
  position: relative;
}

// media query
$lg: 1740px;
$md: 1439px;
$minpc: 1279px;
$default: 1000px;
$tab: 1120px;
$mintab: 940px;
$sp: 767px;
$xsp: 360px;
@mixin lgpc {
  @media (max-width: ($lg)) {
    @content;
  }
}
@mixin mdpc {
  @media (max-width: ($md)) {
    @content;
  }
}
@mixin minpc {
  @media (max-width: ($minpc)) {
    @content;
  }
}
@mixin inner {
  @media (max-width: ($default)) {
    @content;
  }
}
@mixin pc {
  @media (min-width: ($tab+1)) {
    @content;
  }
}
@mixin tab {
  @media (max-width: ($tab)) {
    @content;
  }
}
@mixin mintab {
  @media (max-width: ($mintab)) {
    @content;
  }
}
@mixin sp {
  @media (max-width: ($sp)) {
    @content;
  }
}
@mixin xsp {
  @media (max-width: ($xsp)) {
    @content;
  }
}

// mixin
@mixin transition($property: all, $time: 0.14s, $timing: ease-out) {
  -webkit-transition: $property $time $timing;
  -moz-transition: $property $time $timing;
  -ms-transition: $property $time $timing;
  -o-transition: $property $time $timing;
  transition: $property $time $timing;
}

@mixin transform($value) {
  -webkit-transform: $value;
  -moz-transform: $value;
  -ms-transform: $value;
  -o-transform: $value;
  transform: $value;
}

@mixin text-shadow($value...) {
  -webkit-text-shadow: $value;
  -moz-text-shadow: $value;
  -ms-text-shadow: $value;
  text-shadow: $value;
}

@mixin box-shadow($value...) {
  -webkit-box-shadow: $value;
  -moz-box-shadow: $value;
  -ms-box-shadow: $value;
  box-shadow: $value;
}

@mixin border-radius($value: 4px) {
  -webkit-border-radius: $value;
  -moz-border-radius: $value;
  -ms-border-radius: $value;
  border-radius: $value;
}

@mixin flex() {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

@mixin flex-wrap($value: wrap) {
  -webkit-flex-wrap: $value;
  -ms-flex-wrap: $value;
  flex-wrap: $value;
}

@mixin align-items($value: center) {
  -webkit-align-items: $value;
  -moz-align-items: $value;
  -ms-align-items: $value;
  align-items: $value;
}

@mixin justify-content($value: center) {
  -webkit-justify-content: $value;
  -moz-justify-content: $value;
  -ms-justify-content: $value;
  justify-content: $value;
}

@mixin clearfix {
  &::after {
      content: "";
      display: block;
      clear: both;
  }
}

@mixin next-arrow($color: $MAIN_COLOR, $size: 8px, $border: 2px) {
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -$size / 2;
  width: $size;
  height: $size;
  border-top: $border solid $color;
  border-right: $border solid $color;
  @include transform(rotate(45deg));
}

@mixin prev-arrow($color: $MAIN_COLOR, $size: 8px, $border: 2px) {
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -$size / 2;
  width: $size;
  height: $size;
  border-bottom: $border solid $color;
  border-left: $border solid $color;
  @include transform(rotate(45deg));
}

@mixin linear-gradient($top_color: #40b2ff, $bottom_color: #0087e2) {
  background: $top_color; /* Old browsers */
  background: -moz-linear-gradient(top, $top_color 0%, $bottom_color 100%); /* FF3.6-15 */
  background: -webkit-linear-gradient(top,$top_color 0%, $bottom_color 100%); /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, $top_color 0%, $bottom_color 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}


/* =============================================
 *  01. Common settings
 *      - reset
 *      - common
 *      - padding
 *      - margin
 *      - width
 *  02. Layout
 *      - common
 *      - clearfix
 *  03. Font
 *  04. Parts
 *      - button
 *  05. Form
 *      - parts
 *  06. Calender
 * ============================================= */
/* ---------------------------------------------------------------------------------------- */

/* ==============================================
 01. Common settings
=============================================== */
/* reset
----------------------------------------------- */
html, body, div, span, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
abbr, address, cite, code,
del, dfn, em, img, ins, kbd, q, samp,
small, strong, sub, sup, var,
b, i,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, figcaption, figure,
footer, header, hgroup, menu, nav, section, summary,
time, mark, audio, video {
    margin:0;
    padding:0;
    border:0;
    outline:0;
    font-size:100%;
    vertical-align:bottom;
}
body {
}
article,aside,details,figcaption,figure,main,
footer,header,hgroup,menu,nav,section {
    display:block;
}
nav ul {
    list-style:none;
}
blockquote, q {
    quotes:none;
}
blockquote:before, blockquote:after,
q:before, q:after {
    content:'';
    content:none;
}
a {
    margin:0;
    padding:0;
    font-size:100%;
    vertical-align:baseline;
    background:transparent;
}
/* change colours to suit your needs */
ins {
    background-color:#ff9;
    color:#000;
    text-decoration:none;
}
/* change colours to suit your needs */
mark {
    background-color:#ff9;
    color:#000;
    font-style:italic;
    font-weight:bold;
}
del {
    text-decoration: line-through;
}
abbr[title], dfn[title] {
    border-bottom:1px dotted;
    cursor:help;
}
table {
    border-collapse: separate;
    border-spacing:0;
}
/* change border colour to suit your needs */
hr {
    display:block;
    height:1px;
    border:0;
    border-top:1px solid #cccccc;
    margin:1em 0;
    padding:0;
}
input, select {
    vertical-align:middle;
}
span {
  vertical-align: baseline;
}

/* common
----------------------------------------------- */
*, *:before, *:after {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
       -o-box-sizing: border-box;
      -ms-box-sizing: border-box;
          box-sizing: border-box;
}

html {
  font-size: 62.5%;
  overflow-y: scroll;
  margin: 0;
  width: 100%;
}

body {
  background: #fff;
  color: $FONT_COLOR;
  font-family: $FONT_FAMILY_SANS;
  font-size: 14px;
  font-size: 1.4rem;
  font-weight: normal;
  font-weight: 400;
  letter-spacing: 0;
  margin: 0;
  padding: 0;
  -webkit-text-size-adjust: 100%;
  width: 100%;
  @include sp {
    font-size: 13px;
    font-size: 1.3rem;
  }
}

.preload * {
  @include transition(none);
}

a {
    color: $FONT_COLOR;
    outline: medium none;
    text-decoration: none;
}

a::before, a::after {
}

a:visited {
    outline: medium none;
}

a:focus {
    outline: medium none;
}

a:active, a:hover {
    outline: medium none;
}

a:hover {
  color: $FONT_COLOR;
  text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
    clear: both;
    font-weight: bold;
    font-weight: 600;
    margin: 0;
}

h1 {
}

h2 {
}

h3 {
}

h4 {
}

h5 {
}

h6 {
}

address {
    font-style: italic;
    margin: 0 0 24px;
}

abbr[title] {
    border-bottom: 1px dotted;
}

b, strong {
    font-weight: bold;
}

dfn {
    font-style: italic;
}

mark {
    background: none repeat scroll 0 0 #FFFF00;
    color: #000000;
}

p {
    line-height: 1.6;
    margin: 0 0 24px;
    max-height: 100%;
}

code, kbd, pre, samp {
    -moz-hyphens: none;
    font-family: monospace,serif;
    font-size: 14px;
}

pre {
    background: none repeat scroll 0 0 #F5F5F5;
    color: #666666;
    font-family: monospace;
    font-size: 14px;
    margin: 20px 0;
    overflow: auto;
    padding: 20px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

blockquote, q {
    -moz-hyphens: none;
    quotes: none;
}

blockquote:before, blockquote:after, q:before, q:after {
    content: none;
}

blockquote {
    font-size: 18px;
    font-style: italic;
    font-weight: 300;
    margin: 24px 40px;
}

blockquote blockquote {
    margin-right: 0;
}

blockquote cite, blockquote small {
    font-size: 14px;
    font-weight: normal;
    text-transform: uppercase;
}

blockquote em, blockquote i {
    font-style: normal;
    font-weight: 300;
}

blockquote strong, blockquote b {
    font-weight: 400;
}

small {
    font-size: 13px;
    font-size: 1.3rem;
}

sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sup {
    top: -0.5em;
}

sub {
    bottom: -0.25em;
}

dl {
    margin: 0;
}

dt {
  line-height: 1.8;
    font-weight: bold;
    font-weight: 600;
    margin: 0;
}

dd {
  line-height: 1.8;
    margin: 0;
}

menu, ol, ul {
    margin: 0;
    padding: 0;
}

ul {
    list-style-type: none;
}

li {
  line-height: 1.8;
}
th {
  font-weight: bold;
}
td {
}

img {
    border: 0 none;
    height: auto;
    max-width: 100%;
    vertical-align: middle;
    -webkit-backface-visibility: hidden
}
input[type="text"]:focus,
textarea:focus {
  outline: 0;
}

a[href^="tel:"] {
  cursor: default;
  pointer-events: none;
}

/* ==============================================
 02. Layout
=============================================== */
/* Common
----------------------------------------------- */
.wrapper {
}
.content {
  padding-top: 50px;
  padding-bottom: 50px;
}
.container {
  padding-right: 25px;
  padding-left: 25px;
  max-width: 1060px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  @include sp {
    padding-left: 12px;
    padding-right: 12px;
  }
}
.flex {
  @include flex;
}
.flex-wrap {
  @include flex-wrap;
}
.align-items-center {
  @include align-items;
}
.justify-content-center {
  @include justify-content;
}

/* clearfix
----------------------------------------------- */
.cf:before,
.cf:after {
    content: " ";
    display: table;
}
.cf:after {
    clear: both;
}
.cf {
    *zoom: 1;
}

/* ==============================================
 03. Font
=============================================== */

/* ==============================================
 04. Parts
=============================================== */
/* button
----------------------------------------------- */
.btn {
}

/* page title
----------------------------------------------- */
.page-title {
  margin-bottom: 2rem;
  @include sp {
    margin-bottom: 1.5rem;
  }
}
/* heading
----------------------------------------------- */
.heading-1 {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.25;
  position: relative;
  @include sp {
    font-size: 18px;
  }
}

/* scrollbox
----------------------------------------------- */
.scroll-area {
  overflow: auto;
  position: relative;
  &.ps--active-x {
    padding-bottom: 10px;
    .ps__rail-x {
     opacity: 1;
     .ps__thumb-x {
       height: 10px;
       height: 4px;
     }
   }
  }
}

/* ==============================================
 05. Form
=============================================== */
/* parts
----------------------------------------------- */
.form-message {
  font-size: 120%;
  @include sp {
    font-size: 100%;
  }
}

/* ==============================================
 06. Calendar
=============================================== */
.reservation-calender {
  .fc {
    .fc-view-harness {
      @include sp {
        position: relative;
        margin-left: -12px;
        margin-right: -12px;
      }
    }
    .fc-toolbar-title {
      font-family: $FONT_FAMILY_EN;
      font-size: 26px;
      @include sp {
        font-size: 20px;
      }
    }
    .fc-col-header-cell {
      background-color: #E5E6E6;
      font-weight: normal;
      border-color: $BORDER_COLOR;
    }
    .fc-theme-standard td,
    .fc-theme-standard th {
      border-color: $BORDER_COLOR;
    }
    .fc-daygrid-day {
      font-family: $FONT_FAMILY_EN;
      font-size: 106%;
      background-position: 50% 70%;
      background-repeat: no-repeat;
      &.reserve-status-1 {
        background-image: url("../images/day_mark01.svg");
        background-size: 25px 25px;
      }
      &.reserve-status-2 {
        background-image: url("../images/day_mark02.svg");
        background-size: 25px 25px;
      }
      &.reserve-status-3 {
        background-image: url("../images/day_mark03.svg");
        background-size: 24px 24px;
      }
      &.reserve-status-none {
        background-image: url("../images/day_mark04.svg");
        background-size: 18px 18px;
      }
      &.reserve-status-tel {
        background-image: url("../images/day_mark05.svg");
        background-size: 30px 13px;
        @include sp {
          background-size: 26px 11px;
        }
      }
      &.fc-day-past {
        background-color: #F2F2F2;
        background-image: url("../images/day_mark04.svg");
        background-size: 20px 20px;
        .fc-daygrid-day-top {
          opacity: 1;
        }
      }
      &.fc-day-future {
        .fc-daygrid-day-top {
          opacity: 1;
        }
      }
      &.fc-day-today {
        background-color: #fff;
      }
      &.reserve-active-day {
        .fc-daygrid-day-frame {
          &::before {
            border: 2px solid #FD6066;
            content: "";
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            width: 100%;
          }
        }
      }
    }
    .fc-daygrid-day-frame {
    }
    .fc-daygrid-day-top {
      text-align: center;
      display: block;
      padding-top: .75rem;
    }
    .fc-toolbar-chunk {
      .fc-button {
        border: none;
        font-weight: bold;
        color: $FONT_COLOR;
        background-color: #fff;
        background-repeat: no-repeat;
        background-size: 6px 10px;
        width: 120px;
        @include box-shadow(0 2px 4px rgba($FONT_COLOR,0.3));
        @include sp {
          width: auto;
        }
        &:active {
          box-shadow: none;
        }
      }
      .fc-prev-button {
        background-image: url("../images/arrow_prev.svg");
        background-position: left 10px top 50%;
        @include sp {
          padding-left: 20px;
        }
      }
      .fc-next-button {
        background-image: url("../images/arrow_next.svg");
        background-position: right 10px top 50%;
        @include sp {
          padding-right: 20px;
        }
      }
    }
  }
}

.reservation-time-select {
  margin-top: 4rem;
  @include sp {
    margin-top: 3rem;
  }
  .time-select-title {
    font-family: $FONT_FAMILY_EN;
    font-size: 135%;
    margin-bottom: 1.75rem;
    font-weight: normal;
    @include sp {
      font-size: 120%;
      margin-bottom: 1rem;
    }
  }
  .time-select-list {
    @include flex;
    @include flex-wrap;
    @include sp {
      display: block;
    }
    li {
      margin-right: 10px;
      margin-bottom: 10px;
      @include sp {
        margin-right: auto;
      }
      &:last-child {
        margin-right: auto;
      }
      a {
        background-color: #fff;
        background-position: 50% 70%;
        background-repeat: no-repeat;
        border: 1px solid #ccc;
        display: block;
        font-family: $FONT_FAMILY_EN;
        font-size: 107%;
        height: 104px;
        text-align: center;
        width: 120px;
        @include sp {
          background-position: 12px 50%;
          height: auto;
          text-align: left;
          padding: 12px 12px 12px 50px;
          position: relative;
          width: auto;
          &::after {
            right: 14px;
            @include next-arrow($LINK_COLOR, 10px, 1px);
          }
        }
        &.reserve-status-1 {
          background-image: url("../images/day_mark01.svg");
          background-size: 25px 25px;
        }
        &.reserve-status-2 {
          background-image: url("../images/day_mark02.svg");
          background-size: 25px 25px;
        }
        &.reserve-status-3 {
          background-image: url("../images/day_mark03.svg");
          background-size: 24px 24px;
        }
        &.reserve-status-none {
          background-image: url("../images/day_mark04.svg");
          background-size: 18px 18px;
        }
        &.reserve-status-tel {
          background-image: url("../images/day_mark05.svg");
          background-size: 30px 13px;
          @include sp {
            background-size: 26px 11px;
          }
        }
        .time-range {
          border-bottom: 1px solid #ccc;
          background-color: #F2F2F2;
          display: block;
          padding: 2px;
          @include sp {
            border-bottom: none;
            background-color: inherit;
            padding: 0;
          }
        }
      }
    }
  }
}


/* ==============================================
 Media Queries
=============================================== */
@media screen and (max-width: 767px) {
  a[href^="tel:"] {
    cursor: pointer;
    pointer-events: auto;
  }
}

/* ==============================================
 Media Queries
=============================================== */
/* hidden
----------------------------------------------- */
@media screen and (min-width: 992px) {
  .hidden-lg {
    display: none !important
  }
}
@media screen and (min-width: 768px) and (max-width: 991px) {
  .hidden-md {
    display: none !important
  }
}
@media screen and (min-width: 544px) and (max-width: 767px) {
  .hidden-sm {
    display: none !important
  }
}
@media screen and (max-width: 543px) {
  .hidden-xs {
    display: none !important
  }
}


/* column
----------------------------------------------- */
.col-lg-1 {
    width: 12%;
}
.col-lg-2 {
    width: 16.6666%;
}
.col-lg-3 {
    width: 25%;
}
.col-lg-4 {
    width: 33.3333%;
}
.col-lg-5 {
    width: 41.6666%;
}
.col-lg-6 {
    width: 50%;
}
.col-lg-7 {
    width: 57.0833%;
}
.col-lg-8 {
    width: 66.6667%;
}
.col-lg-9 {
    width: 75%;
}
.col-lg-10 {
    width: 83.3334%;
}
.col-lg-11 {
    width: 91.6666%;
}
.col-lg-12 {
    width: 100%;
}


/* pc min
----------------------------------------------- */
@media screen and (max-width: 1119px) {

  /* column
  ----------------------------------------------- */
  .col-md-1 {
      width: 12%;
  }
  .col-md-2 {
      width: 16.6666%;
  }
  .col-md-3 {
      width: 25%;
  }
  .col-md-4 {
      width: 33.3333%;
  }
  .col-md-5 {
      width: 41.6666%;
  }
  .col-md-6 {
      width: 50%;
  }
  .col-md-7 {
      width: 57.0833%;
  }
  .col-md-8 {
      width: 66.6667%;
  }
  .col-md-9 {
      width: 75%;
  }
  .col-md-10 {
      width: 83.3334%;
  }
  .col-md-11 {
      width: 91.6666%;
  }
  .col-md-12 {
      width: 100%;
  }

}

/* tablet
----------------------------------------------- */
@media screen and (max-width: 991px) {
    body.sp-nav-open {
      overflow: hidden !important;
    }

    /* ==============================================
     Layout
    =============================================== */

    /* column
    ----------------------------------------------- */
    .col-sm-1 {
        width: 12%;
    }
    .col-sm-2 {
        width: 16.6666%;
    }
    .col-sm-3 {
        width: 25%;
    }
    .col-sm-4 {
        width: 33.3333%;
    }
    .col-sm-5 {
        width: 41.6666%;
    }
    .col-sm-6 {
        width: 50%;
    }
    .col-sm-7 {
        width: 57.0833%;
    }
    .col-sm-8 {
        width: 66.6667%;
    }
    .col-sm-9 {
        width: 75%;
    }
    .col-sm-10 {
        width: 83.3334%;
    }
    .col-sm-11 {
        width: 91.6666%;
    }
    .col-sm-12 {
        width: 100%;
    }

}
/* column
----------------------------------------------- */
@media screen and (max-width: 767px) {

    /* column
    ----------------------------------------------- */
    .col-xs-1 {
        width: 12%;
    }
    .col-xs-2 {
        width: 16.6666%;
    }
    .col-xs-3 {
        width: 25%;
    }
    .col-xs-4 {
        width: 33.3333%;
    }
    .col-xs-5 {
        width: 41.6666%;
    }
    .col-xs-6 {
        width: 50%;
    }
    .col-xs-7 {
        width: 57.0833%;
    }
    .col-xs-8 {
        width: 66.6667%;
    }
    .col-xs-9 {
        width: 75%;
    }
    .col-xs-10 {
        width: 83.3334%;
    }
    .col-xs-11 {
        width: 91.6666%;
    }
    .col-xs-12 {
        width: 100%;
    }
}

@media print {
    body {
        zoom: 75%;
    }
}
