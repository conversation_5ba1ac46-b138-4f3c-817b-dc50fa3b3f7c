<?php

use \App\Database\Migration\CustomBlueprint as Blueprint;
use App\Database\Migration\Schema;

class CreateReserveDenialsTable extends \App\Database\Migration\Create
{
    protected $_table = 'reserve_denials';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable($this->getTable())) {
            Schema::create($this->getTable(), function (Blueprint $table) {
                $table->increments('id');
                $table->integer('shop_id')->comment('店舗ID');
                $table->time('denial_time_from')->comment('受入不可時間FROM');
                $table->time('denial_time_to')->comment('受入不可時間TO');
                $table->actionBy();
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }
}
