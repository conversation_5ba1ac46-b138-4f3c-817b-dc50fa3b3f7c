<?php

namespace App\Repositories;

use App\Model\Entities\Reserve;
use App\Model\Entities\ReserveTel;
use App\Repositories\Base\CustomRepository;
use Carbon\Carbon;
use DateInterval;
use DatePeriod;
use DateTime;
use Illuminate\Support\Facades\DB;

class ReserveRepository extends CustomRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    function model()
    {
        return Reserve::class;
    }

    public function validator()
    {
        return \App\Validators\Backend\Reserve::class;
    }

    /**
     * @param $data
     * @return mixed
     */
    public function getReserveInDayByShop($data) {
        return $this->findWhere(['shop_id' => $data['shop_id'], 'reserve_date' => $data['reserve_date']]);
    }

    /**
     * @param $shopId
     * @param $start
     * @param $end
     * @return mixed
     */
    public function getTotalReserveNum($shopId, $start, $end) {
        $query = $this->select(DB::raw("reserve_date, sum(reserve_num) as total"))
            ->where('shop_id', $shopId)
            ->whereBetween('reserve_date', [$start, $end])
            ->groupBy('reserve_date')
            ->get()->pluck('total', 'reserve_date')->toArray();

        return $query;
    }

    /**
     * @param $id
     * @param $start
     * @param $end
     * @return array ['2022-01-02' => ['reserve_time' => '08:00,  'reserve_num' => 1]]
     *
     */
    public function getDataReserve($id, $start, $end) {
        // reserve by shop
        $reserves = app(Reserve::class)->where(['shop_id' => $id])->whereBetween('reserve_date', [$start, $end])->get()->toArray();
        $dataShop = app(ShopInfoRepository::class)->findById($id);
        $timeDeny = app(ReserveDenialsRepository::class)->getTimeDenyByShop($id);

        if (sizeof($reserves) == 0) {
            return [];
        }

        //group by reserve_date
        $dataReserve = [];
        foreach ($reserves as $value) {
            $dataReserve[$value['reserve_date']][] = [
                'reserve_time' => formatTime($value['reserve_time']),
                'reserve_num' => $value['reserve_num'],
            ];
        }

        // check deny
        if (sizeof($dataReserve) > 0 && sizeof($timeDeny) > 0) {
            foreach ($dataReserve as $date => $period) {
                foreach ($period as $key => $value) {
                    $operationTime = [
                        toMinute($value['reserve_time']),
                        toMinute($value['reserve_time']) + (int)$dataShop->interval_min
                    ];

                    foreach ($timeDeny as $deny) {
                        $arrDeny = [$deny['from'], $deny['to']];
                        if (isOverlapped($operationTime, $arrDeny)) {
                            unset($dataReserve[$date][$key]);
                            break;
                        }
                    }
                }
            }
        }

        // check publish time
        $publishFromDate = Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_from_dt)->format('Y-m-d');
        $publishFromTime = Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_from_dt)->format('H:i:s');
        $publishToDate = !empty($dataShop->publish_to_dt) ? Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_to_dt)->format('Y-m-d') : null;
        $publishToTime = !empty($dataShop->publish_to_dt) ? Carbon::createFromFormat('Y-m-d H:i:s', $dataShop->publish_to_dt)->format('H:i:s') : null;
        $today = date('Y-m-d');
        $currentTime = date('H:i:s');

        foreach ($dataReserve as $date => $periods) {
            foreach ($periods as $key => $period) {
                $operationTime = [
                    toMinute($period['reserve_time']),
                    toMinute($period['reserve_time']) + (int)$dataShop->interval_min
                ];

                // publish from datetime
                if ($date == $publishFromDate && $date == $today) {
                    $endTime = strtotime($publishFromTime) > strtotime($currentTime) ? $publishFromTime : $currentTime;
                    if (isOverlapped($operationTime, [0, toMinute($endTime)])) {
                        unset($dataReserve[$date][$key]);
                    }
                }

                // publish to datetime
                if ($date == $publishToDate) {
                    if (isOverlapped($operationTime, [toMinute($publishToTime), 1440])) {
                        unset($dataReserve[$date][$key]);
                    }
                }
            }
        }

        return $dataReserve;
    }
}

