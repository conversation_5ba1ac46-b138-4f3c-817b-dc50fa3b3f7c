
<div class="custom-dialog-inner">
    <div class="form custom-dialog-form" id="reserve-time-form">
        <input type="hidden" name="reserve_date" value="<?php echo e($reserve_date); ?>" id="set-reserve-date">
        <div class="reserve-date-target">
            <div class="label">編集対象</div>
            <div class="target">
                <div id="show-reserve-date"><?php echo e(toDateJa($reserve_date)); ?></div>
            </div>
        </div>
        <ul id="reserve-time-list" class="reserve-time-list">
            <?php if(sizeof($data)): ?>
                <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $isDeny = $value['deny'] == getConstant('DENY.ON');
                    ?>

                    <li class="reserve_num_item <?php echo e($isDeny ? 'disabled' : ''); ?>" data-id="<?php echo e(isset($value['id']) ? $value['id'] : ''); ?>">
                        <div class="time-range" data-from="<?php echo e($value['from']); ?>" data-to="<?php echo e($value['to']); ?>"> <?php echo e($value['from']); ?> 〜 <?php echo e($value['to']); ?></div>
                        <div class="receiving-quantity"><?php echo e($isDeny ? '拒否設定済み' : '受入可能数'); ?>  ： <span><?php echo e(!empty($dataShop->reservation_frame_num) ? $dataShop->reservation_frame_num: ''); ?></span></div>
                        <div class="receiving-number">予約受付数 ：
                            <?php if($isDeny): ?>
                                <input type="number" value="<?php echo e(isset($value['reserve_num']) ? $value['reserve_num'] : 0); ?>" class="form-control" tabindex="-1">
                            <?php else: ?>
                                <input type="number" value="<?php echo e(isset($value['reserve_num']) ? $value['reserve_num'] : 0); ?>" class="form-control">
                            <?php endif; ?>
                        </div>
                    </li>
                    <li class="text-error"><div></div></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <p>選択できません</p>
            <?php endif; ?>
        </ul>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\reserve_one_system\app\Views/backend/calendar/dialog/_list_operation_time.blade.php ENDPATH**/ ?>