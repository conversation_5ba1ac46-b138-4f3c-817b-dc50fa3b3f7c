<?php

namespace App\Model\Base;

use BadMethodCallException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\RelationNotFoundException;
use Illuminate\Database\Eloquent\Relations\Relation;

/**
 * Class CustomBuilder
 */
class CustomBuilder extends Builder
{
    /**
     * @param string $name
     * @return Relation|mixed
     */
    public function getRelation($name)
    {
        $relation = Relation::noConstraints(function () use ($name) {
            try {
                // to keep dynamic attributes
                return $this->getModel()->{$name}();
                // origin source return $this->getModel()->newInstance()->$name();
            } catch (BadMethodCallException $e) {
                throw RelationNotFoundException::make($this->getModel(), $name);
            }
        });

        $nested = $this->relationsNestedUnder($name);
        if (count($nested) > 0) {
            $relation->getQuery()->with($nested);
        }

        return $relation;
    }



}