<?php

namespace App\Validators\Backend;

use App\Validators\Base\BaseValidator;

/**
 * Class ReserveDenial
 * @package App\Validator
 */
class ReserveTel extends BaseValidator
{
    /**
     * @param $data
     * @return bool
     */
    public function validate($data)
    {
        $rules = array(
            'shop_id' => 'required',
            'tel_flg' => 'required|integer|in:0,1',
            'reserve_date' => 'required|date_format:Y-m-d',
        );

        $messages = [];

        return $this->_addRules($rules, $messages)->with($data)->passes();
    }
}
