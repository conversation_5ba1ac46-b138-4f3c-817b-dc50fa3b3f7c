<?php

namespace App\chatworknotify\src;


use App\chatworknotify\src\Exceptions\Handler;
use Illuminate\Contracts\Debug\ExceptionHandler;
use Illuminate\Support\ServiceProvider;

/**
 *
 * Chatwork ServiceProvider
 *
 * @category   Laravel chatworknotify
 * @package    chatworknotify
 * @copyright  Copyright (c) 2013 - 2014 Maatwebsite (http://www.maatwebsite.nl)
 * <AUTHOR> <<EMAIL>>
 * @license    http://www.gnu.org/licenses/old-licenses/lgpl-2.1.txt    LGPL
 */
class ChatworkServiceProvider extends ServiceProvider
{

    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = false;

    /**
     * Bootstrap the application events.
     *
     * @return void
     */

    public function boot()
    {
        $this->publishes([
            __DIR__ . '/config/chatwork.php' => config_path('chatwork.php'),
        ]);

        $this->app->bind(
            ExceptionHandler::class,
            Handler::class
        );

    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->mergeConfigFrom(
            __DIR__ . '/config/chatwork.php', 'chatwork'
        );
    }


}
