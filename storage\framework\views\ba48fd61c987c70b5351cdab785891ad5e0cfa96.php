
<?php $__env->startSection('content'); ?>
    <!-- start::Post -->
    <div class="post">
        <!-- start::Page title -->
        <div class="page-title flex align-items-center">
            <div class="mr-auto flex align-items-center">
                <h1 class="heading-1">クライアント一覧</h1>
            </div>
        </div>
        <!-- end::Page title -->
        <!-- start::Card -->
        <div class="card">
            <!-- start::Card header -->
            <div class="card-header">
                <a href="<?php echo e(route('shop.create')); ?>" class="btn btn-primary">新規追加</a>
            </div>
            <!-- end::Card header -->
            <!-- start::Card body -->
            <div class="mt-4 card-body pt-4 pt-sm-2">
                <?php if($entities->lastPage()>1): ?>
                    <?php echo $__env->make('layouts.backend.elements.paginator', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>
                <div class="mb-4 scroll-area responsive-container-sp">
                    <table class="table align-middle">
                        <tbody>
                        <?php $__currentLoopData = $entities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($entity->id); ?></td>
                                <td style="white-space: pre-line;"><?php echo e($entity->shop_name); ?></td>
                                <td>
                                    <div class="flex align-items-center justify-content-end flex-shrink-0">
                                        <a href="<?php echo e(route('shop.edit', [$entity->id])); ?>" class="btn btn-primary mr-3">詳細</a>
                                        <button type="button" class="btn btn-destroy"
                                                data-action="<?php echo e(route('shop.destroy', [$entity->id])); ?>"
                                                onclick="Shop.showModalDelete(this)"><?php echo e(trans('actions.destroy')); ?></button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                <?php if($entities->lastPage()>1): ?>
                    <?php echo $__env->make('layouts.backend.elements.paginator', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>
            </div>
            <!-- end::Card body -->
        </div>
        <!-- end::Card -->
    </div>
    <!-- end::Post -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.backend.layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\reserve_one_system\app\Views/backend/shop/index.blade.php ENDPATH**/ ?>