<?php

namespace App\Repositories;

use App\Model\Entities\ShopInfo;
use App\Repositories\Base\CustomRepository;
use App\Services\Backend\CalendarService;
use Illuminate\Support\Facades\DB;

class ShopInfoRepository extends CustomRepository
{
    /**
     * Specify Model class name
     *
     * @return string
     */
    function model()
    {
        return ShopInfo::class;
    }

    public function validator()
    {
        return \App\Validators\Backend\ShopInfo::class;
    }

    public function getShopIdByShopKey($shopKey)
    {
        return $this->where('shop_key', $shopKey)->pluck('id')->first();
    }

    /**
     * @param $shopId
     * @return array (key: weekday, value: total number time of frames)
     */
    public function getNumberOfTimeFrames($shopId)
    {
        $dataShop = $this->findById($shopId);
        $interval = $dataShop->interval_min;

        // 0.sun 1.mon 2.tue 3.wed 4.thu 5.fri 6.sat
        $select = "
            FLOOR((SUM(UNIX_TIMESTAMP(mon_operation_to) - UNIX_TIMESTAMP(mon_operation_from)) / 60) / $interval) AS 'mon',
            FLOOR((SUM(UNIX_TIMESTAMP(tue_operation_to) - UNIX_TIMESTAMP(tue_operation_from)) / 60) / $interval) AS 'tue',
            FLOOR((SUM(UNIX_TIMESTAMP(wed_operation_to) - UNIX_TIMESTAMP(wed_operation_from)) / 60) / $interval) AS 'wed',
            FLOOR((SUM(UNIX_TIMESTAMP(thu_operation_to) - UNIX_TIMESTAMP(thu_operation_from)) / 60) / $interval) AS 'thu',
            FLOOR((SUM(UNIX_TIMESTAMP(fri_operation_to) - UNIX_TIMESTAMP(fri_operation_from)) / 60) / $interval) AS 'fri',
            FLOOR((SUM(UNIX_TIMESTAMP(sat_operation_to) - UNIX_TIMESTAMP(sat_operation_from)) / 60) / $interval) AS 'sat',
            FLOOR((SUM(UNIX_TIMESTAMP(sun_operation_to) - UNIX_TIMESTAMP(sun_operation_from)) / 60) / $interval) AS 'sun'
        ";

        $shopInfo = app(ShopInfo::class);
        $numberOfTimeFrames = $shopInfo->select(DB::raw($select))->where('id', $shopId)->first()->toArray();
        $weekday = getConstant('WEEK_MAP');

        $data = [];
        foreach ($weekday as $index => $strWeekday) {
            $data[$index] = (int)$numberOfTimeFrames[$strWeekday];
        }

        return $data;
    }

    public function getNumFramesWithDeny($dataShop) {
        /** @var  CalendarService $calendarService */
        $calendarService = app(CalendarService::class);
        $timeFramesInDay = $calendarService->operationTimeRangeEachDays($dataShop);

        $data = [];
        foreach ($timeFramesInDay as $weekDay => $value) {
            $data[$weekDay] = sizeof($value);
        }

        return $data;
    }
}

