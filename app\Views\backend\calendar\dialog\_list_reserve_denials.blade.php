@if(!empty($data))
    @foreach($data as $reserveDenials)
        <div class="item-period" data-id="{{ $reserveDenials->id }}">
            <div class="box-period">
                <input type="hidden" name="shopId" value="">
                <div class="d-flex justify-between" style="width: 80%">
                    <select name="denial_time_from" class="select-time">
                        @foreach(arrayTime() as $key => $time)
                            <option value="{{ $key }}" {{ subStringTime($reserveDenials->denial_time_from) == $key ? 'selected' : '' }}>{{ $time }}</option>
                        @endforeach
                    </select>
                    <i class="icon-to"></i>
                    <select name="denial_time_to" class="select-time">
                        @foreach(arrayTime() as $key => $time)
                            <option value="{{ $key }}" {{ subStringTime($reserveDenials->denial_time_to) == $key ? 'selected' : '' }}>{{ $time }}</option>
                        @endforeach
                    </select>
                </div>
                <button data-click="removePeriodDeny" href="javascript:void(0)" class="btn btn-remove-period">削除</button>
            </div>

            <div class="text-error error-denial_time_from text-danger"></div>
            <div class="text-error error-denial_time_to text-danger"></div>
        </div>
    @endforeach
@endif

