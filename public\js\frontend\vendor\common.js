// Loading
$(window).on('load',function(){
  $('body').removeClass('preload');
});

$(function () {
  /* IE smooth scroll false
   * ------------------------------------------------------- */
  if(navigator.userAgent.match(/MSIE 10/i) || navigator.userAgent.match(/Trident\/7\./) || navigator.userAgent.match(/Edge\/12\./)) {
   $('body').on("mousewheel", function () {
   (event.preventDefault) ? event.preventDefault():event.returnValue=false;
   var wd = event.wheelDelta;
   var csp = window.pageYOffset;
   window.scrollTo(0, csp - wd);
   });
  }
  /* Click event
   * ------------------------------------------------------- */
  var clickEventType=((window.ontouchstart!==null)?'click':'touchstart');

  /* set height
   * ------------------------------------------------------- */
   if(document.getElementsByClassName('vh-100').length) {
     const setFillHeight = () => {
       const vh = window.innerHeight * 0.01;
       document.getElementsByClassName('vh-100')[0].style.setProperty('--vh', `${vh}px`);
     }
     let vw = window.innerWidth;
     window.addEventListener('resize', () => {
       if (vw === window.innerWidth) {
         return;
       }
       vw = window.innerWidth;
       setFillHeight();
     });
     setFillHeight();
   }
});
