@extends('layouts.backend.layouts.main')
@section('content')
    <!-- start::Post -->
    <div class="post">
        <!-- start::Page title -->
        <div class="page-title flex align-items-center">
            <div class="mr-auto flex align-items-center">
                <h1 class="heading-1">クライアント一覧</h1>
            </div>
        </div>
        <!-- end::Page title -->
        <!-- start::Card -->
        <div class="card">
            <!-- start::Card header -->
            <div class="card-header">
                <a href="{{ route('shop.create') }}" class="btn btn-primary">新規追加</a>
            </div>
            <!-- end::Card header -->
            <!-- start::Card body -->
            <div class="mt-4 card-body pt-4 pt-sm-2">
                @if($entities->lastPage()>1)
                    @include('layouts.backend.elements.paginator')
                @endif
                <div class="mb-4 scroll-area responsive-container-sp">
                    <table class="table align-middle">
                        <tbody>
                        @foreach($entities as $entity)
                            <tr>
                                <td>{{$entity->id}}</td>
                                <td style="white-space: pre-line;">{{$entity->shop_name}}</td>
                                <td>
                                    <div class="flex align-items-center justify-content-end flex-shrink-0">
                                        <a href="{{ route('shop.edit', [$entity->id]) }}" class="btn btn-primary mr-3">詳細</a>
                                        <button type="button" class="btn btn-destroy"
                                                data-action="{{ route('shop.destroy', [$entity->id]) }}"
                                                onclick="Shop.showModalDelete(this)">{{ trans('actions.destroy') }}</button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
                @if($entities->lastPage()>1)
                    @include('layouts.backend.elements.paginator')
                @endif
            </div>
            <!-- end::Card body -->
        </div>
        <!-- end::Card -->
    </div>
    <!-- end::Post -->
@endsection
