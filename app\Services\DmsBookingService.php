<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * DMS Booking Service để kiểm tra trạng thái cancel booking từ hệ thống DMS
 */
class DmsBookingService
{
    /**
     * Cache timeout cho column check (5 phút)
     */
    const COLUMN_CHECK_CACHE_TIMEOUT = 300;

    /**
     * DMS connection name
     */
    protected $dmsConnection;

    /**
     * DMS table name
     */
    protected $dmsTable;

    public function __construct()
    {
        $this->dmsConnection = config('database.dms_connection', 'mysql');
        $this->dmsTable = config('database.dms_table', 'bookings');
    }

    /**
     * Kiểm tra xem column rr_cancel có tồn tại trong DMS table không
     * Sử dụng cache để tránh check nhiều lần
     *
     * @return bool
     */
    public function hasRrCancelColumn()
    {
        $cacheKey = "dms_column_check_{$this->dmsTable}_rr_cancel";
        
        return Cache::remember($cacheKey, self::COLUMN_CHECK_CACHE_TIMEOUT, function () {
            try {
                return Schema::connection($this->dmsConnection)->hasColumn($this->dmsTable, 'rr_cancel');
            } catch (\Exception $e) {
                Log::error('Error checking DMS column existence: ' . $e->getMessage());
                return false;
            }
        });
    }

    /**
     * Kiểm tra xem booking có bị cancel không dựa trên DMS data
     *
     * @param mixed $bookingId ID của booking cần kiểm tra
     * @return bool true nếu booking bị cancel, false nếu không
     */
    public function isBookingCancelled($bookingId)
    {
        try {
            // Nếu column không tồn tại, return false (không cancel)
            if (!$this->hasRrCancelColumn()) {
                return false;
            }

            $result = DB::connection($this->dmsConnection)
                ->table($this->dmsTable)
                ->where('id', $bookingId)
                ->value('rr_cancel');

            // Kiểm tra xem có chứa text "キャンセル" không
            return !empty($result) && strpos($result, 'キャンセル') !== false;

        } catch (\Exception $e) {
            Log::error('Error checking booking cancel status from DMS: ' . $e->getMessage());
            // Trong trường hợp lỗi, return false để không ảnh hưởng đến logic chính
            return false;
        }
    }

    /**
     * Lấy danh sách các booking ID bị cancel từ DMS
     *
     * @param array $bookingIds Danh sách booking IDs cần kiểm tra
     * @return array Danh sách booking IDs bị cancel
     */
    public function getCancelledBookingIds(array $bookingIds)
    {
        try {
            // Nếu column không tồn tại, return empty array
            if (!$this->hasRrCancelColumn()) {
                return [];
            }

            if (empty($bookingIds)) {
                return [];
            }

            $cancelledBookings = DB::connection($this->dmsConnection)
                ->table($this->dmsTable)
                ->whereIn('id', $bookingIds)
                ->where('rr_cancel', 'like', '%キャンセル%')
                ->pluck('id')
                ->toArray();

            return $cancelledBookings;

        } catch (\Exception $e) {
            Log::error('Error getting cancelled booking IDs from DMS: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Lọc bỏ các booking bị cancel từ danh sách
     *
     * @param array $bookings Danh sách booking data
     * @param string $idField Tên field chứa booking ID (default: 'id')
     * @return array Danh sách booking sau khi lọc bỏ cancelled bookings
     */
    public function filterCancelledBookings(array $bookings, $idField = 'id')
    {
        if (empty($bookings)) {
            return $bookings;
        }

        try {
            // Lấy danh sách booking IDs
            $bookingIds = [];
            foreach ($bookings as $booking) {
                if (is_array($booking) && isset($booking[$idField])) {
                    $bookingIds[] = $booking[$idField];
                } elseif (is_object($booking) && isset($booking->{$idField})) {
                    $bookingIds[] = $booking->{$idField};
                }
            }
            
            // Lấy danh sách booking IDs bị cancel
            $cancelledIds = $this->getCancelledBookingIds($bookingIds);

            if (empty($cancelledIds)) {
                return $bookings;
            }

            // Lọc bỏ các booking bị cancel
            return array_filter($bookings, function($booking) use ($cancelledIds, $idField) {
                $bookingId = is_array($booking) ? $booking[$idField] : $booking->{$idField};
                return !in_array($bookingId, $cancelledIds);
            });

        } catch (\Exception $e) {
            Log::error('Error filtering cancelled bookings: ' . $e->getMessage());
            // Trong trường hợp lỗi, return danh sách gốc
            return $bookings;
        }
    }

    /**
     * Kiểm tra DMS connection có available không
     *
     * @return bool
     */
    public function isDmsAvailable()
    {
        try {
            DB::connection($this->dmsConnection)->getPdo();
            return true;
        } catch (\Exception $e) {
            Log::error('DMS connection not available: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Clear cache cho column check
     *
     * @return void
     */
    public function clearColumnCache()
    {
        $cacheKey = "dms_column_check_{$this->dmsTable}_rr_cancel";
        Cache::forget($cacheKey);
    }

    /**
     * Set DMS connection và table (để test hoặc cấu hình động)
     *
     * @param string $connection
     * @param string $table
     * @return $this
     */
    public function setDmsConfig($connection, $table)
    {
        $this->dmsConnection = $connection;
        $this->dmsTable = $table;
        return $this;
    }
}
