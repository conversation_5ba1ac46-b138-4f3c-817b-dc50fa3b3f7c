
a.sorting:after {
    padding-left: 3px;
    position: relative;
    font-size: 10px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: 100;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
}

a.sorting_desc:after {
    content: "\e113";
}

a.sorting_asc:after {
    content: "\e114";
}

.error-message {
    color: #FF0000;
}

.inline-group {
    position: relative;
    display: table;
    border-collapse: separate;
}

table.table-bordered.table-hover td {
    word-break: break-all;
}

.non-word-break {
    word-break: normal !important;
}

#error_msg .alert-danger ul {
    padding: 0;
    margin: 0 -5px;
}

#error_msg .alert-danger ul li, #success_msg ul li {
    list-style-type: none;
}

.table {
    margin-bottom: 0px;
}

.no-data {
    padding: 10px;
}

ul.fore-show-error {
    padding-left: 0;
    margin-top: 5px;
    margin-bottom: 0;
}

ul.fore-show-error > li {
    list-style-type: none;
    color: #a94442;
}

.alert-success, .alert-danger {
    margin-top: 20px;
    margin-bottom: 20px;
}

.disabled-event {
    pointer-events: none;
    user-select: none;
    cursor: default;
}
