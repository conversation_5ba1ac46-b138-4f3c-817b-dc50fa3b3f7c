# Laravel 8.x for Laravel Shift

This repository contains the latest version of Laravel 8. It is used as a reference by [Laravel Shift - the automated way to upgrade Laravel applications](https://laravelshift.com).

## About Shift
*Shift* focuses on providing [automated Shifts](https://laravelshift.com/shifts) and [Human Shifts](https://laravelshift.com/human-shifts) for upgrading and improving your Laravel, Lumen, PHP, and Tailwind projects.

Shifts for *Laravel* include:

- [Laravel 5.0 Shift - upgrade <PERSON>vel from 4.2 to 5.0](https://laravelshift.com/upgrade-laravel-4.2-to-laravel-5.0)
- [Laravel 5.1 Shift - upgrade <PERSON>vel from 5.0 to 5.1](https://laravelshift.com/upgrade-laravel-5.0-to-laravel-5.1) 
- [Laravel 5.2 Shift - upgrade <PERSON><PERSON> from 5.1 to 5.2](https://laravelshift.com/upgrade-laravel-5.1-to-laravel-5.2) 
- [Laravel 5.3 Shift - upgrade <PERSON><PERSON> from 5.2 to 5.3](https://laravelshift.com/upgrade-laravel-5.2-to-laravel-5.3)
- [Laravel 5.4 Shift - upgrade <PERSON><PERSON> from 5.3 to 5.4](https://laravelshift.com/upgrade-laravel-5.3-to-laravel-5.4)
- [Laravel 5.5 Shift - upgrade Laravel from 5.4 to 5.5](https://laravelshift.com/upgrade-laravel-5.4-to-laravel-5.5)
- [Laravel 5.6 Shift - upgrade Laravel from 5.5 to 5.6](https://laravelshift.com/upgrade-laravel-5.5-to-laravel-5.6)
- [Laravel 5.7 Shift - upgrade Laravel from 5.6 to 5.7](https://laravelshift.com/upgrade-laravel-5.6-to-laravel-5.7)
- [Laravel 5.8 Shift - upgrade Laravel from 5.7 to 5.8](https://laravelshift.com/upgrade-laravel-5.7-to-laravel-5.8)
- [Laravel 6.x Shift - upgrade Laravel from 5.8 to 6.x](https://laravelshift.com/upgrade-laravel-5.8-to-laravel-6.0)
- [Laravel 7.x Shift - upgrade Laravel from 6.x to 7.x](https://laravelshift.com/upgrade-laravel-6-to-laravel-7)
- [Laravel 8.x Shift - upgrade Laravel from 7.x to 8.x](https://laravelshift.com/upgrade-laravel-7-to-laravel-8)
- [BrowserKit Tests Converter - upgrade tests from BrowserKit](https://laravelshift.com/upgrade-laravel-5.3-tests-to-laravel-5.4-tests)
- [Laravel Linter - detect lint in your Laravel project](https://laravelshift.com/laravel-linter) 
- [Laravel Analyzer - are you following the "Laravel Way"](https://laravelshift.com/opinionated-laravel-way-shift) 
- [Laravel Fixer - automate changes to the "Laravel Way"](https://laravelshift.com/laravel-code-fixer)
- [Laravel Tests Generator - generate HTTP tests for your Laravel application](https://laravelshift.com/laravel-test-generator) 
- [Consolidate Namespace Shift - condense custom namespaces with Laravel](https://laravelshift.com/laravel-consolidate-custom-namespaces)
- [Namespace Models Shift - move Models to app/Models](https://laravelshift.com/laravel-namespace-models)


Shifts for *Lumen* include:

- [Lumen to Laravel - convert a Lumen project to Laravel](https://laravelshift.com/convert-lumen-to-laravel) 


Shifts for *PHP* include:

- [PSR-2 - adopt the PSR-2 code style](https://laravelshift.com/upgrade-psr2-code-style-standard)
- [PSR-4 - upgrade from PSR-0 to PSR-4](https://laravelshift.com/upgrade-namespace-psr0-psr4)
- [Mysqli Shift - covert mysql to mysqli](https://laravelshift.com/upgrade-mysql-mysqli)
- [PHPUnit 6 Shift - upgrade tests for PHPUnit 6](https://laravelshift.com/upgrade-phpunit-6)
- [PHPUnit 8 Shift - upgrade tests for PHPUnit 8](https://laravelshift.com/upgrade-phpunit-8)
- [PHPUnit 9 Shift - upgrade tests for PHPUnit 9](https://laravelshift.com/upgrade-phpunit-9)

Shifts for *Tailwind* include:

- [Tailwind 1.x Shift - upgrade Tailwind from 0.x to 1.x](https://laravelshift.com/upgrade-tailwind-css-0-to-tailwind-css-1)
- [Tailwind 2.x Shift - upgrade Tailwind from 1.x to 2.x](https://laravelshift.com/upgrade-tailwind-css-1-to-tailwind-css-2)
- [Tailwind UI Shift - upgrade Tailwind UI from 1.x to 2.x](https://laravelshift.com/upgrade-tailwind-ui-1-to-tailwind-ui-2)
- [Tailwind Converter - convert Bootstrap CSS to Tailwind CSS](https://laravelshift.com/convert-bootstrap-to-tailwind-css)
