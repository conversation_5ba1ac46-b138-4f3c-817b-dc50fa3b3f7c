// パラメーター取得
var url = new URL(window.location.href);
var params = url.searchParams;
var jsonDateTime = JSON.parse($('#json_date').val());
var timeRange = JSON.parse($('#time_range').val());
var reserveFormUrl = $('#reserve_form_url').val();
var shopName = $('#shop_name').val();

// カレンダーのセルにアイコンを表示するためのclass命名
function setCellClassName(count, tel) {
    var className = 'reserve-status-none';
    if (tel) {
        className = 'reserve-status-tel';
    } else {
        if (count === 0) {
            className = 'reserve-status-none';
        } else if (count >= 5) {
            className = 'reserve-status-1';
        } else if (count >= 3) {
            className = 'reserve-status-2';
        } else if (count <= 2) {
            className = 'reserve-status-3';
        } else if (count == 'tel') {
            className = 'reserve-status-tel';
        }
    }

    return className;
}

// 時間選択エリアの生成
function createReserveTime(date) {
    var cell_date = new Date(date);
    var format_date = cell_date.getFullYear()+'-'+("0" + (cell_date.getMonth()+1)).slice(-2)+'-'+("0" + cell_date.getDate()).slice(-2);
    const isInvalidDate = (date) => Number.isNaN(date.getTime());
    // タイトル
    if (!isInvalidDate(date)) {
        var title_date = (date.getMonth()+1)+'/'+(date.getDate());
        document.getElementById('time-select-title').textContent = title_date+'の時間選択';
    }
    // 時間選択リスト
    var indexDate = JSON.parse($('#json_date').val()).filter(function(item, index){
        if (item.day == format_date) return item;
    });
    var targetElm = document.getElementById('time-select-list');
    targetElm.innerHTML = '';

    if ((indexDate[0].timeData).length == 0) {
        $('.reservation-time-select .text-error').css('display', 'block');
    } else {
        $('.reservation-time-select .text-error').css('display', 'none');
    }

    if (indexDate.length > 0) {
        var timeData = indexDate[0].timeData;
        for (let key in timeData) {
            var val = timeData[key];
            var className = setCellClassName(val);
            let d = new Date(format_date);
            let day = d.getDay();
            let href = val > 0 ? renderUrl(indexDate[0].day, (timeRange[day][key])) : 'javascript:void(0)';

            var li = '<li><a target="'+ (val > 0 ? '_blank' : "")+'" href="' + href + '" class="' + className + '"><span class="time-range">' + timeRange[day][key] + '</span></a></li>';
            $(li).appendTo(targetElm);
        }
    } else {
        if (isInvalidDate(date)) {
            $('<p>日付を選択してください。</p>').appendTo(targetElm);
        } else {
            $('<p>選択できません</p>').appendTo(targetElm);
        }
    }
}

function renderUrl(date, time) {
    let s = date.split("-");
    let dayOfWeekStr = ["日", "月", "火", "水", "木", "金", "土"][(new Date(date)).getDay()];
    let dateStr = parseInt(s[1]) + '/' +  parseInt(s[2]);
    var href = reserveFormUrl + '?見学予約する物件=' + shopName + '&予約日時=' + dateStr + '（'+ dayOfWeekStr +'） ' + time;

    return encodeURI(href);
}

// ページ読み込み時
createReserveTime(new Date(params));

init(jsonDateTime, $('input[name="date_week"]').val());

/* calender
* --------------------------------------------------------- */
function init(jsonDateTime, date) {
    var calendar = document.getElementById('calendar');
    if (calendar != null) {
        var calendar = new FullCalendar.Calendar(calendar, {
            initialDate: date,
            initialView: 'dayGridWeek',
            locale: 'ja',
            height: 175,
            //initialDate: params.get('date'),
            dayHeaderFormat: {
                weekday: 'short'
            },
            firstDay: 1,
            headerToolbar: {
                left: 'prev',
                center: 'title',
                right: 'next'
            },
            titleFormat: {
                year: 'numeric'
            },
            buttonText: {
                prev: '前の1週間',
                next: '次の1週間',
            },
            dayMaxEvents: true,
            customButtons: {
                prev: {
                    text: '前の月',
                    click: function () {
                        var date = new Date($('input[name="date_week"]').val());
                        var start = toDateString(getMonday(date).addDays(-7));
                        var end = toDateString(getMonday(date).addDays(-1));

                        prev(start, end);
                    }
                },
                next: {
                    text: '次の月',
                    click: function () {
                        var date = new Date($('input[name="date_week"]').val());
                        var start = toDateString(getMonday(date).addDays(7));
                        var end = toDateString(getMonday(date).addDays(13));

                        next(start, end);
                    }
                },
            },
            dayCellClassNames: function(info) {
                var cell_date = new Date(info.date);
                var format_date = cell_date.getFullYear()+'-'+("0" + (cell_date.getMonth()+1)).slice(-2)+'-'+("0" + cell_date.getDate()).slice(-2);
                var indexDate = jsonDateTime.filter(function(item, index){
                    if (item.day == format_date) return item;
                });
                if (indexDate.length > 0) {
                    var className = setCellClassName(indexDate[0].count, indexDate[0].tel);
                } else {
                    var className = 'reserve-status-none';
                }
                if (params.get('date') == format_date) {
                    return [ className, 'reserve-active-day' ]
                } else {
                    return [ className ]
                }

            },
            dayCellContent: function (e) {
                var cell_month = e.date.getMonth() + 1;
                var cell_day = e.date.getDate();
                e.dayNumberText = cell_month+'/'+cell_day;
            },
            dateClick: function (info) {
                if ($(info.dayEl).hasClass('fc-day-past')) return;
                if ($(info.dayEl).hasClass('reserve-status-tel')) {
                    window.location.href = 'tel:' + $('#tel-shop').val();
                    return;
                }
                $('.fc .fc-daygrid-day').removeClass('reserve-active-day');
                $(info.dayEl).addClass('reserve-active-day');
                $('.reservation-time-select').css('display', 'block');
                createReserveTime(info.date);
            },
            fixedWeekCount: false,
            datesSet: function (dateInfo) {
                var today = new Date();
                today.setHours(0);
                today.setMinutes(0);
                today.setSeconds(0);
                today.setMilliseconds(0);
                var viewDate = calendar.view.currentStart;
                // Past
                var minDate = today;
                if (minDate >= viewDate) {
                    $(".fc-prev-button").prop('disabled', true);
                    $(".fc-prev-button").addClass('fc-state-disabled');
                }
                else {
                    $(".fc-prev-button").removeClass('fc-state-disabled');
                    $(".fc-prev-button").prop('disabled', false);
                }

                $('.fc-prev-button').text('前の1週間');
                $('.fc-next-button').text('次の1週間');
            }
        });
        calendar.render();
    }
}

function next (start, end) {
    showLoading();

    $.ajax({
        url: $('#get-data-calendar-month').val(),
        type: 'get',
        data: {
            'shop_id': $('#shop_id').val(),
            'start_date': start,
            'end_date': end
        },

        success: function (res) {
            $('input[name="date_week"]').val(start);
            // set data for calendar Week
            $('#json_date').val(JSON.stringify(res.data.data));
            let date_focus = new Date(start).addDays(2);
            init(res.data.data, date_focus);
            $('.reservation-time-select').css('display', 'none');
            hideLoading();
        },

        error: function (res) {
            hideLoading();
        }
    });
}

function prev (start, end) {
    showLoading();
    $.ajax({
        url: $('#get-data-calendar-month').val(),
        type: 'get',
        data: {
            'shop_id': $('#shop_id').val(),
            'start_date': start,
            'end_date': end
        },

        success: function (res) {
            $('input[name="date_week"]').val(start);
            $('#json_date').val(JSON.stringify(res.data.data));
            // set data for calendar Week
            init(res.data.data, start);
            $('.reservation-time-select').css('display', 'none');
            hideLoading();
        },

        error: function (res) {
            hideLoading();
        }
    });
}

function getMonday(d) {
    d = new Date(d);
    var day = d.getDay(),
        diff = d.getDate() - day + (day == 0 ? -6:1); // adjust when day is sunday
    return new Date(d.setDate(diff));
}
