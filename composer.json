{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "awobaz/compoships": "^2.1", "aws/aws-sdk-php": "^3.211", "davejamesmiller/laravel-breadcrumbs": "^5.3", "doctrine/dbal": "^3.3", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "laravel/framework": "^8.65", "laravel/socialite": "^5.5", "laravel/tinker": "^2.5", "laravelcollective/html": "^6.3", "league/flysystem-aws-s3-v3": "^1.0", "maxbanton/cwh": "^2.0", "prettus/l5-repository": "^2.8"}, "require-dev": {"facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10"}, "autoload": {"classmap": ["database"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}, "files": ["app/Common/HttpCode.php", "app/Common/Common.php", "app/Common/Helper.php"]}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}