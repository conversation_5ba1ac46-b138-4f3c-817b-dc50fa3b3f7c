<?php
use App\Database\Migration\CustomBlueprint as Blueprint;
use App\Database\Migration\Schema;

class AlterShopTable extends \App\Database\Migration\Create
{
    protected $_table = 'shops';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table($this->getTable(), function (Blueprint $table) {
            $table->dropColumn('publish_from_dt');
            $table->timestamp('publish_from_dt_re')->useCurrent()->after('early_reservable_tel_flg')->comment('予約受付開始日時');
        });
    }
}
