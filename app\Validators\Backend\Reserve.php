<?php

namespace App\Validators\Backend;

use App\Repositories\ShopInfoRepository;
use App\Validators\Base\BaseValidator;

/**
 * Class ReserveDenial
 * @package App\Validator
 */
class Reserve extends BaseValidator
{
    /**
     * @param $data
     * @return bool
     */
    public function validate($data)
    {
        $shopInfoRepository = resolve(ShopInfoRepository::class);
        $dataShop = $shopInfoRepository->findById($data['shop_id']);
        $frames = (int)$dataShop->reservation_frame_num;


        $rules = array(
            'shop_id' => 'required',
            'reserve_date' => 'required|date',
            'data.*.reserve_time' => 'required|date_format:H:i|unique_reserve_time',
            'data.*.reserve_num' => 'required|numeric|between:0,' . $frames,
        );

        $messages = [
            'shop_id.required' => '店舗は必須です。',
            'data.*.reserve_num.required' => '予約数は必須です',
            'data.*.reserve_num.between' => '予約数は0〜' . $frames . '整数のみで入力してください。',
            'data.*.reserve_num.numeric' => '予約数はは数値で入力してください。',
            'data.*.reserve_time.required' => '予約時間枠は必須です',
            'data.*.reserve_time.date_format' => '予約日付が不正です。',
            'data.*.reserve_time.unique_reserve_time' => '予約時間枠は存在しています。',
        ];

        return $this->_addRules($rules, $messages)->with($data)->passes();
    }
}
