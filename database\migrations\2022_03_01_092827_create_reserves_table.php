<?php

use App\Database\Migration\CustomBlueprint as Blueprint;
use App\Database\Migration\Schema;

class CreateReservesTable extends \App\Database\Migration\Create
{
    protected $_table = 'reserves';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable($this->getTable())) {
            Schema::create($this->getTable(), function (Blueprint $table) {
                $table->increments('id');
                $table->integer('shop_id')->comment('店舗ID');
                $table->date('reserve_date')->comment('予約日付');
                $table->time('reserve_time')->comment('予約時間枠');
                $table->tinyInteger('reserve_num')->comment('予約数');
                $table->actionBy();
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }
}
