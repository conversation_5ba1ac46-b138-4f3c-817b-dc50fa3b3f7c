<?php

namespace App\Helpers;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\File\Exception\UploadException;

/**
 * Class FileService
 * @package App\Services
 */
class MyStorage
{
    public function __call($name, $arguments)
    {
        return call_user_func_array([Storage::class, $name], $arguments);
    }

    public function path($file)
    {
        return str_replace('/', '\\', storage_path($file));
    }

    /**
     * @param $fileName
     * @param $content
     * @return mixed
     */
    public function uploadToTmp($fileName, $content)
    {
        $this->_validationFile($fileName, $content);
        $newFilePath = getTmpUploadDir(date(getConstant('DEFAULT_DATE_VALUE'))) . DIRECTORY_SEPARATOR . $fileName;
        $this->deleteTmpDaily();
        if ($this->isUploadFile($content)) {
            $r = Storage::putFileAs(getTmpUploadDir(date(getConstant('DEFAULT_DATE_VALUE'))), $content, $fileName, 'public');
            if (!$r) {
                throw new  UploadException(trans('messages.file_upload_failed', ['file' => $newFilePath]));
            }
            return $newFilePath;
        }

        $r = $this->put($newFilePath, $content);
        if (!$r) {
            throw new UploadException(trans('messages.file_upload_failed', ['file' => $newFilePath]));
        }
        return $newFilePath;
    }

    protected function _validationFile($fileName, $content)
    {
        if ($this->isUploadFile($content)) {
            $ext = $content->getClientOriginalExtension();
        } else {
            $ext = array_last(explode('.', $fileName));
        }
        $extBlacklist = (array)getSystemConfig('ext_blacklist', ['php', 'phtml', 'html']);
        if (in_array($ext, $extBlacklist)) {
            throw new UploadException(trans('messages.file_upload_failed', ['file' => $fileName]));
        }
        return true;
    }

    /**
     * @param $fileName
     */
    public function download($fileName)
    {

    }


    /**
     * @param $fileName
     * @return mixed|string
     */
    public function url($fileName)
    {
        if (!$fileName) {
            return '';
        }

        if (Str::contains($fileName, ['http://', 'https://'])) {
            return $fileName;
        }

        $storageDir = $fileName;
        if (getMediaDir() !== getMediaSaveDir()) {
            $mediaDirSave = getSystemConfig('media_save_dir');
            $explode = explode($mediaDirSave, $fileName);
            if (!empty($explode) && strpos($fileName, $mediaDirSave) !== false) {
                unset($explode[0]);
                $storageDir = getMediaDir(implode('', $explode));
            }
        }

        $storageDir = str_replace('\\', '/', $storageDir);
        $storageDir = str_replace('//', '/', $storageDir);
        if (!Storage::exists($storageDir)) {
            return '';
        }

        if (config('filesystems.default') == 'public') {
            return public_url($storageDir);
        }

        // S3
        if (!empty(config('filesystems.disks.s3.url'))) {
            // CloudFront point to staging folder
            // Ex: storageDir: /staging/images/avatar/original/me.jpg => Link: CloudFront/images/avatar/original/me.jpg
            $fileName = str_replace('\\', '/', $fileName);
            $fileName = str_replace('//', '/', $fileName);
            return urldecode(Storage::url($fileName));
        }

        return urldecode(Storage::url($storageDir));
    }

    public function withOutUrl($fileName)
    {
        $url = $this->url('__prefix__');
        $url = str_replace('__prefix__', '', $url);
        $fileName = str_replace($url, '', $fileName);
        return $fileName;
    }


    public function moveFromTmpToMedia($filePath, $newName = '')
    {
        if (!Storage::exists($filePath)) {
            throw new UploadException(trans('messages.file_dose_not_exist', ['file' => $filePath]));
        }
        $newFilePath = getMediaDir($newName ? $newName : $filePath);
        $newFileSavePath = getMediaSaveDir($newName ? $newName : $filePath);
        $nameBackup = $newFilePath . '_' . time();

        if (Storage::exists($newFilePath)) {
            // rename
            Storage::move($newFilePath, $nameBackup);
        }
        try {
            $r = Storage::move($filePath, $newFilePath);
            if (!$r) {
                throw new UploadException(trans('messages.file_upload_failed', ['file' => $filePath]));
            }
            if (Storage::exists($nameBackup)) {
                // rename
                Storage::delete($nameBackup);
            }
            return $newFileSavePath;
        } catch (\Exception $exception) {
            // rollback
            if (Storage::exists($nameBackup)) {
                // rename
                Storage::move($nameBackup, $newFilePath);
            }
            throw $exception;
        }
    }

    public function put($file, $content)
    {
        if (!$this->isUploadFile($content)) {
            $content = $this->base64ToFile($content);
        }
        return Storage::put($file, $content);
    }

    public function base64ToFile($fileData)
    {
        @list($type, $fileData) = explode(';', $fileData);
        @list(, $fileData) = explode(',', $fileData);
        return base64_decode($fileData);
    }

    public function isUploadFile($data)
    {
        return $data instanceof UploadedFile;
    }

    /**
     * @return mixed
     */
    public function deleteTmpDaily()
    {
        for ($i = 1; $i <= 30; $i++) {
            $directory = getTmpUploadDir(today()->subDays($i)->format(getConstant('DEFAULT_DATE_VALUE')));
            Storage::deleteDirectory($directory);
        }
    }

    /**
     * Custom from function uploadToTmp() and moveTmpToMedia()
     * @param string $fileName
     * @param $content
     * @return array
     */
    public function uploadFileToS3($fileName, $content)
    {
        $this->_validationFile($fileName, $content);
        // storage dir
        $newFilePath = getMediaDir($fileName);
        // to save DB
        $newFileSavePath = getMediaSaveDir($fileName);

        if ($this->isUploadFile($content)) {
            $r = Storage::putFileAs(getMediaDir(), $content, $fileName, 'public');
            if (!$r) {
                return [
                    'status' => false,
                    'filename' => '',
                ];
            }

            return [
                'status' => true,
                'filename' => $newFileSavePath,
            ];
        }

        $r = $this->put($newFilePath, $content);
        if (!$r) {
            return [
                'status' => false,
                'filename' => '',
            ];
        }

        return [
            'status' => true,
            'filename' => $newFileSavePath,
        ];
    }

    /**
     * @param $fileName
     * @return mixed|string
     */
    public function getFilePathFromS3($fileName) {
        return $this->url($fileName);
    }
}