<?php

namespace App\Validators\Base;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Lang;
use \Prettus\Validator\LaravelValidator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Arr;
/**
 * Class BaseValidator
 * @package App\Validator\Base
 */
class BaseValidator extends LaravelValidator
{
    /**
     *
     */
    const RULE_CREATE = 'create';
    /**
     *
     */
    const RULE_UPDATE = 'update';
    /**
     *
     */
    const RULE_SHOW = 'show';
    /**
     *
     */
    const RULE_DESTROY = 'destroy';
    /**
     *
     */
    const RULE_SEARCH = 'search';

    /**
     *
     */
    const IMAGE_EXTENSION = 'img';

    /**
     *
     */
    const CSV_EXTENSION = 'csv';

    /**
     * @var null
     */
    protected $_model = null;
    /**
     * @var array
     */
    protected $rules = [];
    /**
     * @var array
     */
    protected $messages = [];
    /**
     * @var array
     */
    protected $_data = [];

    public function with(array $data)
    {
        $this->data = array_replace_recursive($this->data, $data, $this->_data);

        return $this;
    }

    /**
     * @param $data
     * @return bool
     */
    public function validateCreate($data)
    {
        $this->beforeValidateCreate($data);
        return $this->with($data)->passes(self::RULE_CREATE);
    }

    /**
     * @param $data
     * @return bool
     */
    public function validateUpdate($data)
    {
        $this->beforeValidateUpdate($data);
        return $this->with($data)->passes(self::RULE_UPDATE);
    }

    /**
     * @param $data
     * @return bool
     */
    public function validateShow($data)
    {
        $this->beforeValidateShow($data);
        return $this->with($data)->passes(self::RULE_SHOW);
    }

    /**
     * @param $data
     * @return bool
     */
    public function validateDestroy($data)
    {
        $this->beforeValidateDestroy($data);
        return $this->with($data)->passes(self::RULE_DESTROY);
    }

    /**
     * @param $data
     * @return bool
     */
    public function validateSearch($data)
    {
        $this->beforeValidateSearch($data);
        return $this->with($data)->passes(self::RULE_SEARCH);
    }

    /**
     * @param $data
     * @return bool
     */
    public function validateOther($data)
    {
        $rules = [
            'id' => 'required|max:1'
        ];
        $message = [];
        return $this->_addRules($rules, $message)->with($data)->passes();
    }

    /**
     * @param array $config
     * @return string
     */
    public function _fileRule($config)
    {
        $rule = '';
        $minSize = Arr::get($config, 'size.min');
        $maxSize = Arr::get($config, 'size.max');
        if($minSize){
            $minSize = 1024 * 1024 * $minSize;
            $rule .= 'min:' . $minSize .'|';
        }

        if($maxSize){
            $maxSize = 1024 * 1024 * $maxSize;
            $rule .= 'max:' . $maxSize .'|';
        }

        $ext = Arr::get($config, 'ext', []);
        if(!empty($ext)){
            $rule .= 'mimes:' . implode(',', $ext);
        }

        return $rule;
    }

    /**
     * @return array
     */
    protected function _buildCreateRules()
    {
        return ['rules' => $this->_getRulesDefault(), 'messages' => $this->_getMessagesDefault()];
    }

    protected function _buildRulesWithPrimaryKey()
    {
        $rules = [];
        foreach ((array)$this->getModel()->getKeyName() as $key) {
            $rules[$key] = 'required';
        }
        if (isset($key)) {
            $rules[$key] .= $this->_getExistInDbRule();
        }
        return $rules;
    }

    protected function _getMessagePrimaryKeyExist()
    {
        $keys = (array)$this->getModel()->getKeyName();
        return [end($keys) . '.custom_exists' => trans('messages.data_does_not_exist')];
    }

    /**
     * @return array
     */
    protected function _buildUpdateRules()
    {
        return ['rules' => array_merge($this->_buildRulesWithPrimaryKey(), $this->_getRulesDefault()), 'messages' => $this->_getMessagesDefault()];
    }

    /**
     * @return array
     */
    protected function _buildShowRules()
    {
        return [
            'rules' => $this->_buildRules($this->_buildRulesWithPrimaryKey(), false),
            'messages' => $this->_getMessagePrimaryKeyExist(),
        ];
    }

    /**
     * @return array
     */
    protected function _buildDestroyRules()
    {
        return [
            'rules' => $this->_buildRules(
                $this->_buildRulesWithPrimaryKey(),
                false),
            'messages' => $this->_getMessagePrimaryKeyExist(),
        ];
    }

    /**
     * @return array
     */
    protected function _buildSearchRules()
    {
        return ['rules' => [], 'messages' => []];
    }

    /**
     * @param $data
     */
    public function beforeValidateCreate(&$data)
    {
        return $this->_build(self::RULE_CREATE, $data);
    }

    /**
     * @param $data
     */
    public function beforeValidateUpdate(&$data)
    {
        return $this->_build(self::RULE_UPDATE, $data);
    }

    protected function _getPrimaryKeyString()
    {
        $keys = (array)$this->getModel()->getKeyName();
        return implode('_', $keys);
    }

    /**
     * @param $data
     */
    public function beforeValidateShow(&$data)
    {
        if (!is_array($data)) {
            $data = array($this->_getPrimaryKeyString() => $data);
        }
        return $this->_build(self::RULE_SHOW, $data);
    }

    /**
     * @param $data
     */
    public function beforeValidateDestroy(&$data)
    {
        if (!is_array($data)) {
            $data = array($this->_getPrimaryKeyString() => $data);
        }
        return $this->_build(self::RULE_DESTROY, $data);
    }

    /**
     * @param $data
     */
    public function beforeValidateSearch(&$data)
    {
        return $this->_build(self::RULE_SEARCH, $data);
    }

    /**
     * @return array
     */
    protected function _getRulesDefault()
    {
        return array();
    }

    /**
     * @return array
     */
    protected function _getMessagesDefault()
    {
        return array();
    }

    /**
     * @param array $rules
     * @param bool $mergeDefault
     * @return array
     */
    protected function _buildRules($rules = array(), $mergeDefault = true)
    {
        return $mergeDefault ? array_merge($this->_getRulesDefault(), $rules) : $rules;
    }

    /**
     * @param array $messages
     * @param bool $mergerDefault
     */
    protected function _setMessages($messages = array(), $mergerDefault = true)
    {
        $messagesX = $this->messages;
        if ($mergerDefault) {
            $messagesX = array_merge($this->messages, $this->_getMessagesDefault());
        }
        $this->messages = array_merge($messagesX, $messages);
    }

    /**
     * @param null $action
     * @return bool
     */
    public function passes($action = null)
    {
        $this->setData($this->data);
        $rules = $action ? $this->getRules($action) : $this->rules;
        $validator = $this->validator->make($this->data, $rules, $this->messages)->setAttributeNames($this->_getAttributeNames());

        $beforeMethod = '_beforeValidate' . ucfirst($action);
        if (method_exists($this, $beforeMethod)) {
            $this->{$beforeMethod}($validator);
        } elseif (method_exists($this, '_before'.ucfirst($action))){
            $this->{'_before'.ucfirst($action)}($validator);
        }

        $fails = $validator->fails();

        $afterMethod = '_afterValidate' . ucfirst($action);
        if (method_exists($this, $afterMethod)) {
            $this->{$afterMethod}($validator);
        }elseif (method_exists($this, '_after'.ucfirst($action))){
            $this->{'_after'.ucfirst($action)}($validator);
        }
        if ($fails || !empty($validator->errors()->messages())) {
            $this->errors = $validator->messages();
            return false;
        }

        return true;
    }

    protected function _getAttributeNames()
    {
        return (array)Lang::get('models.' . $this->getModel()->getAlias() . '.attributes');
    }

    /**
     * @return null
     */
    public function getModel()
    {
        return $this->_model;
    }

    /**
     * @param null $model
     * @return  $this;
     */
    public function setModel($model)
    {
        $this->_model = $model;
        return $this;
    }

    /**
     * @param $type
     */
    protected function _build($type, $data = [])
    {
        $this->setData($data);
        $r = array();
        switch ($type) {
            case self::RULE_CREATE :
                $r = $this->_buildCreateRules();
                break;
            case self::RULE_UPDATE :
                $r = $this->_buildUpdateRules();
                break;
            case self::RULE_SHOW :
                $r = $this->_buildShowRules();
                break;
            case self::RULE_DESTROY :
                $r = $this->_buildDestroyRules();
                break;
            case self::RULE_SEARCH:
                $r = $this->_buildSearchRules();
                break;
        }
        $this->rules[$type] = isset($r['rules']) ? (array)$r['rules'] : array();
        $this->_setMessages(isset($r['messages']) ? (array)$r['messages'] : array());
    }

    /**
     * @param string $modelClass
     * @param array $fields
     * @return string
     */
    protected function _getExistInDbRule($modelClass = '', $fields = array())
    {
        $modelClass = $modelClass ? $modelClass : get_class($this->getModel()->getModel());
        $fieldStr = '';
        foreach ($fields as $field => $value) {
            if(is_string($field)){
                $value = (array)$value;
                $fieldStr .= ',' . $field . ',' . implode('*_*', $value);
                continue;
            }
            $valueFromParams = (array)$this->getData($value);
            $fieldStr .= ',' . $value . ',' . implode('*_*', $valueFromParams);
        }
        return '|custom_exists:' . $modelClass. $fieldStr;
    }

    protected function _implode($prefix, $params)
    {
        return join($prefix, array_map(function ($value) {
            return $value === null ? 'NULL' : $value;
        }, $params));
    }

    /**
     * @param string $modelClass
     * @param array $fields
     * @return string
     */
    protected function _getUniqueInDbRule($modelClass = '', $fields = array())
    {
        $modelClass = $modelClass ? $modelClass : get_class($this->getModel()->getModel());
        $fieldStr = '';
        foreach ($fields as $field => $value) {
            if(is_string($field)){
                $value = (array)$value;
                $fieldStr .= ',' . $field . ',' . implode('*_*', $value);
                continue;
            }
            $valueFromParams = (array)$this->getData($value);
            $fieldStr .= ',' . $value . ',' . implode('*_*', $valueFromParams);
        }
        return '|custom_unique:' . $modelClass . $fieldStr;
    }

    /**
     * @param $array
     * @return string
     */
    protected function _getInArrayRule($array)
    {
        return '|in:' . $this->_implode(',', $array);
    }

    /**
     * @return string
     */
    protected function _getSmallIntegerRule()
    {
        return '|integer|max:32767';
    }

    protected function _addDeleteScope($fields = [])
    {
        if ($field = getDelFlagColumn()) {
            $fields[] = $field;
            $fields[] = getDelFlagColumn('active');
            return $fields;
        }
        if ($field = getDeletedAtColumn()) {
            $fields[] = $field;
            $fields[] = null;
            return $fields;
        }
    }

    /**
     * @param $key
     * @param $default
     * @return array
     */
    public function getData($key = null, $default = null)
    {
        if ($key) {
            return Arr::get($this->_data, $key, $default);
        }
        return $this->_data;

    }

    /**
     * @param array $data
     */
    public function setData($data)
    {
        $this->_data = array_replace_recursive($this->_data, $data);
    }

    /**
     * @param $key
     * @return bool
     */
    public function has($key)
    {
        $data = $this->getData();
        return isset($data[$key]);
    }

    /**
     * @param array $rules
     * @param array $messages
     * @return $this
     */
    protected function _addRules($rules = array(), $messages = array())
    {
        $this->rules += $rules;
        $this->_setMessages($messages);
        return $this;
    }

    protected $_otherData = [];

    /**
     * @return array
     */
    public function getOtherData($key = null, $default = null)
    {
        if ($key) {
            return isset($this->_otherData[$key]) ? $this->_otherData[$key] : $default;
        }
        return $this->_otherData;

    }

    /**
     * @param array $otherData
     * @return $this;
     */
    public function setOtherData($otherData)
    {
        $this->_otherData += $otherData;
        return $this;
    }

    public function _getFileAsUploaded($url)
    {
        $info = pathinfo($url);
        // for cloud ex: s3 => have to get file from s3 to local
        // create a new tmp file for validate
        $tmpFile = getTmpUploadPath(date('Y-m-d').DIRECTORY_SEPARATOR.'tmp_file_' . randomString() . '_' . data_get($info, 'basename'));
        // need to check permission
        file_put_contents($tmpFile, Storage::get($url));
        $info = pathinfo($url);
        return new UploadedFile($tmpFile, $info['basename'], null, null, true);
    }

    protected function _hasFileUpload($field)
    {
        $file = $this->getData($field);
        if ($file instanceof UploadedFile) {
            return true;
        }

        // check in tmp
        if (!is_string($file)) {
            return false;
        }

        $tmpPath = getTmpUploadDir();
        if (!str_contains($file, $tmpPath)) {
            return false;
        }

        if (!Storage::exists($file)) {
            return false;
        }

        $fileUploaded = $this->_getFileAsUploaded($file);
        $data = $this->getData();
        data_set($data, $field, $fileUploaded);
        $this->setData($data);
        return true;
    }

    protected function _toMb($value)
    {
        return $value * 1024;
    }

    protected function _afterValidateCreate($validator)
    {

    }

    protected function _beforeValidateCreate($validator)
    {

    }

    protected function _afterValidateUpdate($validator)
    {

    }

    protected function _beforeValidateUpdate($validator)
    {

    }

    protected function _afterValidateShow($validator)
    {

    }

    protected function _beforeValidateShow($validator)
    {

    }

    protected function _afterValidateDestroy($validator)
    {

    }

    protected function _beforeValidateDestroy($validator)
    {

    }

}
