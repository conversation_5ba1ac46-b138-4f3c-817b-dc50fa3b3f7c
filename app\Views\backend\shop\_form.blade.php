@php
    $isEdit = !empty($entity->getKey());
    $editClass = !empty($isEdit) ? 'edit' : null;
@endphp
<!-- start::Post -->
<div class="post">
    <!-- start::Form -->
    {!! MyForm::model($entity, ['route' => ['shop.valid', $entity->getKey()], 'autocomplete' => 'off', 'class' => 'form form-primary']) !!}
    {!! MyForm::setForeShowError(false) !!}
    <div class="page-title flex align-items-center">
        <div class="mr-auto flex align-items-center">
            <h1 class="heading-1">店舗基本情報</h1>
        </div>
    </div>
    <div class="form-block">
        <div class="form-group">
            <div class="form-label">店舗名<span class="form-required">*</span></div>
            <div class="form-field">
                {!! MyForm::text('shop_name', data_get($entity, 'shop_name'), ['class' => 'form-control form-size-w380', 'autocomplete' => 'off', 'defaultClass' => false] ) !!}
                {!! MyForm::error('shop_name') !!}
            </div>
        </div>
        <div class="form-group">
            <div class="form-label">来店予約URL<span class="form-required">*</span></div>
            <div class="form-field">
                {!! MyForm::text('reserve_form_url', data_get($entity, 'reserve_form_url'), ['class' => 'form-control form-size-w520', 'autocomplete' => 'off', 'defaultClass' => false] ) !!}
                {!! MyForm::error('reserve_form_url') !!}
            </div>
        </div>
        <div class="form-group">
            <div class="form-label">住所<span class="form-required">*</span></div>
            <div class="form-field">
                {!! MyForm::text('address', data_get($entity, 'address'), ['class' => 'form-control form-size-w520', 'autocomplete' => 'off', 'defaultClass' => false] ) !!}
                {!! MyForm::error('address') !!}
            </div>
        </div>
        <div class="form-group">
            <div class="form-label">電話番号<span class="form-required">*</span></div>
            <div class="form-field">
                {!! MyForm::text('tel', data_get($entity, 'tel'), ['class' => 'form-control form-size-w520', 'autocomplete' => 'off', 'defaultClass' => false] ) !!}
                {!! MyForm::error('tel') !!}
            </div>
        </div>
        <div class="form-group">
            <div class="form-label">管理者メール<span class="form-required">*</span></div>
            <div class="form-field">
                {!! MyForm::text('shop_user_email', data_get($entity, 'shop_user_email'), ['class' => 'form-control form-size-w520', 'autocomplete' => 'off', 'defaultClass' => false] ) !!}
                {!! MyForm::error('shop_user_email') !!}
            </div>
        </div>
        <div class="form-group">
            @if(!empty($isEdit))
                <div class="form-label">パスワード変更<span class="form-required"></span></div>
                <div class="form-field flex flex-wrap align-items-center form-field-password">
                    <div class="form-change flex">
                        {!! MyForm::password('shop_user_pwd_current', ['class' => 'form-control form-size-w220', 'id' => 'shop_user_pwd_current', 'placeholder' => '現状のパスワード','autocomplete' => 'off', 'defaultClass' => false]) !!}
                        {!! MyForm::password('shop_user_pwd', ['class' => 'form-control form-size-w220', 'id' => 'shop_user_pwd' ,'placeholder' => '新しいパスワード', 'autocomplete' => 'off', 'defaultClass' => false]) !!}
                        {!! MyForm::password('shop_user_pwd_confirm', ['class' => 'form-control form-size-w220', 'id' => 'shop_user_pwd_confirm' ,'autocomplete' => 'off', 'placeholder' => 'パスワード再入力', 'defaultClass' => false]) !!}
                        <button type="button" name="" class="btn btn-primary" id="submit-change">変更</button>
                    </div>
                    {!! MyForm::error('shop_user_pwd_current') !!}
                    {!! MyForm::error('check_current_password') !!}
                    {!! MyForm::error('shop_user_pwd') !!}
                    {!! MyForm::error('shop_user_pwd_confirm') !!}
                </div>
            @else
                <div class="form-label">パスワード<span class="form-required">*</span></div>
                <div class="form-field flex flex-wrap align-items-center form-field-password">
                    {!! MyForm::password('shop_user_pwd', ['class' => 'form-control form-size-w520', 'autocomplete' => 'off', 'defaultClass' => false] ) !!}
                    {!! MyForm::error('shop_user_pwd') !!}
                </div>
            @endif
        </div>
        @if(!empty($isEdit))
            <div class="form-group">
                <div class="form-label">設置用JS</div>
                <div class="form-field">
                    <div class="form-key-js" readonly>
                        <pre><code>&lt;script type="text/javascript" src="{{ route('script_month') }}?s={{ $entity->shop_key }}"&gt;&lt;/script&gt;</code></pre>
                    </div>
                </div>
            </div>
        @endif
    </div>
    <div class="page-title flex align-items-center mt-5">
        <div class="mr-auto flex align-items-center">
            <h1 class="heading-1">定休日と営業時間の設定<span class="form-required">*</span></h1>
        </div>
    </div>
    <div class="form-block">
        @foreach(getConfig('week_day') as $key => $value)
            <div class="form-group">
                <div class="form-label">{{ data_get(getConfig('week_type'), $key) }}</div>
                <div class="form-field form-field-weekly form-disabled-control-field">
                    <div class="form-change flex">
                        <div class="flex align-items-center">
                            <div class="form-select">
                                @php $operationFrom = \Carbon\Carbon::parse(data_get($entity, $value.'_operation_from'))->format('H:i'); @endphp
                                {!! MyForm::dropDown(
                                             $value.'_operation_from',
                                             $operationFrom,
                                             getConfig('operation'),
                                             true,
                                             ['defaultClass' => false, 'class' => 'form-control' . ' ' .$editClass])
                                         !!}
                            </div>
                            <div class="mx-3">〜</div>
                            <div class="form-select">
                                @php $operationTo = \Carbon\Carbon::parse(data_get($entity, $value.'_operation_to'))->format('H:i'); @endphp
                                {!! MyForm::dropDown(
                                             $value.'_operation_to',
                                             $operationTo,
                                             getConfig('operation'),
                                             true,
                                             ['defaultClass' => false, 'class' => 'form-control' . ' ' . $editClass])
                                         !!}
                            </div>
                        </div>
                        <div class="checkbox-styled {{$editClass}}">
                            <div class="item">
                                {!! MyForm::checkbox($value.'_closing_flg', true, data_get($entity, $value.'_closing_flg'), ['checkboxPrefix' => false, 'defaultClass' => false, 'id' => $value.'_closing_flg', 'class' => 'checkbox_shop']) !!}
                                <label for="{{ $value.'_closing_flg' }}">定休日</label>
                            </div>
                        </div>
                        @if(!empty($isEdit))
                            <span style="margin-top: 11px; margin-left: 55px; color: #a94442">*一度決定すると変更できません。</span>
                        @endif
                    </div>
                    {!! MyForm::error($value.'_operation_from') !!}
                    {!! MyForm::error($value.'_operation_to') !!}
                </div>
            </div>
        @endforeach
        <div class="form-group">
            <div class="form-label">インターバル</div>
            <div class="form-field form-field-interval">
                <div class="form-change flex">
                    <div class="form-select">
                        {!! MyForm::dropDown(
                                     'interval_min',
                                     data_get($entity, 'interval_min'),
                                     getConfig('interval_min'),
                                     ['' => ''],
                                     ['defaultClass' => false, 'class' => 'form-control' . ' ' . $editClass])
                                 !!}
                    </div>
                    @if(!empty($isEdit))
                        <span style="margin-top: 11px; margin-left: 30px; color: #a94442">*一度決定すると変更できません。</span>
                    @endif
                </div>
                {!! MyForm::error('interval_min') !!}
            </div>
        </div>
        <div class="form-group">
            <div class="form-label">表示開始日時</div>
            <div class="form-field form-field-startdate">
                <div class="form-change flex">
                    <div class="form-select">
                        {!! MyForm::dropDown(
                                     'reservable_period_days',
                                     data_get($entity, 'reservable_period_days'),
                                     getReservePeriodDay(),
                                     ['' => ''],
                                     ['defaultClass' => false, 'class' => 'form-control'])
                                 !!}
                    </div>
                    <div class="flex align-items-center">
                        <div class="checkbox-styled-tel">
                            <div class="item-tel">
                                {!! MyForm::checkbox('early_reservable_tel_flg', true, data_get($entity, 'early_reservable_tel_flg'), ['checkboxPrefix' => false ,'defaultClass' => false, 'id' => 'early_reservable_tel_flg', 'class' => 'checkbox_shop', 'checked' => empty($isEdit) ? true : false]) !!}
                                <label for="early_reservable_tel_flg">受付開始前はTEL</label>
                            </div>
                        </div>
                    </div>
                </div>
                {!! MyForm::error('reservable_period_days') !!}
            </div>
        </div>
        <div class="form-group">
            <div class="form-label">受付期間</div>
            <div class="form-field form-field-reception">
                <div class="form-change flex">
                    <div class="flex align-items-center">
                        <div class="form-datepicker mr-3">
                            {!! MyForm::text('publish_from_dt_date', !empty(data_get($entity, 'publish_from_dt_date')) ? data_get($entity, 'publish_from_dt_date') : $publishFromDate , ['class' => 'form-control form-control-datepicker datepicker form-size-w200', 'autocomplete' => 'off', 'defaultClass' => false] ) !!}
                        </div>
                        <div class="form-select">
                            {!! MyForm::dropDown(
                                         'publish_from_dt_time',
                                         !empty(data_get($entity, 'publish_from_dt_time')) ? data_get($entity, 'publish_from_dt_time') : $publishFromTime,
                                         getConfig('operation'),
                                         true,
                                         ['defaultClass' => false, 'class' => 'form-control'])
                                     !!}
                        </div>
                    </div>
                    <div class="mx-3" style="padding-top: 11px">〜</div>
                    <div class="form-datepicker mr-3">
                        {!! MyForm::text('publish_to_dt_date', !empty(data_get($entity, 'publish_to_dt_date')) ? data_get($entity, 'publish_to_dt_date') : $publishToDate, ['class' => 'form-control form-control-datepicker datepicker form-size-w200', 'autocomplete' => 'off', 'defaultClass' => false]) !!}
                    </div>
                    <div class="form-select">
                        {!! MyForm::dropDown(
                                     'publish_to_dt_time',
                                     !empty(data_get($entity, 'publish_to_dt_time')) ? data_get($entity, 'publish_to_dt_time') : $publishToTime,
                                     getConfig('operation'),
                                     true,
                                     ['defaultClass' => false, 'class' => 'form-control'])
                                 !!}
                    </div>
                </div>
                {!! MyForm::error('publish_from_dt') !!}
                {!! MyForm::error('publish_to_dt') !!}
            </div>
        </div>
        <div class="form-group">
            <div class="form-label">ルーム数</div>
            <div class="form-field form-field-interval">
                {!! MyForm::text('reservation_frame_num', data_get($entity, 'reservation_frame_num'), ['class' => 'form-control form-size-w140', 'autocomplete' => 'off', 'defaultClass' => false] ) !!}
                {!! MyForm::error('reservation_frame_num') !!}
            </div>
        </div>
        @if(!empty($isEdit))
             <div class="form-group">
                <div class="form-field">
                    <input type="hidden" id="change_password" value="{{ route('shop.change_pass', ['shop' => $entity->getKey()]) }}">
                </div>
             </div>
        @endif
        <div class="form-submit-buttons flex align-items-center mt-4 mt-sm-5">
            <button name="Submit" type="Submit" class="btn btn-primary btn-submit">保存</button>
        </div>
    </div>
{!! MyForm::close() !!}
<!-- end::Form -->
</div>
<!-- end::Post -->
