if (window.jQuery) {
    if (typeof $.fn.jquery !== 'undefined') {
        $(function() {
            var rem_ua = navigator.userAgent;
            if (rem_ua.search(/Android 4/) !== -1) {
                if ($('meta [name=viewport]').length == 0) {
                    $('head').append($(''));
                }
            }
        });
    }
} else {
    document.write('<script src="{{ public_url('js/frontend/vendor/jquery.min.js') }}"></script>');
}
document.open();
document.write(`
<style>.text-error {color: #a94442; display: none}</style>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link media="all" type="text/css" rel="stylesheet" href="{{ public_url('css/frontend/fonts.css') }}">
<link media="all" type="text/css" rel="stylesheet" href="{{ public_url('css/frontend/vendor/jquery-ui.min.css') }}">
<link media="all" type="text/css" rel="stylesheet" href="{{ public_url('css/frontend/vendor/md-preloader.min.css') }}">
<link media="all" type="text/css" rel="stylesheet" href="{{ public_url('css/frontend/style.css') }}">
<link media="all" type="text/css" rel="stylesheet" href="{{ public_url('css/frontend/vendor/fullcalendar/main.min.css') }}">
<link media="all" type="text/css" rel="stylesheet" href="{{ public_url('css/frontend/vendor/spacing.css') }}">
<div class="main">
    <div class="page">
        <div class="wrapper">
            <div class="content">
                <div class="container">
                    <div class="post">
                        <div class="page-title"><h1 class="heading-1">見学予約スケジュール</h1></div>
                        <div id="calender-form" class="reservation-calender-form"><p class="form-message">
                                見学をご希望される日程をお選びください。</p>
                            <div class="reservation-calender reservation-calender--month">
                                <div id="calendar"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" id="json_date" data-data="" value='{!! json_encode($arrDateTime) !!}'>
    <input type="hidden" id="time_range" data-data="" value='{!! json_encode($timeRange) !!}'>
    <input type="hidden" id="get-data-calendar-month" value="{{ route('getDataCalendarMonth') }}">
    <input type="hidden" id="now" value="{{ date('Y-m-d') }}">
    <input type="hidden" id="start_date" value="{{ $start_date }}">
    <input type="hidden" id="end_date" value="{{ $end_date }}">
    <input type="hidden" id="shop_id" value="{{ $dataShop->id  }}">
    <input type="hidden" id="route-calendar-week" value="{{ route('script_week') }}">
    <input type="hidden" id="shop-key-of-script" value="{{ request('s') }}">
    <input type="hidden" id="script_week_url" value="{{ public_url('js/frontend/custom/script_week.js') }}">
    <input type="hidden" id="reserve_form_url" value="{{ $dataShop->reserve_form_url }}">
    <input type="hidden" id="shop_name" value="{{ $dataShop->shop_name }}">
    <input type="hidden" id="tel-shop" value="{{ $dataShop->tel }}">
</div>

<script src="{{ public_url('js/frontend/vendor/fullcalendar/main.min.js') }}"></script>
<script src="{{ public_url('js/frontend/vendor/fullcalendar/locales-all.min.js') }}"></script>
<script src="{{ public_url('js/frontend/vendor/fullcalendar/ja.js') }}"></script>
<script src="{{ public_url('js/frontend/vendor/utils/common.js') }}"></script>
<script src="{{ public_url('js/frontend/vendor/utils/loadingoverlay.min.js') }}"></script>
<script src="{{ public_url('js/frontend/custom/script_month.js') }}"></script>
`)
document.close();
