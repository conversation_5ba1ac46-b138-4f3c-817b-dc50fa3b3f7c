<FilesMatch "(composer\.json)|(package.json)|\.(env|htaccess|example|gitignore|lock|md)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>
# Deny access to sub directory
RewriteRule ^(app|config|bootstrap|database|resources|routes|storage|tests|vendor)\/.*$ - [F]

<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews
    </IfModule>

    RewriteEngine On

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)/$ /$1 [L,R=301]

    # Handle Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_URI} !^.*\.(jpg|css|js|gif|png|ico)$ [NC]
    RewriteRule ^ index.php [L]

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
</IfModule>
