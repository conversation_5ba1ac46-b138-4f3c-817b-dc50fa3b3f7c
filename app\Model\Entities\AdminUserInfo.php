<?php

namespace App\Model\Entities;

use App\Model\Base\Auth\User;
use Illuminate\Notifications\Notifiable;

class AdminUserInfo extends User
{
    protected $table = "admin_users";
    use Notifiable;
    use \App\Model\Presenters\AdminUserInfo;
    protected $_alias = 'admin_user_info';

    protected $hidden = ['password'];

    protected $primaryKeyAutoIncrement = 'id';

    protected $sequence = 'admin_users_id_seq';

    public function setLoginPasswordAttribute($value)
    {
        $this->attributes['password'] = genPassword($value);
    }

    public function getAuthPassword()
    {
        return $this->password;
    }

    public function setAuthPassword($password)
    {
        return $this->setLoginPasswordAttribute($password);
    }

    public function getRememberToken()
    {
        return null; // not supported
    }

    public function setRememberToken($value)
    {
        // not supported
    }


    public function setGroupsAttribute($value)
    {
        $this->attributes['groups'] = $this->getGroups(false, $value);
    }
}

