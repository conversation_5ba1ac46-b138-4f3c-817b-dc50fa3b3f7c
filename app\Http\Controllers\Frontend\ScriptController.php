<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Base\FrontendController;
use App\Repositories\ShopInfoRepository;
use App\Services\Backend\CalendarService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\View;

class ScriptController extends FrontendController
{
    private $calendarService;
    protected $shopInfoRepository;

    public function __construct(CalendarService $calendarService,
                                ShopInfoRepository $shopInfoRepository)
    {
        $this->calendarService = $calendarService;
        $this->shopInfoRepository = $shopInfoRepository;
    }

    public function getScriptMonth()
    {
        $shopId = $this->shopInfoRepository->getShopIdByShopKey(\request('s'));
        if (empty($shopId)) {
            abort(404);
        }

        $dataShop = $this->shopInfoRepository->findById($shopId);

        // start/end date calendar
        $req['start_date'] = Carbon::now()->startOfMonth()->startOfWeek()->format('Y-m-d');
        $req['end_date'] = Carbon::now()->endOfMonth()->endOfWeek()->format('Y-m-d');
        $req['shop_id'] = $shopId;

        $data = $this->calendarService->getDateCalendarMonth($req);

        $content = View::make('frontend.script.script_month')->with([
            'arrDateTime' => $data['arrayDatetime'],
            'start_date' => $req['start_date'],
            'end_date' => $req['end_date'],
            'timeRange' => $data['timeRange'],
            'dataShop' => $dataShop
        ])->render();

        $response = Response::make($content);
        $response->header('Content-Type', 'application/javascript');

        return $response;
    }

    public function getScriptWeek()
    {
        $date = request()->get('date');
        $content = View::make('frontend.script.script_week', compact('date'))->render();
        return successResponse(['data' => $content], 'success');
    }

    public function viewScript()
    {
        $content = View::make('frontend.script.view_script');
        $response = Response::make($content);
        return $response;
    }

    public function getDataCalendarMonth(Request $request)
    {
        $req = $request->only('shop_id', 'start_date', 'end_date');
        $data = $this->calendarService->getDateCalendarMonth($req);

        return successResponse(['data' => $data['arrayDatetime']], 'success');
    }
}

