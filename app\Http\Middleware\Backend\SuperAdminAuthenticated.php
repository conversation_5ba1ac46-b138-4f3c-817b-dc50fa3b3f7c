<?php

namespace App\Http\Middleware\Backend;

use App\Http\Middleware\Authenticated;
use Closure;
use Illuminate\Support\Facades\Auth;

/**
 * Class SuperAdminAuthenticated
 * @package App\Http\Middleware
 */
class SuperAdminAuthenticated extends Authenticated
{
    /**
     *
     */
    public function init()
    {
        $this->setGuard(backendGuard());
    }


    /**
     * @param $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    protected function _toLogin($request)
    {
        if (!backendGuard()->user()->isSuperAdmin()) {
            return parent::_toLogin($request)->with('error', trans('auth.permission_denied')); // TODO: Change the autogenerated stub
        }
        return parent::_toLogin($request);
    }
}
